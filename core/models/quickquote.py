from typing import Optional
from urllib.parse import urljoin
from datetime import datetime
from django.db import models
from django.db.models.query import QuerySet
from django_countries.fields import CountryField
from django.conf import settings
from django.urls import reverse
from django.utils.translation import gettext_lazy as _

from core.models import Customer, EmailType, NofenceCompany
from core.models.models import NOFENCE_UK, User
from core import fields as own_fields

from .segmentation_status import SegmentationStatus
from .base import BaseHistoryModel

NOFENCE_NAMES = {
    "NO": "Nofence AS",
    "ES": "Nofence SL",
    "GB": "Nofence UK Ltd",
    "US": "Nofence US",
}


class QuickQuote(BaseHistoryModel):
    class Status(models.TextChoices):
        STARTED = "STARTED"
        COMPLETED = "COMPLETED"

    class GrazingMethod(models.TextChoices):
        EXTENSIVE = "EXTENSIVE"
        MANAGED = "MANAGED"

    class MobileCoverage(models.TextChoices):
        UNKNOWN = "UNKNOWN"
        BAD = "BAD"
        MEDIUM = "MEDIUM"
        GOOD = "GOOD"

    class BusinessType(models.TextChoices):
        BUSINESS = "BUSINESS"
        CHARITY = "CHARITY"
        PRIVATE = "PRIVATE"

    class QualificationStatus(models.TextChoices):
        LIKELY_QUALIFIED = "LIKELY_QUALIFIED"
        QUALIFICATION_UNKNOWN = "QUALIFICATION_UNKNOWN"
        UNQUALIFIED = "UNQUALIFIED"

    class Source(models.TextChoices):
        PRICE_CALCULATOR = "PRICE_CALCULATOR"
        QUICK_QUOTE = "QUICK_QUOTE"
        REQUEST_ACCOUNT = "REQUEST_ACCOUNT"

    SegmentationStatus = SegmentationStatus

    session = models.CharField(max_length=255)

    country = CountryField()

    browser_timezone = models.CharField(
        max_length=300,
        null=True,
        blank=True,
        help_text="Timezone of the user (auto detected from the browser)",
    )

    first_name = models.CharField(max_length=125, null=True, blank=True)
    last_name = models.CharField(max_length=125, null=True, blank=True)
    email = own_fields.EmailField(null=True, blank=True)
    phone_number = models.CharField(max_length=50, null=True, blank=True)
    sms_consent = models.BooleanField(default=False, null=True, blank=True)

    company_name = models.CharField(max_length=255, null=True, blank=True)
    business_type = models.CharField(max_length=50, choices=BusinessType.choices, null=True, blank=True)

    number_of_animals = models.IntegerField(null=True, blank=True)

    grazing_method = models.CharField(max_length=50, choices=GrazingMethod.choices, null=True, blank=True)
    pasture_types: QuerySet["QuickQuotePasture"]

    mobile_coverage = models.CharField(max_length=50, choices=MobileCoverage.choices, null=True, blank=True)

    status = models.CharField(max_length=50, choices=Status.choices, default=Status.STARTED)
    hubspot_contact_id = models.CharField(max_length=255, null=True, blank=True)
    hubspot_company_id = models.CharField(max_length=255, null=True, blank=True)
    hubspot_deal_id = models.CharField(max_length=255, null=True, blank=True)
    hubspot_deal_line_id = models.CharField(max_length=255, null=True, blank=True)

    source = models.CharField(max_length=50, choices=Source.choices, default=Source.QUICK_QUOTE)

    qualification_status = models.CharField(max_length=50, choices=QualificationStatus.choices, null=True, blank=True)
    completed_at = models.DateTimeField(null=True, blank=True)
    auto_qualified = models.BooleanField(null=True, blank=True)
    auto_qualify_cohort_special = models.CharField(max_length=255, null=True, blank=True)

    referral_code = models.CharField(max_length=255, null=True, blank=True)

    customer = models.ForeignKey(
        Customer,
        models.CASCADE,
        null=True,
        blank=True,
        related_name="quick_quotes",
        help_text="The customer that was created from this quick quote",
    )

    def __str__(self):
        return f"{self.first_name} {self.last_name} - {self.email}"

    def get_animal(self) -> Optional["QuickQuoteAnimal"]:
        return getattr(self, "animal", None)

    def get_farming_purpose(self) -> Optional["QuickQuoteFarmingPurpose"]:
        return getattr(self, "farming_purpose", None)

    @property
    def account_activation_url(self):
        # This is applicable only if the related customer has been created
        if self.customer:
            return self.customer.get_create_account_url()
        return None

    @property
    def segmentation_status(self):
        all_animals = ["CATTLE", "SHEEP", "GOAT"]
        if not hasattr(self, "animal"):
            return self.SegmentationStatus.UNKNOWN

        if (
            self.animal.animal == "CATTLE"
            and self.grazing_method == QuickQuote.GrazingMethod.EXTENSIVE
            and hasattr(self, "farming_purpose")
            and self.farming_purpose.farming_purpose == QuickQuoteFarmingPurpose.FarmingPurpose.MEAT_PRODUCTION
        ):
            return self.SegmentationStatus.BEEF_CATTLE_EXTENSIVE

        if (
            self.animal.animal in all_animals
            and self.grazing_method == QuickQuote.GrazingMethod.EXTENSIVE
            and hasattr(self, "farming_purpose")
            and self.farming_purpose.farming_purpose == QuickQuoteFarmingPurpose.FarmingPurpose.CONSERVATION
        ):
            return self.SegmentationStatus.HABITAT_MANAGEMENT

        if (
            self.animal.animal in all_animals
            and self.pasture_types.filter(pasture_type=QuickQuotePasture.PastureType.WOODLAND).exists()
        ):
            return self.SegmentationStatus.AGROFORESTRY

        if (
            self.animal.animal == "CATTLE"
            and hasattr(self, "farming_purpose")
            and self.farming_purpose.farming_purpose == QuickQuoteFarmingPurpose.FarmingPurpose.HEIFERS
        ):
            return self.SegmentationStatus.DAIRY_NON_MILKING

        if self.animal.animal in ["SHEEP", "GOAT"] and self.number_of_animals > 150:
            return self.SegmentationStatus.COMMERCIAL_SHEEP_GOAT

        if (
            self.animal.animal in all_animals
            and hasattr(self, "farming_purpose")
            and self.farming_purpose.farming_purpose == QuickQuoteFarmingPurpose.FarmingPurpose.DAIRY_PRODUCTION
        ):
            return self.SegmentationStatus.NON_TARGET_DAIRY_MILKING

        return self.SegmentationStatus.UNKNOWN

    @property
    def get_qualification_status(
        self,
    ) -> QualificationStatus:
        qualification_status = self.QualificationStatus.QUALIFICATION_UNKNOWN

        # Check if unqualified
        if self.number_of_animals and self.number_of_animals > 300:
            qualification_status = self.QualificationStatus.UNQUALIFIED
        if hasattr(self, "animal") and self.animal.animal not in ["CATTLE", "SHEEP", "GOAT"]:
            qualification_status = self.QualificationStatus.UNQUALIFIED

        if qualification_status == self.QualificationStatus.UNQUALIFIED:
            return qualification_status

        # Check if qualified
        if self.number_of_animals and self.number_of_animals >= 5 and self.number_of_animals <= 50:
            if (
                hasattr(self, "farming_purpose")
                and self.farming_purpose.farming_purpose != QuickQuoteFarmingPurpose.FarmingPurpose.DAIRY_PRODUCTION
            ):
                if self.mobile_coverage and self.mobile_coverage == self.MobileCoverage.GOOD:
                    if self.grazing_method and self.grazing_method == self.GrazingMethod.EXTENSIVE:
                        if (
                            self.pasture_types.count() == 1
                            and self.pasture_types.first().pasture_type == QuickQuotePasture.PastureType.FLAT_OPEN
                        ):
                            qualification_status = self.QualificationStatus.LIKELY_QUALIFIED

        return qualification_status

    def can_be_auto_qualified(self):
        from core.models.qq_auto_qualify_config import (
            QQAutoQualifyConfigNO,
            QQAutoQualifyConfigEU,
            QQAutoQualifyConfigUS,
        )

        eligible_countries = [
            "NO",
            "US",
            "ES",
            "IE",
            "GB",
            "GG",
            "IM",
            "JE",
        ]
        if user := User.objects.filter(email__iexact=self.email).first():
            if user.is_user_activation_done():
                return False

        if self.country not in eligible_countries:
            return False

        if self.country == "NO":
            return QQAutoQualifyConfigNO(quick_quote=self).can_be_auto_qualified()

        elif self.country == "US":
            return QQAutoQualifyConfigUS(quick_quote=self).can_be_auto_qualified()

        remaining_countries = [
            country
            for company in NofenceCompany.objects.exclude(countries__contains=["NO", "US"])
            for country in company.countries
        ]

        if self.country in remaining_countries:
            return QQAutoQualifyConfigEU(quick_quote=self).can_be_auto_qualified()

        return False

    def set_qualification_status(self):
        self.qualification_status = self.get_qualification_status_based_on_business_type
        self.save()

    @property
    def auto_qualify_cohort(self):
        if self.auto_qualify_cohort_special:
            return self.auto_qualify_cohort_special
        if self.auto_qualified:
            return "Qualified"
        elif self.auto_qualified is False:
            return "Not Qualified"
        return None

    def track_auto_qualify_event(self, screen_url: str, timestamp: int = None):
        """Creates and sends an auto-qualify event to Amplitude"""
        if self.auto_qualify_cohort is None:
            return

        import logging

        logger = logging.getLogger(__name__)

        try:
            # First send an identify event to ensure user identity is maintained
            identify_event = {
                "user_id": str(self.pk),
                "event_type": "$identify",
                "country": self.customer.country.name,
                "user_properties": {
                    "$set": {
                        "cohort": self.auto_qualify_cohort,
                    },
                },
            }

            # Then create the actual tracking event
            qualify_event = {
                "user_id": str(self.pk),
                "country": self.country.name,
                "event_type": "Assign Auto Qualification Cohort",
                "time": int(timestamp or datetime.now().timestamp() * 1000),
                "event_properties": {
                    "screenUrl": screen_url,
                },
            }

            from core.default_api_clients import create_amplitude_client

            response = create_amplitude_client().track_events([identify_event, qualify_event])
            if response:
                logger.info(
                    f"Successfully tracked events in Amplitude, for Assign Auto Qualification Cohort: "
                    f"qq_id={self.pk}, screen_url={screen_url}",
                    extra={"response": response.json()},
                )

        except Exception as e:
            logger.exception(
                f"Failed to track QQ Auto Qualify event for user: {self.customer.user.pk}, screen: {screen_url}",
                exc_info=e,
            )

    def assign_auto_qualified(self) -> None:
        """
        Assign `True` vs. `False` in a round-robin manner among auto-qualified QuickQuotes.
        For the n-th auto-qualified QQ, assign `True` if n is even, `False` if n is odd (or vice versa).
        """
        if not self.can_be_auto_qualified():
            return

        uk_countries = NofenceCompany.objects.get(code=NOFENCE_UK).countries

        if self.country in uk_countries:
            count_so_far = QuickQuote.objects.filter(
                country__in=uk_countries,
                # we want to do a/b testing for MANAGED grazing only
                grazing_method=QuickQuote.GrazingMethod.MANAGED,
                auto_qualified__in=[True, False],
            ).count()
        else:
            count_so_far = QuickQuote.objects.filter(
                country=self.country,
                grazing_method=QuickQuote.GrazingMethod.MANAGED,
                auto_qualified__in=[True, False],
            ).count()

        # For EXTENSIVE grazing method, we auto-qualify everyone (old successful experiment)
        if self.grazing_method == QuickQuote.GrazingMethod.EXTENSIVE:
            self.auto_qualified = True
            self.auto_qualify_cohort_special = "Everyone Auto Qualified"

        else:
            if count_so_far % 2 == 0:
                self.auto_qualified = True
                self.auto_qualify_cohort_special = f"Qualified {self.grazing_method}"

            else:
                self.auto_qualified = False
                self.auto_qualify_cohort_special = f"Not Qualified {self.grazing_method}"

        self.save()

    def send_email_auto_qualified(self):
        if not self.auto_qualified:
            return

        additional_links = {}
        if self.country == "US":
            # also add additional links to the US email
            additional_links["office_hours_link"] = (
                "https://forms.gle/VMTR6nWQA1vV494Z8?utm_source=qq_email&utm_medium=email"
            )
            additional_links["meeting_link"] = (
                "https://nofence.co/meetings/haakan-skyttmo/us-meetings?utm_source=qq_email&utm_medium=email"
            )
            additional_links["facebook_group_link"] = (
                "https://www.facebook.com/groups/nofencefarmersusa/?utm_source=qq_email&utm_medium=email"
            )

        nofence_company = NofenceCompany.objects.filter(countries__contains=self.country).first()
        from core.default_services import create_email_service

        create_email_service().send_simple_email(
            email_type=EmailType.QUICK_QUOTE_AUTO_QUALIFIED,
            to_email=self.email,
            from_email=nofence_company.sales_contact_us_email if nofence_company else "<EMAIL>",
            subject=str(_("Nofence - Please activate your account")),
            template_context={
                "name": f"{self.first_name} {self.last_name}",
                "account_activation_url": self.account_activation_url,
                "nofence_url": "https://nofence.no",
                "nofence_name": NOFENCE_NAMES.get(self.country, "Nofence"),
                "country": self.country.code,
                "nofence_phone_number": nofence_company.sales_contact_us_phone if nofence_company else "",
                "nofence_email": nofence_company.sales_contact_us_email if nofence_company else "",
                "additional_links": additional_links,
            },
            customer=self.customer,
        )

        self.customer.user.activation_status = User.ActivationStatus.ACCOUNT_CREATION_EMAIL_SENT
        self.customer.user.save()
        self.customer.update_customer_status(Customer.Status.PROSPECT_QUALIFIED, "Quick quote auto qualified")

    @property
    def get_unqualified_by_sales(self):
        if (
            self.qualification_status == self.QualificationStatus.UNQUALIFIED
            and self.get_qualification_status != self.QualificationStatus.QUALIFICATION_UNKNOWN
        ):
            return True
        return False

    @property
    def get_qualification_status_based_on_business_type(self):
        if self.get_qualification_status == self.QualificationStatus.UNQUALIFIED:
            return self.QualificationStatus.UNQUALIFIED

        if self.business_type == QuickQuote.BusinessType.PRIVATE:
            return self.QualificationStatus.UNQUALIFIED

        return self.get_qualification_status

    @property
    def is_unqualified_because_of_business_type(self):
        if self.get_qualification_status == self.QualificationStatus.UNQUALIFIED:
            return False
        if self.get_qualification_status_based_on_business_type == self.QualificationStatus.UNQUALIFIED:
            return True
        return False

    def send_quick_quote_result_email(self):
        from core.default_services import create_email_service

        customer = self.customer

        if self.email and customer and customer.has_active_customer_status():
            create_email_service().send_simple_email(
                email_type=EmailType.QUICK_QUOTE_EXISTING_CUSTOMER,
                to_email=self.email,
                subject=str(_("Nofence - Purchase from the online store today")),
                template_context={
                    "name": f"{self.first_name} {self.last_name}",
                    "store_url": urljoin(settings.ACCOUNT_BASE_URL, reverse("account:store:store")),
                    "store_url_short": "nofence.no/store",
                    "country": customer.country.code,
                },
                customer=customer,
            )
        elif self.country not in settings.COUNTRIES_FIRST:
            create_email_service().send_simple_email(
                email_type=EmailType.QUICK_QUOTE_UNSUPPORTED_COUNTRY,
                to_email=self.email,
                from_email="<EMAIL>",
                subject=str(_("Nofence - Thank you for your interest!")),
                template_context={
                    "name": f"{self.first_name} {self.last_name}",
                    "country": self.country.name,
                },
            )
        elif self.is_unqualified_because_of_business_type:
            nofence_company = NofenceCompany.objects.filter(countries__contains=self.country).first()
            create_email_service().send_simple_email(
                email_type=(
                    EmailType.PRIVATE_CUSTOMER_UNQUALIFIED_US
                    if self.country.code == "US"
                    else EmailType.PRIVATE_CUSTOMER_UNQUALIFIED
                ),
                to_email=self.email,
                from_email=nofence_company.sales_contact_us_email if nofence_company else "<EMAIL>",
                subject=str(_("Nofence - Thank you for your interest!")),
                template_context={
                    "name": f"{self.first_name} {self.last_name}",
                    "nofence_url": "https://nofence.no",
                    "nofence_name": NOFENCE_NAMES.get(self.country, "Nofence"),
                    "nofence_phone_number": (
                        nofence_company.get_url(self.country, NofenceCompany.URLType.SALES_CONTACT_US_PHONE)
                        if nofence_company
                        else ""
                    ),
                    "nofence_email": (
                        nofence_company.get_url(self.country, NofenceCompany.URLType.SALES_CONTACT_US_EMAIL)
                        if nofence_company
                        else ""
                    ),
                },
            )
        elif self.qualification_status == self.QualificationStatus.UNQUALIFIED:
            nofence_company = NofenceCompany.objects.filter(countries__contains=self.country).first()
            create_email_service().send_simple_email(
                email_type=EmailType.QUICK_QUOTE_UNQUALIFIED,
                to_email=self.email,
                from_email=nofence_company.sales_contact_us_email if nofence_company else "<EMAIL>",
                subject=str(_("Nofence - Thank you for your interest!")),
                template_context={
                    "name": f"{self.first_name} {self.last_name}",
                    "nofence_url": "https://nofence.no",
                    "nofence_name": NOFENCE_NAMES.get(self.country, "Nofence"),
                    "nofence_phone_number": (
                        nofence_company.get_url(self.country, NofenceCompany.URLType.SALES_CONTACT_US_PHONE)
                        if nofence_company
                        else ""
                    ),
                    "nofence_email": (
                        nofence_company.get_url(self.country, NofenceCompany.URLType.SALES_CONTACT_US_EMAIL)
                        if nofence_company
                        else ""
                    ),
                },
            )
        elif (
            self.qualification_status
            in [
                self.QualificationStatus.QUALIFICATION_UNKNOWN,
                self.QualificationStatus.LIKELY_QUALIFIED,
            ]
            and not self.auto_qualified
        ):
            nofence_company = NofenceCompany.objects.filter(countries__contains=self.country).first()
            email_type = EmailType.QUICK_QUOTE_QUALIFIED
            create_email_service().send_simple_email(
                email_type=email_type,
                to_email=self.email,
                from_email=nofence_company.sales_contact_us_email if nofence_company else "<EMAIL>",
                subject=str(_("Nofence - Thank you for your interest!")),
                template_context={
                    "name": f"{self.first_name} {self.last_name}",
                    "qualification_status": self.qualification_status,
                    "nofence_url": "https://nofence.no",
                    "nofence_name": NOFENCE_NAMES.get(self.country, "Nofence"),
                    "nofence_phone_number": nofence_company.sales_contact_us_phone if nofence_company else "",
                    "nofence_email": nofence_company.sales_contact_us_email if nofence_company else "",
                },
            )

    def is_filled(self):
        # Farming purpose is not required as we skip it if Other animal is selected

        extra = [] if self.country == "US" else [self.company_name]

        return all(
            [
                self.country,
                self.first_name,
                self.last_name,
                self.email,
                self.phone_number,
                self.number_of_animals,
                self.grazing_method,
                self.mobile_coverage,
                self.animal if hasattr(self, "animal") else None,
                self.pasture_types.count() > 0,
            ]
            + extra,
        )


class QuickQuoteAnimal(BaseHistoryModel):
    quick_quote = models.OneToOneField(QuickQuote, models.CASCADE, related_name="animal")
    animal = models.CharField(max_length=255)

    def __str__(self):
        return f"{self.animal}"


class QuickQuoteFarmingPurpose(BaseHistoryModel):
    class FarmingPurpose(models.TextChoices):
        MEAT_PRODUCTION = "MEAT_PRODUCTION"
        DAIRY_PRODUCTION = "DAIRY_PRODUCTION"
        WOOL_PRODUCTION = "WOOL_PRODUCTION"
        HEIFERS = "HEIFERS"
        CONSERVATION = "CONSERVATION"

    quick_quote = models.OneToOneField(QuickQuote, models.CASCADE, related_name="farming_purpose")
    farming_purpose = models.CharField(max_length=50, choices=FarmingPurpose.choices)

    def __str__(self):
        return f"{self.farming_purpose}"


class QuickQuotePasture(BaseHistoryModel):
    class PastureType(models.TextChoices):
        FLAT_OPEN = "FLAT_OPEN"
        MOUNTAIN = "MOUNTAIN"
        VALLEY = "VALLEY"
        SHELTER = "SHELTER"
        WETLAND = "WETLAND"
        WOODLAND = "WOODLAND"

    quick_quote = models.ForeignKey(QuickQuote, models.CASCADE, related_name="pasture_types")
    pasture_type = models.CharField(max_length=50, choices=PastureType.choices)

    def __str__(self):
        return f"{self.pasture_type}"
