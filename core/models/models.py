import calendar
import dataclasses
import logging
import operator
import os
import re
import pandas as pd
from contextlib import nullcontext
from datetime import date, timedelta, datetime, UTC
from decimal import Decimal
from typing import Union, Optional, List, Tuple, Dict, Self
from functools import cached_property, reduce
from collections import deque
from urllib.parse import urljoin
from django.core.files.base import File
from django.conf import settings
from djangoql.parser import DjangoQLParser

from moneyed import get_currency
from django_countries import Countries
from mongoengine import DoesNotExist
from dateutil.relativedelta import relativedelta
from django_deprecate_fields import deprecate_field

from io import BytesIO
from barcode import Code128 as ShipmentBarcode
from barcode.writer import SVGWriter as BarcodeSVGWriter

from django.utils.encoding import force_bytes
from django.utils.http import urlsafe_base64_encode, urlsafe_base64_decode
from django.utils.formats import date_format
from django.contrib.auth.tokens import default_token_generator
from django.contrib.auth import hashers
from django.contrib.postgres.fields import ArrayField

from django.urls import reverse
from django.utils import translation
from django.db.models.query import QuerySet
from django.contrib.auth.base_user import BaseUserManager
from django.contrib.auth.models import AbstractUser
from django.db import models, transaction, IntegrityError
from django.db.models import (
    CASCADE,
    Count,
    PROTECT,
    SET_NULL,
    Sum,
    Q,
    F,
    Min,
    Max,
    functions,
    Case,
    When,
    Value,
    OuterRef,
    Exists,
)
from django.db.models.manager import BaseManager
from django.utils import timezone
from django.utils.translation import gettext_lazy as _, ngettext
from django.core import signing
from django.core.exceptions import ValidationError
from django.core.files.storage import storages
from django.templatetags.static import static
from django_countries.fields import CountryField
from djmoney.models.fields import MoneyField, CurrencyField
from djmoney.money import Money
from djmoney.settings import CURRENCY_CHOICES
from djmoney.contrib.exchange.models import convert_money
from model_utils import FieldTracker
from simple_history.models import HistoricalRecords
from stdnum import no, vatin


from .documents import AppUserDocument, CollarDocument, UserDocument
from core.models import EMS_PROVIDERS
from core.models.choices import CustomerStatus
from core.models.collarstate import PastureReportSource, PastureUpdateReason
from core.models.SVGAndImageField import SVGAndImageField
from core import products, tokens_generator
from core.constants import CATTLE, SHEEP_GOAT
from core.misc_utils import get_youtube_thumbnail_url, validate_youtube_url
from core.utils.phone_number_utils import format_phone_number
from core.products import SKUS_REPLACEMENT_BRACKET
from core import json_encoding
from core.misc_utils import format_tax_percentage
from core import fields as own_fields
from tinymce.models import HTMLField
from core.clickhouse.clickhouse_connection import clickhouse_collarserver
from .hubspotmodels import HubSpotPipelineModel, HubSpotPipelineStageModel
from .segmentation_status import SegmentationStatus

from localflavor.us.us_states import US_STATES as US_REGIONS

from .base import (
    BaseModel,
    BaseHistoryModel,
    BaseHistoryModelForModelTranslations,
    CAPS,
    RelatedObjectOrNone,
    get_grazing_season_from_timestamp,
)

logger = logging.getLogger(__name__)

PRODUCT_TYPE = (
    (CATTLE, _("Cattle")),
    (SHEEP_GOAT, _("Sheep Goat")),
)


FIXED = "fixed"
VARIABLE = "variable"
LEASING = "leasing"
PREPAID = "prepaid"

PRICE_PLAN_TYPE = (
    (FIXED, _("Fixed Price")),
    (VARIABLE, _("Variable Price")),
    (LEASING, _("Leasing")),
    (PREPAID, _("Prepaid")),
)

ANNUALLY = "A"
MONTHLY = "M"
BILLING_PERIOD = (
    (ANNUALLY, _("Annually")),
    (MONTHLY, _("Monthly")),
)

"""
country codes used to identify our nofence companies
"""
NOFENCE_UK = "UK"
NOFENCE_NO = "NO"
NOFENCE_ES = "ES"
NOFENCE_US = "US"

"""
warehouse codes used to identify our warehouses
"""
INISSION = "INISSION"
HAPRO = "HAPRO"

""" from_date, to_date, price_fraction """
NOFENCE_NO_PERIODS = [
    (date(2023, 3, 1), date(2023, 8, 1), 1),
    (date(2023, 8, 1), date(2023, 11, 1), 0.5),
    (date(2023, 11, 1), date(2024, 3, 1), 0),
]

""" from_date, to_date, price_fraction """
NOFENCE_UK_PERIODS = [
    (date(2022, 3, 1), date(2022, 4, 1), 11 / 12),
    (date(2022, 4, 1), date(2022, 5, 1), 10 / 12),
    (date(2022, 5, 1), date(2022, 6, 1), 9 / 12),
    (date(2022, 6, 1), date(2022, 7, 1), 8 / 12),
    (date(2022, 7, 1), date(2022, 8, 1), 7 / 12),
    (date(2022, 8, 1), date(2022, 9, 1), 6 / 12),
    (date(2022, 9, 1), date(2022, 10, 1), 5 / 12),
    (date(2022, 10, 1), date(2022, 11, 1), 4 / 12),
    (date(2022, 11, 1), date(2022, 12, 1), 3 / 12),
    (date(2022, 12, 1), date(2023, 1, 1), 2 / 12),
    (date(2023, 1, 1), date(2023, 2, 1), 1 / 12),
    (date(2023, 2, 1), date(2023, 3, 1), 0),
]

NEW_COLLAR_REQUIRED_FIELDS = [
    "sim_card_id",
    "product_record",
    "mec_revision",
    "pcb_revision",
    "ems_provider",
    "firmware",
    "firmware_date",
]


class SalesOrderAccountingSystem(models.TextChoices):
    """The external accounting system that handles orders"""

    VISMA = "VISMA"
    VISMA_PREPAID = "VISMA_PREPAID"
    XERO = "XERO"

    @classmethod
    def to_invoice_provider(cls, value):
        if cls.VISMA == value:
            return Invoice.Provider.VISMA
        elif cls.VISMA_PREPAID == value:
            return Invoice.Provider.VISMA_PREPAID
        elif cls.XERO == value:
            return Invoice.Provider.XERO
        raise RuntimeError(f"Unexpected value {value}")

    @classmethod
    def from_invoice_provider(cls, value: "Invoice.Provider"):
        if Invoice.Provider.VISMA == value:
            return cls.VISMA
        elif Invoice.Provider.VISMA_PREPAID == value:
            return cls.VISMA_PREPAID
        elif Invoice.Provider.XERO == value:
            return cls.XERO
        raise RuntimeError(f"Unexpected value {value}")

    @classmethod
    def is_visma_system(cls, value):
        return value in [cls.VISMA, cls.VISMA_PREPAID]


class EmailType(models.TextChoices):
    VAT_INVOICE = "VAT_INVOICE"
    PAYMENT_REQUEST = "PAYMENT_REQUEST"
    ORDER_CONFIRMATION = "ORDER_CONFIRMATION"
    PRICE_LEVEL_NOTIFICATION = "PRICE_LEVEL_NOTIFICATION"
    PRICE_CAP_NOTIFICATION = "PRICE_CAP_NOTIFICATION"
    USAGE_INVOICE = "USAGE_INVOICE"
    REPLACEMENT_AND_RETURN = "REPLACEMENT_AND_RETURN"
    REPLACEMENT_AND_RECYCLE = "REPLACEMENT_AND_RECYCLE"
    USAGE_INVOICE_PO_DRAFT = "USAGE_INVOICE_PO_DRAFT"
    FIRST_PAYMENT_REMINDER = "FIRST_PAYMENT_REMINDER"
    SECOND_PAYMENT_REMINDER = "SECOND_PAYMENT_REMINDER"
    DEBT_COLLECTION_WARNING = "DEBT_COLLECTION_WARNING"
    COLLARS_PENDING_PAYMENT_INFO = "COLLARS_PENDING_PAYMENT_INFO"
    CREDIT_NOTE = "CREDIT_NOTE"
    APP_USER_INVITE = "APP_USER_INVITE"
    APP_USER_DELETED = "APP_USER_DELETED"
    USER_ACTIVATION = "USER_ACTIVATION"
    ACCOUNT_CREATION_FLOW = "ACCOUNT_CREATION_FLOW"
    USER_ACCOUNT_ACCESS_INVITE = "USER_ACCOUNT_ACCESS_INVITE"
    USER_ACCOUNT_ACCESS_DELETED = "USER_ACCOUNT_ACCESS_DELETED"
    GETTING_STARTED = "GETTING_STARTED"
    PASSWORD_RESET = "PASSWORD_RESET"
    CONFIRM_EMAIL_CHANGE = "CONFIRM_EMAIL_CHANGE"
    ADD_BILLING_CONTACT_EMAIL = "ADD_BILLING_CONTACT_EMAIL"
    INTERNAL_DEBT_COLLECTION_NOTIFICATION = "INTERNAL_DEBT_COLLECTION_NOTIFICATION"
    INTERNAL_DEBT_COLLECTION_PAID_NOTIFICATION = "INTERNAL_DEBT_COLLECTION_PAID_NOTIFICATION"
    INTERNAL_DEBT_COLLECTION_COLLECT_NOTIFICATION = "INTERNAL_DEBT_COLLECTION_COLLECT_NOTIFICATION"
    INTERNAL_DIRECT_DEBIT_FAILED_NOTIFICATION = "INTERNAL_DIRECT_DEBIT_FAILED_NOTIFICATION"
    FINANCIAL_REPORT = "FINANCIAL_REPORT"
    PURCHASE_ORDER = "PURCHASE_ORDER"
    DIRECT_DEBIT_REMINDER_FIRST = "DIRECT_DEBIT_REMINDER_FIRST"
    DIRECT_DEBIT_REMINDER_SECOND = "DIRECT_DEBIT_REMINDER_SECOND"
    PO_NUMBER_UPDATE_REMINDER = "PO_NUMBER_UPDATE_REMINDER"
    ENDING_YEARLY_SUBSCRIPTION = "ENDING_YEARLY_SUBSCRIPTION"
    WEEE_BATTERY_REPORT = "WEEE_BATTERY_REPORT"
    PURCHASE_ORDER_REPORT = "PURCHASE_ORDER_REPORT"
    ORDER_CANCELLED = "ORDER_CANCELLED"
    WEB_SHOP_STOCK = "WEB_SHOP_STOCK"
    CANCELLED_ORDER_FINANCE = "CANCELLED_ORDER_FINANCE"
    QUICK_QUOTE_EXISTING_CUSTOMER = "QUICK_QUOTE_EXISTING_CUSTOMER"
    QUICK_QUOTE_UNQUALIFIED = "QUICK_QUOTE_UNQUALIFIED"
    PRIVATE_CUSTOMER_UNQUALIFIED = "PRIVATE_CUSTOMER_UNQUALIFIED"
    PRIVATE_CUSTOMER_UNQUALIFIED_US = "PRIVATE_CUSTOMER_UNQUALIFIED_US"
    QUICK_QUOTE_QUALIFIED = "QUICK_QUOTE_QUALIFIED"
    QUICK_QUOTE_AUTO_QUALIFIED = "QUICK_QUOTE_AUTO_QUALIFIED"
    QUICK_QUOTE_UNSUPPORTED_COUNTRY = "QUICK_QUOTE_UNSUPPORTED_COUNTRY"
    DIRECT_DEBIT_PAYOUT_NOTIFICATION = "DIRECT_DEBIT_PAYOUT_NOTIFICATION"
    PROPOSAL = "PROPOSAL"
    ACCOUNT_CREATION_REMINDER = "ACCOUNT_CREATION_REMINDER"
    ACCOUNT_CREATION_REMINDER_PROSPECT_BASKET = "ACCOUNT_CREATION_REMINDER_PROSPECT_BASKET"
    REFEREE_DISCOUNT_CODE_CREATED = "REFEREE_DISCOUNT_CODE_CREATED"
    REFERRER_VOUCHER_CREATED = "REFERRER_VOUCHER_CREATED"
    REFERRER_VOUCHER_EXPIRED = "REFERRER_VOUCHER_EXPIRED"
    REFERRER_VOUCHER_WILL_EXPIRE = "REFERRER_VOUCHER_WILL_EXPIRE"
    GET_VOUCHER_FOR_BATTERY_REPLACEMENT = "GET_VOUCHER_FOR_BATTERY_REPLACEMENT"
    INTERNAL_POST_PAYMENT_LIMIT_NOTIFICATION = "INTERNAL_POST_PAYMENT_LIMIT_NOTIFICATION"


class FreightMethod(models.TextChoices):
    COLLAR_COUNT_MATRIX = "COLLAR_COUNT_MATRIX"
    WEIGHT_THRESHOLD = "WEIGHT_THRESHOLD"


class EmailConfig(BaseModel):
    nofence_company = models.ForeignKey("NofenceCompany", CASCADE, related_name="email_configs")

    email_type = models.CharField(max_length=100, choices=EmailType.choices)

    send_grid_template = models.CharField(max_length=300)

    from_email = own_fields.EmailField(blank=True, help_text=_("Will <NAME_EMAIL>, if empty."))

    from_name = models.CharField(
        max_length=300,
        blank=True,
        help_text=_(
            "Name that will be displayed as the sender of the email. Will fallback to Nofence Company name, if empty.",
        ),
    )

    # We could add a language code to the config to allow different templates for different languages,
    # but for now there is only a single template version per company
    class Meta:
        unique_together = [
            ("nofence_company", "email_type"),
        ]

    def __str__(self):
        return f"{self.nofence_company} - {self.get_email_type_display()}"

    @property
    def from_email_formatted(self):
        email = self.from_email or "<EMAIL>"
        name = self.from_name or self.nofence_company.name
        return f"{name} <{email}>"


class NofenceCompany(BaseModel):
    # String constant used when comparing the company with constants in code (NOFENCE_NO, NOFENCE_UK)
    code = models.CharField(max_length=100, unique=True, help_text=_("Internal constant used in the code"))
    name = models.CharField(max_length=100)
    countries = CountryField(
        blank=True,
        help_text=_("Don't change the value of an existing company unless you know what you're doing"),
        multiple=True,
    )

    address_value = RelatedObjectOrNone("address")

    accounting_system = models.CharField(
        max_length=300,
        choices=SalesOrderAccountingSystem.choices,
        help_text=_("Don't change the value of an existing company unless you know what you're doing"),
    )
    pre_paid = models.BooleanField(
        default=False,
        help_text=_(
            "Require sales orders to be fully paid before shipping goods. "
            "Don't change this unless you know what you're doing",
        ),
    )
    is_part_of_debt_collection_flow = models.BooleanField(
        default=False,
        help_text=(
            "If true the second payment reminder for invoices will be a debt collection warning "
            "and it will start our internal debt collection process."
        ),
    )
    tax_number = models.CharField(max_length=20, blank=True, default="")

    international_shipment_warehouse = models.ForeignKey(
        "Warehouse",
        PROTECT,
        related_name="international_nofence_companies",
        help_text=_(
            "Used for international shipment routing. If set, shipments will be routed to this warehouse.",
        ),
        null=True,
        blank=True,
    )

    domestic_shipment_warehouse = models.ForeignKey(
        "Warehouse",
        PROTECT,
        related_name="domestic_nofence_companies",
        help_text=_(
            "Used for domestic shipment routing. If set, shipments will be routed to this warehouse.",
        ),
        null=True,
        blank=True,
    )

    sales_contact_us_email = own_fields.EmailField(
        blank=True,
        default="",
        help_text=_("Contact email displayed to customers"),
    )

    sales_contact_us_phone = models.CharField(
        max_length=300,
        blank=True,
        default="",
        help_text=_("Contact phone number displayed to customers"),
    )

    support_contact_us_phone = models.CharField(
        max_length=300,
        blank=True,
        default="",
        help_text=_("Support phone number displayed to customers"),
    )

    sales_page_url = models.CharField(
        max_length=300,
        blank=True,
        default="",
        help_text=_("Sales page customers can be directed to"),
    )

    help_center_url = models.CharField(
        max_length=300,
        null=True,
        blank=True,
        help_text=_("Help center page customers can be directed to"),
    )

    feedback_url = models.CharField(
        max_length=300,
        null=True,
        blank=True,
        help_text=_("Feedback page customers can be directed to"),
    )

    collar_not_reporting_url = models.CharField(
        max_length=300,
        null=True,
        blank=True,
        help_text=_("Page were customers can read more about collars not reporting"),
    )

    beacon_info_url = models.CharField(
        max_length=300,
        null=True,
        blank=True,
        help_text=_("Page were customers can read more about beacons"),
    )

    cattle_forum_url = models.CharField(
        max_length=300,
        null=True,
        blank=True,
        help_text=_("Url to cattle user forum"),
    )

    sheep_and_goat_forum_url = models.CharField(
        max_length=300,
        null=True,
        blank=True,
        help_text=_("Url to sheep and goat user forum"),
    )

    international_sales_contact_us_email = own_fields.EmailField(
        blank=True,
        null=True,
        help_text="International contact email displayed to customers",
    )

    international_sales_contact_us_phone = models.CharField(
        max_length=300,
        blank=True,
        null=True,
        help_text="International contact phone number displayed to customers",
    )

    international_support_contact_us_phone = models.CharField(
        max_length=300,
        blank=True,
        null=True,
        help_text="International support phone number displayed to customers",
    )

    international_sales_page_url = models.URLField(
        null=True,
        blank=True,
        help_text="Sales page customers can be directed to",
    )
    international_help_center_url = models.URLField(
        null=True,
        blank=True,
        help_text="International help center page customers can be directed to",
    )
    international_feedback_url = models.URLField(
        null=True,
        blank=True,
        help_text="Help center page customers can be directed to",
    )
    international_collar_not_reporting_url = models.URLField(
        null=True,
        blank=True,
        help_text="Page were customers can read more about collars not reporting",
    )
    international_beacon_info_url = models.URLField(
        null=True,
        blank=True,
        help_text="Page were customers can read more about beacons",
    )
    international_cattle_forum_url = models.URLField(
        null=True,
        blank=True,
        help_text=_("Url to cattle user forum"),
    )
    international_sheep_and_goat_forum_url = models.URLField(
        null=True,
        blank=True,
        help_text=_("Url to sheep and goat user forum"),
    )

    # Used to limit access to invoice documents for users in the selected groups.
    # If a user is a member of a group that is linked to some nofence_company,
    # the user will only have access to invoice documents from those companies in the admin.
    group_access = models.ManyToManyField(
        "auth.Group",
        blank=True,
        related_name="company_access",
    )

    store_active = models.BooleanField(
        default=False,
        help_text="Customers associated with this company can access the store",
    )

    hubspot_sales_pipeline = models.ForeignKey(
        HubSpotPipelineModel,
        PROTECT,
        help_text="Used for getting correct pipeline for quick quote",
        null=True,
        blank=True,
        default=None,
    )
    hubspot_sales_pipeline_stage = models.ForeignKey(
        HubSpotPipelineStageModel,
        PROTECT,
        help_text="Used for getting correct pipeline stage for quick quote",
        null=True,
        blank=True,
        default=None,
    )

    hubspot_support_pipeline = models.ForeignKey(
        HubSpotPipelineModel,
        PROTECT,
        help_text="Used for getting correct pipeline for support tickets",
        null=True,
        blank=True,
        default=None,
        related_name="nofence_company_support_pipeline",
    )
    hubspot_support_pipeline_stage = models.ForeignKey(
        HubSpotPipelineStageModel,
        PROTECT,
        help_text="Used for getting correct pipeline stage for support tickets",
        null=True,
        blank=True,
        default=None,
        related_name="nofence_company_support_pipeline_stage",
    )

    freight_method_order_with_collars = models.CharField(
        max_length=300,
        null=True,
        blank=True,
        choices=FreightMethod.choices,
        help_text=(
            "The freight method to use for orders with collars in the web shop. "
            "If not specified the collar count matrix is used."
        ),
    )

    one_stop_shop_number = models.CharField(
        max_length=300,
        blank=True,
        default="",
        help_text=_("One-Stop-Shop (OSS) number. https://vat-one-stop-shop.ec.europa.eu/one-stop-shop_en"),
    )

    referrer_discount = models.ForeignKey(
        "CampaignReferral",
        help_text=_("Discount campaign to be applied to the referrer once the referee has made a purchase"),
        on_delete=models.SET_NULL,
        related_name="referrer_companies",
        blank=True,
        null=True,
    )

    referrer_discount_expires_after_days = models.IntegerField(
        blank=True,
        null=True,
        help_text=_("Number of days after which the referrer discount expires"),
    )

    referee_discount = models.ForeignKey(
        "CampaignReferral",
        help_text=_("Discount Campaign to be applied once the referee has made a purchase"),
        on_delete=models.SET_NULL,
        related_name="referee_companies",
        blank=True,
        null=True,
    )

    referee_discount_expires_after_days = models.IntegerField(
        blank=True,
        null=True,
        help_text=_("Number of days after which the referee discount expires"),
    )

    referrals_enabled = models.BooleanField(default=False, help_text="Enable referrals program for this company")
    referrals_enabled_for_countries = CountryField(
        multiple=True,
        blank=True,
        help_text="Enable referrals program for company in selected countries, if unset, all countries are allowed",
    )

    customer_statuses_referral_enabled = ArrayField(
        models.CharField(
            max_length=20,
            choices=CustomerStatus.choices,
            blank=True,
            help_text="Customer statuses allowed to be referred if customer already exists",
        ),
        blank=True,
        default=list,
    )

    sales_order_invoice_due_date_days = models.IntegerField(
        default=14,
        help_text="Number of days used as the due date for sales order invoices",
    )

    first_payment_reminder_days = models.IntegerField(
        default=6,
        help_text="Number of days used to trigger the first payment reminder",
    )

    second_payment_reminder_days = models.IntegerField(
        default=16,
        help_text="Number of days used to trigger the second payment reminder",
    )

    delayed_delivery_popup = models.BooleanField(
        default=False,
        help_text="Show the delayed delivery popup",
    )

    class Meta:
        verbose_name_plural = "Nofence companies"

    def __str__(self):
        return self.name

    def __eq__(self, other):
        """Added for backwards compatibility with checks in existing code

        E.g we often compare nofence_company with one of the constants NOFENCE_NO, NOFENCE_UK constants
        """

        if self.code == other:
            return True

        return super().__eq__(other)

    def __hash__(self):
        """
        Need to override __hash__ after overriding __eq__
        https://code.djangoproject.com/ticket/30333
        """
        return super().__hash__()

    def get_address(self):
        if not self.address_value:
            return {}
        return {
            "address1": self.address_value.address1,
            "address2": self.address_value.address2,
            "zip_code": self.address_value.zip_code,
            "city": self.address_value.city,
            "region": self.address_value.region,
            "country": self.address_value.country,
        }

    def get_email_config(self, email_type: EmailType) -> EmailConfig:
        try:
            return self.email_configs.get(email_type=email_type)
        except Exception:
            logger.warning(f"No email config found for email type {email_type} and company {self}.")
            raise

    def get_max_unpaid_amount_in_post_payment(self, currency: str) -> Optional[Money]:
        try:
            return self.post_payment_limits.get(
                max_unpaid_amount_currency=currency,
            ).max_unpaid_amount
        except PostPaymentLimit.DoesNotExist:
            return None

    def get_current_unpaid_amount_in_post_payment_orders(
        self, currency: str, exclude_order_ids: list[int] = None
    ) -> Money:
        qs = SalesOrder.objects.filter(
            nofence_company=self,
            payment_status=SalesOrder.PaymentStatus.PENDING_PAYMENT,
            post_payment=True,
            currency=currency,
        )

        if exclude_order_ids:
            qs = qs.exclude(id__in=exclude_order_ids)

        currently_unpaid = sum(o.sum_total() for o in qs.prefetch_related("lines"))

        return currently_unpaid or Money("0", currency)

    def clean(self):
        if self.is_part_of_debt_collection_flow:
            if self.code not in [NOFENCE_NO, NOFENCE_UK]:
                raise ValidationError(
                    {
                        "is_part_of_debt_collection_flow": "Only Norway and UK can be part of debt collection flow",
                    },
                )
        for country in self.countries:
            if NofenceCompany.objects.filter(countries__contains=country).exclude(code=self.code).exists():
                raise ValidationError(
                    {
                        "countries": _("Country {name} already assigned to a nofence company.").format(
                            name=country.name
                        )
                    },
                )
        super(NofenceCompany, self).clean()

    def get_display_name(self):
        """Used in email footers"""
        return settings.NOFENCE_COMPANY_NAMES.get(self.code, "")

    def get_display_address(self):
        """Used in email footers"""
        if not self.address_value:
            return ""
        return self.address_value.format_html_display(with_city=True, with_country=True)

    def get_shipment_warehouse(self, ship_to_country_code: str = None):
        """
        We have different warehouses for domestic and international shipments
        So, we need to determine which warehouse to use based on the ship_to_country_code
        """
        if not ship_to_country_code:
            return self.domestic_shipment_warehouse

        if self.code == "ES" and ship_to_country_code != "ES":
            return self.international_shipment_warehouse

        return self.domestic_shipment_warehouse

    class URLType(models.TextChoices):
        HELP_CENTER = "HELP_CENTER"
        FEEDBACK = "FEEDBACK"
        COLLAR_NOT_REPORTING = "COLLAR_NOT_REPORTING"
        BEACON_INFO = "BEACON_INFO"
        CATTLE_FORUM = "CATTLE_FORUM"
        SHEEP_AND_GOAT_FORUM = "SHEEP_AND_GOAT_FORUM"
        SALES_PAGE = "SALES_PAGE"
        SALES_CONTACT_US_EMAIL = "SALES_CONTACT_US_EMAIL"
        SALES_CONTACT_US_PHONE = "SALES_CONTACT_US_PHONE"
        SUPPORT_CONTACT_US_PHONE = "SUPPORT_CONTACT_US_PHONE"

    def get_url(self, country: str, url_type: URLType) -> Optional[str]:
        url_map_domestic = {
            self.URLType.HELP_CENTER: self.help_center_url,
            self.URLType.FEEDBACK: self.feedback_url,
            self.URLType.COLLAR_NOT_REPORTING: self.collar_not_reporting_url,
            self.URLType.BEACON_INFO: self.beacon_info_url,
            self.URLType.CATTLE_FORUM: self.cattle_forum_url,
            self.URLType.SHEEP_AND_GOAT_FORUM: self.sheep_and_goat_forum_url,
            self.URLType.SALES_PAGE: self.sales_page_url,
            self.URLType.SALES_CONTACT_US_EMAIL: self.sales_contact_us_email,
            self.URLType.SALES_CONTACT_US_PHONE: self.sales_contact_us_phone,
            self.URLType.SUPPORT_CONTACT_US_PHONE: self.support_contact_us_phone,
        }
        url_map_international = {
            self.URLType.HELP_CENTER: self.international_help_center_url,
            self.URLType.FEEDBACK: self.international_feedback_url,
            self.URLType.COLLAR_NOT_REPORTING: self.international_collar_not_reporting_url,
            self.URLType.BEACON_INFO: self.international_beacon_info_url,
            self.URLType.CATTLE_FORUM: self.international_cattle_forum_url,
            self.URLType.SHEEP_AND_GOAT_FORUM: self.international_sheep_and_goat_forum_url,
            self.URLType.SALES_PAGE: self.international_sales_page_url,
            self.URLType.SALES_CONTACT_US_EMAIL: self.international_sales_contact_us_email,
            self.URLType.SALES_CONTACT_US_PHONE: self.international_sales_contact_us_phone,
            self.URLType.SUPPORT_CONTACT_US_PHONE: self.international_support_contact_us_phone,
        }

        if self == NOFENCE_ES and country != "ES":
            international_help_center_url = url_map_international[url_type]
            if international_help_center_url:
                return international_help_center_url
        return url_map_domestic[url_type]


class PostPaymentLimit(BaseHistoryModel):
    nofence_company = models.ForeignKey(NofenceCompany, CASCADE, related_name="post_payment_limits")

    max_unpaid_amount = MoneyField(max_digits=14, decimal_places=2)

    class Meta:
        constraints = [
            models.UniqueConstraint(
                fields=["nofence_company", "max_unpaid_amount_currency"],
                name="unique_post_payment_limit",
            ),
        ]


def lookup_default_tax_category():
    # .values is used so that for future migrations, new field references will not break
    default = TaxCategory.objects.filter(default=True).values("id").first()
    if default:
        return default["id"]


class StripeProductTaxCode(BaseHistoryModel):
    class Type(models.TextChoices):
        SERVICES = "services"
        DIGITAL = "digital"
        PHYSICAL = "physical"

    tax_code_id = models.CharField(max_length=64, null=False, blank=False, unique=True)
    type = models.CharField(max_length=64, choices=Type.choices)
    description = models.CharField(max_length=1024, null=False, blank=False)
    name = models.CharField(max_length=256, null=True, blank=True)

    def __str__(self):
        return f"{self.tax_code_id} - {self.name} - {self.type}"


class TaxCategory(BaseHistoryModel):
    """Just used to link products and tax definitions"""

    class TaxCategoryType(models.TextChoices):
        DEFAULT = "DEFAULT", "Default"
        SERVICES = "SERVICES", "Services"
        FLAT_RATE_SCHEME_HW = "FLAT_RATE_SCHEME_HW", "Flat rate scheme hardware"
        FLAT_RATE_SCHEME_SUBS = "FLAT_RATE_SCHEME_SUBS", "Flat rate scheme subscriptions"

    name = models.CharField(max_length=300)
    category_type = models.CharField(
        max_length=300,
        choices=TaxCategoryType.choices,
        blank=True,
        null=True,
    )
    default = models.BooleanField(default=False)
    stripe_product_tax_code = models.OneToOneField(
        StripeProductTaxCode,
        on_delete=models.DO_NOTHING,
        null=True,
        blank=True,
        related_name="stripe_tax_code",
    )

    class Meta:
        verbose_name_plural = "Tax categories"

        constraints = [
            models.UniqueConstraint(
                fields=["default"],
                condition=Q(default=True),
                name="unq_default_tax_category",
            ),
        ]

    def __str__(self):
        return self.name


class TaxCode(BaseHistoryModel):
    """Tax codes that we send to Xero (maybe Visma later)"""

    class Provider(models.TextChoices):
        XERO = "XERO"

    name = models.CharField(max_length=300)
    tax_code = models.CharField(
        max_length=300,
        help_text="This is the ID of the tax rate, which we can set on the invoice line items",
    )
    nofence_company = models.ForeignKey(
        NofenceCompany,
        on_delete=models.CASCADE,
        related_name="tax_codes",
    )

    # Keep track of where we got the values from, if synced from external system
    provider = models.CharField(max_length=300, null=True, blank=True, choices=Provider.choices)

    # Extra data. E.g. the JSON body of the API response when the code is fetches from an external system
    metadata = models.JSONField(encoder=json_encoding.JSONEncoder, default=dict, blank=True)

    auto_tax_code_key = models.CharField(
        max_length=300,
        null=True,
        blank=True,
        unique=True,
        help_text="Internal key used when auto creating tax rates for US",
    )

    class Meta:
        unique_together = ("tax_code", "nofence_company")

    def __str__(self):
        return f"{self.get_provider_display()} {self.nofence_company.code} - {self.name}"


class TaxDefinition(BaseHistoryModel):
    class TaxRate(Decimal, models.Choices):
        TAX_25 = Decimal("25.0"), "25%"
        TAX_23 = Decimal("23.0"), "23%"
        TAX_21 = Decimal("21.0"), "21%"
        TAX_20 = Decimal("20.0"), "20%"
        TAX_0 = Decimal("0.0"), "0%"

    nofence_company = models.ForeignKey(NofenceCompany, CASCADE, related_name="tax_definitions")
    country = CountryField()
    tax_category = models.ForeignKey(
        TaxCategory,
        CASCADE,
        related_name="tax_definitions",
        default=lookup_default_tax_category,
    )
    tax_rate = models.DecimalField(
        max_digits=3,
        decimal_places=1,
        choices=TaxRate.choices,
        help_text=(
            "Warning: All tax rates for the same combination of Nofence Company and country "
            "must be the same. Mixed tax rates is currently not supported. "
        ),
    )

    tax_code = models.ForeignKey(
        TaxCode,
        CASCADE,
        null=True,
        blank=True,
        help_text=(
            "If set this is sent to Xero when creating invoices. "
            "Warning: If set, make sure the tax rate defined by the the tax code in the accounting system matches "
            "the tax rate above."
        ),
    )

    # Translations needed? Could maybe use the data from the tax code instead?
    tax_message = models.CharField(
        max_length=300,
        default="",
        blank=True,
        help_text="If set this message is shown by the tax rate on the lines when "
        "rendering invoices. Using the string '{tax_rate}' in the message will be "
        "replaced by the tax rate %",
    )

    class Meta:
        constraints = [
            models.UniqueConstraint(
                fields=["nofence_company", "country", "tax_category"],
                name="unq_tax_definition",
            ),
        ]

    def __str__(self):
        return f"{self.country} - {self.get_tax_rate_display()}"

    def get_tax_code(self) -> Optional[str]:
        """Returns the tax code to use for this definition."""
        # Return None if value is empty
        if self.tax_code:
            return self.tax_code.tax_code

    def get_tax_message(self) -> Optional[str]:
        # If we want we could get the message from the tax code instead, but currently using
        # a billy defined message.
        if self.tax_message:
            return self.tax_message.replace("{tax_rate}", format_tax_percentage(self.tax_rate))


class UserManager(BaseUserManager):
    """
    Custom user model manager where email is the unique identifiers
    for authentication instead of usernames.
    """

    def create_user(self, email, password, **extra_fields):
        """
        Create and save a User with the given email and password.
        """
        if not email:
            raise ValueError(_("The Email must be set"))
        email = self.normalize_email(email)
        user = self.model(email=email, **extra_fields)
        user.set_password(password)
        user.save()
        return user

    def create_superuser(self, email, password, **extra_fields):
        """
        Create and save a SuperUser with the given email and password.
        """
        extra_fields.setdefault("is_staff", True)
        extra_fields.setdefault("is_superuser", True)
        extra_fields.setdefault("is_active", True)

        if extra_fields.get("is_staff") is not True:
            raise ValueError(_("Superuser must have is_staff=True."))
        if extra_fields.get("is_superuser") is not True:
            raise ValueError(_("Superuser must have is_superuser=True."))
        return self.create_user(email, password, **extra_fields)

    def get_by_natural_key(self, username: str):
        return User.objects.get(email__iexact=self.normalize_email(username))


class User(AbstractUser, BaseHistoryModel):
    class ActivationStatus(models.TextChoices):
        NOT_ACTIVATED = "NOT_ACTIVATED"
        ACTIVATION_EMAIL_SENT = "ACTIVATION_EMAIL_SENT"
        ACCOUNT_CREATION_EMAIL_SENT = "ACCOUNT_CREATION_EMAIL_SENT"
        ACTIVATED_FROM_ACCOUNT_CREATION_FLOW = "ACTIVATED_FROM_ACCOUNT_CREATION_FLOW"
        ACTIVATED_FROM_BILLY = "ACTIVATED_FROM_BILLY"
        APP_USER = "APP_USER"

    username = None
    first_name = models.CharField(max_length=255, null=True, blank=True)
    last_name = models.CharField(max_length=255, null=True, blank=True)
    name = models.CharField(max_length=255)
    email = own_fields.EmailField(_("email address"), unique=True)

    first_login_date_after_onboarding_page = models.DateTimeField(
        null=True,
        blank=True,
        help_text=_("Used to track the date when the user first logged in after the onboarding page was shown"),
    )

    # Override the password field to allow larger password hashes from my.nofence
    password = models.CharField(_("password"), max_length=1200)

    USERNAME_FIELD = "email"
    REQUIRED_FIELDS = []

    # Can be used to limit the access to warehouse related data
    # If empty, the default, the user will have access to all warehouses.
    # This is currently used to limit access to shipments
    warehouse_access = models.ManyToManyField(
        "Warehouse",
        blank=True,
        help_text=_(
            "If set the user will be limited to only have access shipments from the selected warehouses "
            "in the shipments list.",
        ),
    )

    # Can be used to limit the access to company related data.
    # If empty, the default, the user will have access to all companies.
    # This is currently used to limit access to invoice_documents.
    company_access = models.ManyToManyField(
        NofenceCompany,
        blank=True,
        help_text=_(
            "If set the user will be limited to only have access to invoice documents from the "
            "selected companies in the invoice documents list.",
        ),
    )

    # Django already has the is_active flag which we could use, but this is currently setup to be
    # updated based on the company stage in HubSpot, so we use a separate field to keep of the
    # user activation status in billy
    # Default to null, which means the customer was handled by the old activation in my.nofence
    # New customers are created with, NOT_ACTIVATED and set to ACTIVATED_FROM_BILLY when activated with the
    # new activation in billy.
    activation_status = models.CharField(
        max_length=300,
        choices=ActivationStatus.choices,
        null=True,
        blank=True,
        help_text="If empty the user activation was initially handled by my.nofence.no",
    )

    objects = UserManager()

    def __str__(self):
        return self.email

    class Meta:
        db_table = "auth_user"

        permissions = (
            ("beta_test", _("The user is allowed to beta test features.")),
            ("2nd_line_support", _("Admin features for 2nd line support.")),
            ("pick_and_pack_app", _("The use has access to the pick and pack app in billy")),
            ("healthcheck", _("Enables access to health check.")),
            ("adminapp", _("The user is allowed to access the adminapp API.")),
            ("data_export", _("The user is allowed to export collar data.")),
            # This is used to allow the support team to e.g. add a user to the data export group
            ("manage_groups", _("The user is allowed to manage a users permission groups")),
            ("send_test_email", _("The user is allowed to send test email records")),
            # Impersonation in the app is currently implemented in the app by using a special username like:
            # <EMAIL><EMAIL>
            ("impersonate_user_in_app", _("The user is allowed to login on behalf of another user in the app")),
            ("set_order_post_payment", "The user is allowed to set post payment on orders"),
            ("override_order_options", "Allows the user to set options on orders in web shop"),
        )

    def is_customer(self):
        try:
            self.customer
            return True
        except Exception:
            return False

    def is_app_user(self):
        try:
            if self.is_customer():
                return False
            self.app_user
            return True
        except Exception:
            return False

    def is_active_with_password(self):
        return self.is_active and self.has_usable_password()

    def has_password_set(self):
        return self.password not in ("", "dummy", "dummy_password")

    def can_override_order_options(self):
        return self.has_perm("core.override_order_options")

    def get_real_user(self) -> "User":
        if getattr(self, "is_impersonate", False):
            return self.impersonator
        return self

    def get_real_user_id(self) -> int:
        if getattr(self, "is_impersonate", False):
            return self.impersonator.pk
        return self.pk

    def can_access_store(self):
        if self.is_staff:
            return True
        if not self.is_customer():
            return False
        customer = self.customer

        if not (self.is_user_activation_done() and self.is_active_with_password()):
            logger.warning(f"User {self} has not activated his account, denying access to store")
            return False

        if customer.has_store_access:
            return True

        if not customer.nofence_company.store_active:
            return False

        if customer.nofence_company.code == NOFENCE_NO:
            if customer.country != "NO":
                return False
            if customer.visma_prepaid_id is None:
                return False
        elif customer.nofence_company.code == NOFENCE_UK:
            if customer.country not in ["GB", "IE"]:
                return False
            if customer.xero_id is None:
                return False
        else:
            if customer.xero_id is None:
                return False
        return True

    def get_accounts_access(self):
        return UserAccountAccess.objects.filter(user=self).all()

    def add_access_to_account(self, customer, access_type, is_accepted=False):
        return UserAccountAccess.objects.create(
            user=self,
            customer=customer,
            type=access_type,
            is_accepted=is_accepted,
        )

    def get_session_auth_hash(self) -> str:
        """Calculate the hash used to validate auth sessions

        We include the password reset code in the hash so that customer sessions logged
        into the account pages will be invalidated when the user resets his password
        in my.nofence.

        We do the same for app users using a token counter that is incremented every time
        we make any changes that should invalidate a token.
        """
        auth_hash = super().get_session_auth_hash()
        if self.is_customer() and self.customer.user_no:
            pwd_reset_count = self._get_password_reset_count()
            if pwd_reset_count:
                auth_hash = f"{auth_hash}-{pwd_reset_count}"
        if self.is_app_user():
            token_counter = AppUserDocument.get_token_counter(self.email)
            auth_hash = f"{auth_hash}-{token_counter}"
        return auth_hash

    def _get_password_reset_count(self) -> Optional[int]:
        try:
            doc = UserDocument.objects.only("password_reset_count").get(user_no=self.customer.user_no)
            return doc.password_reset_count or 0
        except DoesNotExist:
            return 1

    def validate_unique(self, *args, **kwargs):
        super().validate_unique(*args, **kwargs)
        qs = User.objects.filter(email__iexact=self.email)
        if not self._state.adding and self.pk is not None:
            qs = qs.exclude(pk=self.pk)
        if qs.exists():
            raise ValidationError({"email": _("User with this Email address already exists.")})

    def is_user_activation_done(self):
        if self.activation_status in [
            User.ActivationStatus.ACTIVATED_FROM_BILLY,
            User.ActivationStatus.ACTIVATED_FROM_ACCOUNT_CREATION_FLOW,
        ]:
            return True

        # App users are currently handled separately by the invitation handling
        if self.activation_status == User.ActivationStatus.APP_USER:
            return self.has_usable_password()

        # If None the activation was initially handled by my.nofence. If the password has been
        # set we consider the account to be activated.
        # Earlier we used to set the users initial password to "dummy" or "dummy_password",
        # which is not an unusable password, so we have to check for those.
        return (
            self.activation_status is None
            and self.password not in ("dummy", "dummy_password")
            and self.has_usable_password()
        )

    def send_user_activation_email(self):
        # Old account activation, only active from app login screen
        logger.info(f"Preparing to send activation email to {self}")

        if self.is_user_activation_done():
            logger.info(f"Account already activated. Skipping sending activation email to {self}")
            return

        if self.activation_status == User.ActivationStatus.APP_USER:
            logger.info(f"App user. Skipping sending activation email to {self}")
            return

        if self.activation_status == User.ActivationStatus.ACCOUNT_CREATION_EMAIL_SENT:
            logger.info(f"Account creation email already sent. Skipping sending activation email to {self}")
            return

        customer: Customer = self.customer if self.is_customer() else None
        block_activation_email_customer_statuses = [
            Customer.Status.UNQUALIFIED_CUSTOMER,
            Customer.Status.LOST_CUSTOMER,
            Customer.Status.LOST_PROSPECT,
            Customer.Status.PROSPECT_UNREACHABLE,
            Customer.Status.DELETED,
        ]
        if customer and customer.customer_status in block_activation_email_customer_statuses:
            logger.info(f"Customer status {customer.customer_status}. Skipping sending activation email to {self}")
            return

        new_activation_email_customer_statuses = [
            Customer.Status.PROSPECT,
            Customer.Status.PROSPECT_QUALIFIED,
            Customer.Status.PROSPECT_ACCOUNT,
            Customer.Status.PROSPECT_WEBSHOP,
            Customer.Status.PROSPECT_BASKET,
            Customer.Status.PROSPECT_ORDER,
            Customer.Status.NEW_CUSTOMER,
            Customer.Status.CUSTOMER_Y2,
        ]
        if customer and customer.customer_status in new_activation_email_customer_statuses:
            logger.info(f"Customer status {customer.customer_status}. Sending new activation email to {self}")
            customer.send_account_activation_flow_email()
            return

        with self.activate_preferred_translation_language():
            from core.default_services import create_email_service

            create_email_service().send_simple_email(
                email_type=EmailType.USER_ACTIVATION,
                to_email=self.email,
                subject=str(_("Nofence Account Activation")),
                template_context={
                    "activation_url": self.get_user_activation_url(),
                    "full_name": self.name,
                },
                customer=customer,
            )

        logger.info(f"Sent activation email to {self}")

        self.activation_status = User.ActivationStatus.ACTIVATION_EMAIL_SENT
        self.save()

    def activate_preferred_translation_language(self):
        activate_translations = nullcontext()
        if self.is_customer():
            customer: Customer = self.customer
            activate_translations = customer.activate_preferred_translation_language()
        return activate_translations

    def get_user_activation_url(self) -> str:
        # Old account activation, only active from app login screen
        lang_param = ""
        if self.is_customer():
            customer: Customer = self.customer
            lang_param = "?lang=" + customer.get_preferred_language_code()

        return (
            urljoin(
                settings.ACCOUNT_BASE_URL,
                reverse(
                    "activate-account",
                    kwargs={
                        "uidb64": self.base64_encoded_id(),
                        "token": self.create_user_activation_token(),
                    },
                ),
            )
            + lang_param
        )

    def create_user_activation_token(self) -> str:
        # Old account activation, only active from app login screen
        from core import user_activation

        return user_activation.default_activation_token_generator.make_token(self)

    def base64_encoded_id(self) -> str:
        """Used in invite url"""
        return urlsafe_base64_encode(force_bytes(self.id))

    def get_warehouse_access(self):
        # Limit access to shipments from warehouses defined on any of the user's auth groups
        # or on the user itself. If no warehouses are defined the user will have access to
        # all warehouses.

        groups = self.groups.prefetch_related("warehouse_access").all()

        warehouse_access = [warehouse for group in groups for warehouse in group.warehouse_access.all()]

        warehouse_access += self.warehouse_access.all()

        return warehouse_access

    def save(self, *args, **kwargs) -> None:
        increment_token_counter = False
        if not self._state.adding and self.is_app_user():
            # We could use the FieldTracker, but because of https://github.com/jazzband/django-model-utils/issues/331,
            # we check for changes manually
            existing = User.objects.get(id=self.id)
            if self.is_active != existing.is_active and not self.is_active:
                increment_token_counter = True

        if self._state.adding and self.activation_status is None:
            self.activation_status = User.ActivationStatus.NOT_ACTIVATED

        ret = super().save(*args, **kwargs)

        if increment_token_counter:
            # Increment after save in case the save fails.
            self.app_user.increment_token_counter()

        return ret


class UserEmailUpdateManager(models.Manager):
    def get_from_base64_id(self, uidb64: str) -> Optional["UserEmailUpdate"]:
        """Used in accept url"""
        try:
            uid = urlsafe_base64_decode(uidb64).decode()
            user_email_update = self.get(pk=uid)
        except (ValueError, UserEmailUpdate.DoesNotExist):
            user_email_update = None
        return user_email_update


class UserEmailUpdate(BaseModel):
    new_email = own_fields.EmailField()
    old_email = own_fields.EmailField()
    user = models.OneToOneField(User, CASCADE, related_name="email_update")

    objects: UserEmailUpdateManager = UserEmailUpdateManager()

    def create_accept_url(self, request) -> str:
        """Create a token the same ways as django's password reset function"""
        return request.build_absolute_uri(
            reverse(
                "account:change-email-accept",
                kwargs={
                    "uidb64": self.base64_encoded_id(),
                    "token": self.create_accept_token(),
                },
            ),
        )

    def create_accept_token(self) -> str:
        token = default_token_generator.make_token(self.user)
        return token

    def check_accept_token(self, token: str) -> bool:
        return default_token_generator.check_token(self.user, token)

    def base64_encoded_id(self) -> str:
        """Used in invite url"""
        return urlsafe_base64_encode(force_bytes(self.id))

    def send_accept_mail(self, request):
        from core.default_services import create_email_service

        create_email_service().send_simple_email(
            email_type=EmailType.CONFIRM_EMAIL_CHANGE,
            to_email=self.new_email,
            subject=str(_("Nofence Accept Email Update")),
            template_context={
                "accept_url": self.create_accept_url(request),
            },
            customer=self.user.customer if self.user.is_customer() else None,
        )

    def update_email_and_increment_token_counter(self):
        from core import default_api_clients

        if self.user.is_customer() and self.user.customer.user_no:
            """Updates email and increments token counter in master server to invalidate existing tokens"""
            nofence = default_api_clients.create_nofence_client(default_timeout=15)
            nofence.update_email(
                self.user.customer.user_no,
                self.new_email,
            )
        elif self.user.is_app_user():
            """Increments token counter in master server to invalidate existing tokens"""
            nofence = default_api_clients.create_nofence_client(default_timeout=15)
            nofence.increment_app_user_token_counter(self.user.email)

    @transaction.atomic
    def delete_self(self):
        return self.delete()

    def __str__(self):
        return str(self.user)

    def delete(self, *args, **kwargs):
        self.update_email_and_increment_token_counter()
        ret = super().delete(*args, **kwargs)
        return ret


class PricePlan(BaseHistoryModel):
    """
    Holds our price plans
    """

    name = models.CharField(max_length=255)
    product = models.CharField(max_length=2, choices=PRODUCT_TYPE)
    sku = models.CharField(max_length=255)
    usage_sku = models.CharField(max_length=255, null=True, blank=True)
    type = models.CharField(max_length=12, choices=PRICE_PLAN_TYPE)

    def __str__(self):
        return f"{self.get_product_display()} - {self.get_type_display()} - {self.name}"

    @property
    def is_12_month_pre_paid(self):
        return self.type == PREPAID

    @property
    def is_prorated(self):
        if self.type in [FIXED, VARIABLE]:
            return True
        return False


class PricePlanPriceQuerySet(models.QuerySet):
    def for_country(self, country):
        return self.filter(countries__contains=country)

    def for_customer(self, customer: "Customer"):
        return self.for_country(customer.country)


class PricePlanPrice(BaseHistoryModel):
    """
    This model holds specific prices for a priceplan given a currency and countries it applies to.
    For a case like Norway this is simple, we have prices in NOK and it applies for NO, however
    for a lot of European countries we want to price in EUR, but we want a different price in say
    Spain compared to Germany.
    """

    objects = models.Manager.from_queryset(PricePlanPriceQuerySet)()

    price_plan = models.ForeignKey(PricePlan, PROTECT, related_name="price_plan_prices")
    annual = MoneyField(max_digits=14, decimal_places=2, default_currency="NOK")
    usage = MoneyField(max_digits=14, decimal_places=2, default_currency="NOK", default=None, blank=True, null=True)
    countries = CountryField(multiple=True, default="NO")


class CustomerManager(models.Manager):
    def create(self, email, name, is_active=True, **obj_data):
        """create a user model with some basic data"""
        if not email:
            raise ValueError(_("The Email must be set"))
        with transaction.atomic():
            existing_user = User.objects.filter(email__iexact=email).first()
            if existing_user and existing_user.is_app_user():
                obj_data["user"] = existing_user
            else:
                new_user = User.objects.create(
                    email=email,
                    name=name,
                    is_active=is_active,
                    # Set the initial password to an unusable password.
                    # make_password(None) has special meaning in Django.
                    password=hashers.make_password(None),
                )
                obj_data["user"] = new_user

            """ Name is required by both customer and user.
            Use the customer name as default for user name until this is updated. """
            obj_data["name"] = name

            created_customer = super().create(**obj_data)
            created_customer.user.add_access_to_account(
                created_customer,
                UserAccountAccess.PermissionsType.PRIMARY,
                True,
            )

            return created_customer


class Customer(BaseHistoryModel):
    class Type(models.TextChoices):
        BUSINESS = "BUSINESS", _("Business")
        PRIVATE = "PRIVATE", _("Private")

    Status = CustomerStatus

    class InitiatedContactStatus(models.TextChoices):
        FIRST_ATTEMPT_FAILED = "FIRST_ATTEMPT_FAILED", "1st. attempt - failed"
        SECOND_ATTEMPT_FAILED = "SECOND_ATTEMPT_FAILED", "2nd. attempt - failed"

    class PurchaseOrderFlow(models.TextChoices):
        DRAFT_FLOW = "DRAFT_FLOW"
        # EDIT_FLOW allows the user to edit issued invoices with
        # updated PO numbers. This is also true the draft flow once
        # the draft has been submitted.
        EDIT_FLOW = "EDIT_FLOW"

    PRICE_PLAN_PREFERENCE = (
        (FIXED, _("Fixed Price")),
        (VARIABLE, _("Variable Price")),
    )

    T2_PRICE_PLAN_THRESHOLD = 50
    PRICE_LEVEL_T1 = "t1"
    PRICE_LEVEL_T2 = "t2"

    user = models.OneToOneField(User, on_delete=models.CASCADE)
    business_reg_no = models.CharField(_("Business registration number"), max_length=50, blank=True)
    eori_number = models.CharField("EORI number", max_length=50, blank=True, default="")
    flat_rate_scheme = models.BooleanField(
        default=False,
        help_text="Is the customer registered on the Flat-rate Scheme?",
    )
    invoice_requester_first_name = models.CharField(max_length=255, null=True, blank=True)
    invoice_requester_last_name = models.CharField(max_length=255, null=True, blank=True)
    name = models.CharField(max_length=255, default=" ")
    country = CountryField(
        default="NO",
        help_text=_(
            "Only change the country if you know what you're doing. "
            "You should also manually change my.nofence, visma/xero, and "
            "trigger a sync to hubspot",
        ),
    )
    nofence_company: NofenceCompany = models.ForeignKey(
        NofenceCompany,
        PROTECT,
        help_text=_(
            "Changing the Nofence company of a customer will affect the accounting system "
            "used to track the customer's orders and invoices. Don't change the value unless "
            "you know what you're doing.",
        ),
    )
    user_no = models.CharField(max_length=6, null=True, blank=True, unique=True)
    visma_id = models.CharField(max_length=6, null=True, blank=True, unique=True)
    visma_prepaid_id = models.CharField(max_length=6, null=True, blank=True, unique=True)
    hubspot_id = models.CharField(max_length=12, null=True, blank=True, unique=True)
    xero_id = models.CharField(max_length=255, null=True, blank=True)
    go_cardless_id = models.CharField(max_length=300, null=True, blank=True, unique=True)
    stripe_id = models.CharField(max_length=300, null=True, blank=True, unique=True)
    stripe_default_payment_method = models.CharField(max_length=300, null=True, blank=True)
    billing_period_preference = models.CharField(max_length=1, null=False, choices=BILLING_PERIOD, default=ANNUALLY)
    automatic_invoice = models.BooleanField(help_text="If true this customer is automatically invoiced", default=True)
    automatic_invoice_note = models.TextField(blank=True, null=False)

    phone_number = models.CharField(max_length=50, blank=True)
    phone_number_formatted = models.CharField(max_length=50, blank=True)
    phone_number_mobile = models.CharField(max_length=50, blank=True)
    phone_number_mobile_formatted = models.CharField(max_length=50, blank=True)
    sms_consent = models.BooleanField(
        default=False,
        blank=True,
        null=True,
        help_text="Indicates if customer consented to be contacted via SMS or whatsapp during the quickquote",
    )

    # Should not be nullable, but at the moment we don't have the data for existing customers, which must
    # be fetched from hubspot. We should migrate this to not nullable when the data has been populated.
    customer_type = models.CharField(max_length=50, null=True, blank=True, choices=Type.choices)

    # Customer experience level.
    customer_status = models.CharField(max_length=50, null=True, blank=True, choices=Status.choices)
    customer_status_changed_at = models.DateTimeField(null=True, blank=True)

    segmentation_status = models.CharField(max_length=50, null=True, blank=True, choices=SegmentationStatus.choices)

    # Status of the contact initiated by the sales team
    initiated_contact = models.CharField(max_length=50, null=True, blank=True, choices=InitiatedContactStatus.choices)
    first_contact_initiated_at = models.DateTimeField(null=True, blank=True)
    unqualified_by_sales = models.BooleanField(
        default=False,
        help_text="If true, the qualification status of the customer was changed to unqualified in hubspot",
    )

    # this field is used for enabling access for a customer while ignoring other settings
    has_store_access = models.BooleanField(
        null=True,
        blank=True,
        default=False,
        help_text="Only use for testing, will give store access regardless of other settings",
    )

    # this field is used for disabling access for a customer while ignoring other settings
    store_access_disabled = models.BooleanField(
        null=True,
        blank=True,
        default=False,
        help_text="Will disable access to the store regardless of other settings",
    )

    referrals_enabled = models.BooleanField(
        null=True,
        blank=True,
        default=False,
        help_text="If True, the customer gets access to the Referral section in the accounts menu",
    )

    sg_price_plan_preference = models.CharField(
        max_length=12,
        choices=PRICE_PLAN_PREFERENCE,
        null=True,
        blank=True,
        help_text="let the customer pick a sheeep goat price preference and if allowed this is what will be used",
    )
    sg_price_plan_quantity_preference = models.IntegerField(
        null=True,
        blank=True,
        help_text="let the customer decide the quantity of sheep goat collars for their subscription.",
    )
    c_price_plan_preference = models.CharField(
        max_length=12,
        choices=PRICE_PLAN_PREFERENCE,
        null=True,
        blank=True,
        help_text="let the customer pick a cattle price preference and if allowed this is what will be used",
    )
    c_price_plan_quantity_preference = models.IntegerField(
        null=True,
        blank=True,
        help_text="let the customer decide the quantity of cattle collars for their subscription.",
    )
    price_plan_custom_text_field = models.CharField(
        max_length=512,
        blank=True,
        help_text=_("Let the customer include custom information to the order, such as a purchase order number."),
    )

    nofence_contact = models.ForeignKey(
        User,
        SET_NULL,
        null=True,
        blank=True,
        related_name="nofence_contact",
        help_text="If this customer is handled manually, who is our contact?",
    )

    objects = CustomerManager()

    tracker = FieldTracker(
        fields=[
            "customer_status",
            "country",
            "is_part_of_debt_collection_flow",
            "phone_number",
            "phone_number_mobile",
            "approved_for_post_payment",
        ],
    )

    # Easier access to related one to one addresses.
    company_address_value = RelatedObjectOrNone("company_address")
    invoice_address_value = RelatedObjectOrNone("invoice_address")
    delivery_address_value = RelatedObjectOrNone("delivery_address")
    suggested_proposal = models.ForeignKey(
        "SalesOrder",
        SET_NULL,
        null=True,
        blank=True,
        related_name="suggested_proposal_for_customer",
    )

    # NOTE: The main contact email is the User.email field.
    invoice_contact_email = own_fields.EmailField(default="", blank=True)
    delivery_contact_email = own_fields.EmailField(default="", blank=True)

    # Used by the PricePlanLevelNotifier to keep track of the last notified collar count
    # to not send repeated notifications and to only send notification when the level changes.
    price_plan_level_notification_last_collar_count = models.IntegerField(
        null=True,
        blank=True,
        help_text=(
            "Internal value used for price plan level notifications."
            " Don't edit unless you know what your doing. <i><b>Blank and 0 have different meanings.</b></i>"
        ),
    )

    price_cap_notification_last_reached_caps = models.PositiveIntegerField(
        default=0,
        help_text=(
            "Internal value used for reached price caps notifications. Don't edit unless you know what your doing."
        ),
    )

    # If null, the purchase order update flow is disabled.
    purchase_order_flow = models.CharField(
        max_length=300,
        null=True,
        blank=True,
        choices=PurchaseOrderFlow.choices,
    )

    # If true, customer will get debt collection warning on the second payment reminder.
    is_part_of_debt_collection_flow = models.BooleanField(
        default=False,
        help_text=(
            "If True, customer will get debt collection warning on the second payment reminder. "
            "This is only possible for VISMA customers at the moment. "
            "Note: If changed from enabled -> disabled, any active debt collections will be cancelled."
        ),
    )

    # If true, we skip the deal validation of number of collars
    is_allowed_to_buy_few_collars = models.BooleanField(
        default=False,
        help_text=(
            "If True, customer will be allowed to buy less than 5 collars (in addition to what he already has). "
            "If False, a new collar deal (in addition to already owned collars) needs to be 5 or more per type. "
            "Should only be set to True in special cases!"
        ),
    )

    can_use_direct_debit = models.BooleanField(
        default=False,
        help_text=("If True, customer will be able to enable and use direct debit"),
    )

    can_use_card_payment = models.BooleanField(
        null=True,
        blank=True,
        help_text=(
            "If set this will override the card payment access for this customer. "
            "By default card payments are enabled or disabled for some "
            "nofence companies in the code."
        ),
    )

    is_nofence_company_shipment_receiver = models.BooleanField(
        default=False,
        help_text=(
            "Signifies that the customer is a special customer that represents a Nofence Company. "
            "This is used to pick up shipment of stock between warehouses in different Nofence Companies."
        ),
    )

    customer_relationship_terminated = models.DateField(
        null=True,
        blank=True,
        help_text=(
            "If set, customer is not allowed to use our systems anymore. "
            "Warnings will show up in the app, and the customer will not be able to use the collars."
        ),
    )

    customer_gdpr = models.CharField(
        max_length=300,
        null=True,
        blank=True,
        help_text=(
            "This value is synced from hubspot the customer_gdpr company property in hubspot. Don't edit in billy"
        ),
    )

    confirmed_no_interest = models.BooleanField(
        null=True,
        blank=True,
        help_text=(
            "This value is synced from hubspot the confirmed_no_interest company property in hubspot."
            " Don't edit in billy"
        ),
    )

    number_of_faulty_batteries = models.IntegerField(
        default=0,
        help_text="Number of faulty batteries for this customer",
        verbose_name="Number of faulty batteries",
    )

    filled_out_faulty_batteries_request = models.BooleanField(
        default=False,
        null=True,
        blank=True,
        help_text=("This value is set to True when the customer has filed out a request for faulty batteries."),
    )

    previously_uk = models.BooleanField(
        default=False,
        help_text=(
            "This is a temporary field to check if the customer belonged to nofence_uk. "
            "Useful when migrating Ireland customers to nofence_es"
        ),
    )

    vies_request_payload = models.JSONField(default=dict, blank=True, null=True)
    vies_response = models.JSONField(default=dict, blank=True, null=True)
    vies_valid = models.BooleanField(default=False, blank=True, null=True)

    fence_definition_version = models.IntegerField(null=True, blank=True)
    feature_flags = models.JSONField(default=dict, null=True, blank=True)

    approved_for_post_payment = models.BooleanField(
        default=False,
        help_text=(
            "If checked, all new order from this customer will automatically "
            "be created as post payment orders, the customer will not be able to "
            "choose a different payment method."
        ),
    )

    approved_for_post_payment_by = models.ForeignKey(
        settings.AUTH_USER_MODEL,
        PROTECT,
        null=True,
        blank=True,
        help_text="The user that approved this customer for post payment",
        related_name="customers_approved_for_post_payment",
        # Don't create the default index for foreign keys, we add the same index manually
        # to be able to add the indexes concurrently to not cause locking.
        # This is also done for the history model.
        # See the Meta and HistoryMeta classes.
        db_index=False,
    )

    def __str__(self):
        return f"{self.name}"

    def clean(self):
        if not self.automatic_invoice and not self.nofence_contact:
            if not self.user.is_staff:
                raise ValidationError({"nofence_contact": "Contact required when disabling invoicing unless staff"})

        if self.nofence_contact and not self.nofence_contact.is_staff:
            raise ValidationError({"nofence_contact": "Contact must be staff"})

        accounting_systems = self._get_existing_accounting_system_ids() if not self._state.adding else []

        for external_id, accounting_system in accounting_systems:
            if external_id:
                exists = (
                    CustomerAccountingSystem.objects.filter(
                        customer=self,
                        nofence_company=self.nofence_company,
                        accounting_system=accounting_system,
                    )
                    .exclude(external_id=external_id)
                    .first()
                )
                if exists:
                    raise ValidationError(
                        f"This customer already has another {accounting_system} ID ({exists.external_id}) "
                        f"registered for {self.nofence_company}, see the Customer accounting systems section. "
                        "If you still want to add this new ID, please delete the old Customer accounting system entry "
                        f"before trying to change the {accounting_system} ID.",
                    )
                exists = (
                    CustomerAccountingSystem.objects.filter(
                        nofence_company=self.nofence_company,
                        accounting_system=accounting_system,
                        external_id=external_id,
                    )
                    .exclude(customer=self)
                    .first()
                )
                if exists:
                    raise ValidationError(
                        f"Another customer ({exists.customer}) already has this {accounting_system} ID "
                        f"({exists.external_id}) registered for {self.nofence_company}, see the Customer accounting "
                        "systems. If you still want to add this new ID, please remove the duplicate Customer "
                        f"accounting system entry before trying to change the {accounting_system} ID.",
                    )

    def save(self, update_fields=None, *args, **kwargs):
        if not self.pk and getattr(self, "nofence_company", None) is None:
            try:
                nofence_company = NofenceCompany.objects.get(countries__contains=self.country)
            except NofenceCompany.DoesNotExist:
                nofence_company = NofenceCompany.objects.get(code=NOFENCE_NO)
            self.nofence_company = nofence_company

        if not self.pk and (self.country == "NO" or self.country == "GB"):
            self.is_part_of_debt_collection_flow = True
        if not self.pk and self.nofence_company in [NOFENCE_ES, NOFENCE_UK]:
            self.can_use_direct_debit = True

        update_fields = set(update_fields) if update_fields else None

        status_changed = False
        country_changed = False
        if self.tracker.has_changed("customer_status"):
            self.customer_status_changed_at = timezone.now()
            status_changed = True
            if update_fields:
                update_fields.add("customer_status_changed_at")

        if self.tracker.has_changed("country"):
            country_changed = True
            if update_fields:
                update_fields.add("country")

        if self.tracker.has_changed("phone_number"):
            self.phone_number_formatted = format_phone_number(self.phone_number, self.country.code)
            if update_fields:
                update_fields.add("phone_number_formatted")

        if self.tracker.has_changed("phone_number_mobile"):
            self.phone_number_mobile_formatted = format_phone_number(self.phone_number_mobile, self.country.code)
            if update_fields:
                update_fields.add("phone_number_mobile_formatted")

        if self.pk and self.tracker.has_changed("is_part_of_debt_collection_flow"):
            if not self.is_part_of_debt_collection_flow:
                from .invoices import DebtCollection

                qs = DebtCollection.objects.filter(
                    invoice_document__customer=self,
                    status__in=[
                        DebtCollection.Status.WARNING_SENT_TO_CUSTOMER,
                        DebtCollection.Status.SENT_TO_DEBT_COLLECTION,
                    ],
                )
                for dept_collection in qs:
                    dept_collection.status = DebtCollection.Status.CANCELLED
                    dept_collection._change_reason = "debt collection flow deactivated on customer"
                    dept_collection.save()

        if self.tracker.has_changed("approved_for_post_payment"):
            if self.approved_for_post_payment:
                self.approved_for_post_payment_by = _get_current_authenticated_user()
            else:
                self.approved_for_post_payment_by = None

        super(Customer, self).save(update_fields=update_fields, *args, **kwargs)

        if status_changed:
            CustomerStatusEvent.objects.create(
                customer=self,
                status=self.customer_status,
                status_changed_at=timezone.now(),
                change_reason=getattr(self, "_customer_status_change_reason", None),
            )

            # Instead of triggering tasks directly here we could have a task
            # polling for the CustomerStatusEvents. But trying this for now.
            def sync_status_to_hubspot():
                from core.integrations.hubspot_integration import tasks

                tasks.sync_billy_customer_status_to_hubspot.delay(
                    self.id,
                )

            if self.hubspot_id:
                transaction.on_commit(sync_status_to_hubspot)

        if country_changed:

            def sync_country_to_hubspot():
                from core.integrations.hubspot_integration import tasks

                tasks.sync_billy_customer_country_to_hubspot.delay(
                    self.id,
                )

            if self.hubspot_id:
                transaction.on_commit(sync_country_to_hubspot)

        accounting_systems = self._get_existing_accounting_system_ids()

        for external_id, accounting_system in accounting_systems:
            if external_id:
                try:
                    CustomerAccountingSystem.objects.get_or_create(
                        customer=self,
                        nofence_company=self.nofence_company,
                        accounting_system=accounting_system,
                        external_id=external_id,
                    )
                except IntegrityError as e:
                    raise IntegrityError(
                        f"Duplicate ID {external_id} CustomerAccountingSystem for "
                        f"customer={self}, "
                        f"nofence_company={self.nofence_company}, "
                        f"accounting_system={accounting_system}. "
                        f"({e})",
                    )

    def _get_existing_accounting_system_ids(self) -> list[tuple[str, SalesOrderAccountingSystem]]:
        """Used by when saving the customer accounting systems.

        We only return the accounting system ids of the same type as the accounting system set
        on the current nofence company of the customer.
        """
        accounting_systems = []
        if SalesOrderAccountingSystem.is_visma_system(self.nofence_company.accounting_system):
            accounting_systems.extend(
                [
                    (self.visma_id, SalesOrderAccountingSystem.VISMA),
                    (self.visma_prepaid_id, SalesOrderAccountingSystem.VISMA_PREPAID),
                ]
            )
        elif self.nofence_company.accounting_system == SalesOrderAccountingSystem.XERO:
            accounting_systems.extend(
                [
                    (self.xero_id, SalesOrderAccountingSystem.XERO),
                ]
            )
        return accounting_systems

    def optional_user_attribute(self, attr, default=None):
        try:
            return getattr(self.user, attr)
        except Exception:
            return default

    def has_active_customer_status(self):
        """Returns true for the customer status that we consider to be active.

        That is the customer has completed account setup.
        """
        return self.customer_status is not None and self.customer_status not in [
            Customer.Status.DELETED,
            Customer.Status.UNQUALIFIED_CUSTOMER,
            Customer.Status.LOST_CUSTOMER,
            Customer.Status.LOST_PROSPECT,
            Customer.Status.PROSPECT,
            Customer.Status.PROSPECT_QUALIFIED,
            Customer.Status.PROSPECT_UNREACHABLE,
        ]

    def is_pre_approved_for_post_payment(self):
        return self.approved_for_post_payment

    @property
    def carousel_items(self):
        carousel_items = (
            CarouselItem.objects.annotate(market_count=Count("markets"))
            .filter(
                Q(markets=self.nofence_company) | Q(countries__contains=self.country),
            )
            .order_by("ordering")
        )

        has_previous_orders = self.sales_orders.filter(order_type=SalesOrder.Type.SO).exists()

        # if we are missing quick quote or the customer already
        # bought something in the past we filter out SUGGESTED_ORDER
        if not self.quick_quote or has_previous_orders:
            carousel_items = carousel_items.filter(
                ~Q(special_modal_type=CarouselItem.SpecialModalTypes.SUGGESTED_ORDER),
            )

        # also filter based on customer status to show the referral banner
        if self.has_referrals_enabled:
            carousel_items = carousel_items.exclude(
                referral_type__in=[
                    CarouselItem.ReferralType.IS_NOT_CUSTOMER_WITH_REFERRAL_CODE,
                    CarouselItem.ReferralType.IS_NOT_CUSTOMER_NO_REFERRAL_CODE,
                ],
            )
        elif self.has_referrals_restricted_access and self.has_active_referral_code:
            carousel_items = carousel_items.exclude(
                referral_type__in=[
                    CarouselItem.ReferralType.IS_CUSTOMER,
                    CarouselItem.ReferralType.IS_NOT_CUSTOMER_NO_REFERRAL_CODE,
                ],
            )
        elif self.has_referrals_restricted_access and not self.has_active_referral_code:
            carousel_items = carousel_items.exclude(
                referral_type__in=[
                    CarouselItem.ReferralType.IS_CUSTOMER,
                    CarouselItem.ReferralType.IS_NOT_CUSTOMER_WITH_REFERRAL_CODE,
                ],
            )
        return carousel_items

    @property
    def has_active_referral_code(self):
        from core.models.referrals import CustomerReferral, ReferralStatus

        return CustomerReferral.objects.filter(
            status=ReferralStatus.ACTIVE,
            email__iexact=self.email,
            expiry_date__gte=timezone.now().date(),
        ).exists()

    @property
    def quick_quote(self):
        from core.models.quickquote import QuickQuote

        return (
            QuickQuote.objects.filter(
                email=self.email,
                number_of_animals__isnull=False,
                animal__isnull=False,
            )
            .order_by("-created_at")
            .first()
        )

    @property
    def auto_qualify_cohort_qq(self):
        from core.models.quickquote import QuickQuote

        return (
            self.quick_quotes.filter(
                status=QuickQuote.Status.COMPLETED,
                auto_qualified__isnull=False,
            )
            .order_by("-created_at")
            .first()
        )

    @property
    def has_referrals_restricted_access(self):
        return (
            self.customer_status is not None
            and self.customer_status
            not in [
                Customer.Status.NEW_CUSTOMER,
                Customer.Status.CUSTOMER_Y2,
            ]
            and (
                self.referrals_enabled
                or (
                    self.nofence_company.referrals_enabled
                    and (
                        not self.nofence_company.referrals_enabled_for_countries
                        or self.country in self.nofence_company.referrals_enabled_for_countries
                    )
                )
            )
        )

    @property
    def has_referrals_enabled(self):
        return (
            self.customer_status is not None
            and self.customer_status
            in [
                Customer.Status.NEW_CUSTOMER,
                Customer.Status.CUSTOMER_Y2,
            ]
            and (
                self.referrals_enabled
                or (
                    self.nofence_company.referrals_enabled
                    and (
                        not self.nofence_company.referrals_enabled_for_countries
                        or self.country in self.nofence_company.referrals_enabled_for_countries
                    )
                )
            )
        )

    @property
    def invoice_requester(self):
        first_name = self.invoice_requester_first_name or self.name or ""
        last_name = self.invoice_requester_last_name or ""
        if not first_name and not last_name:
            return None
        if first_name and not last_name:
            return first_name
        return f"{first_name} {last_name}"

    @property
    def is_active(self):
        return self.optional_user_attribute("is_active")

    @is_active.setter
    def is_active(self, is_active):
        self.user.is_active = is_active
        self.user.save()

    @property
    def email(self):
        return self.optional_user_attribute("email")

    @email.setter
    def email(self, value):
        self.user.email = value
        self.user.save()

    @property
    def vies_date(self):
        if self.vies_response:
            return self.vies_response.get("requestDate", None)
        return None

    @property
    def valid_vat_status(self) -> bool:
        if self.country == "IE":
            if self.nofence_company == NOFENCE_ES:
                return True if self.business_reg_no or self.flat_rate_scheme else False

        if self.country == "US":
            return True

        return True if self.business_reg_no else False

    @property
    def flat_rate_scheme_applicable(self):
        return self.country == "IE"

    @property
    def is_authenticated(self):
        """
        Always return True. This is a way to tell if the user has been
        authenticated in templates.
        """
        return True

    @property
    def is_staff(self) -> bool:
        return bool(self.optional_user_attribute("is_staff"))

    @property
    def vat_identification_number(self):
        if self.country == "NO" and not self.business_reg_no.endswith("MVA"):
            return f"{self.business_reg_no}MVA"
        return self.business_reg_no

    @property
    def has_invoice_overdue(self) -> bool:
        # This property is used by the app. We updated to only be true if overdue by more than 6 days.
        # In order to not require an app update we cannot change the name of the property.
        return self.overdue_invoices.exists()

    @property
    def overdue_invoices(self) -> QuerySet:
        return self.invoice_documents.is_overdue_by(days=6)

    @property
    def has_unpaid_invoices(self) -> bool:
        return self.invoice_documents.is_unpaid().exists()

    @property
    def can_see_getting_started_menu(self):
        # don't show the getting_started section in account page if the user's first login date is older than 30 days
        if not self.user.first_login_date_after_onboarding_page:
            return True

        return self.user.first_login_date_after_onboarding_page > timezone.now() - timedelta(days=30)

    @property
    def total_balance(self) -> str:
        # This property is used by the app. We updated to only count invoices overdue by more than 6 days.
        # In order to not require an app update we cannot change the name of the property.
        balance = self.invoice_documents.is_overdue_by(days=6).total_balance_sums()
        if not balance.has_any_amount():
            return None
        balance = ", ".join(map(str, balance.values()))
        return balance

    def get_app_users(self, active_only: bool = True):
        from core.models.appusers import AppUser  # importing locally due to circular import error

        user_account_access = UserAccountAccess.objects.filter(
            customer=self,
            type__in=UserAccountAccess.app_users_permissions,
        ).all()

        users_list = []
        for permission in user_account_access:
            permission: UserAccountAccess
            if active_only:
                if permission.user.is_active_with_password():
                    users_list.append(permission.user)
            else:
                users_list.append(permission.user)
        return AppUser.objects.filter(user__in=users_list)

    @property
    def has_debt_collection(self) -> bool:
        # See if we find any unpaid invoices with debt collection sent to debt collector
        return (
            self.invoice_documents.is_unpaid()
            .filter(
                invoice_debt_collection__isnull=False,
                invoice_debt_collection__status__in=["SENT_TO_DEBT_COLLECTION"],
            )
            .exists()
        )

    @property
    def has_debt_collection_warning(self) -> bool:
        # See if we find any unpaid invoices with debt collection warning sent to customer
        return (
            self.invoice_documents.is_unpaid()
            .filter(
                invoice_debt_collection__isnull=False,
                invoice_debt_collection__status__in=["WARNING_SENT_TO_CUSTOMER"],
            )
            .exists()
        )

    @property
    def oldest_unpaid_invoice_due_date(self) -> Optional[date]:
        # Used by app on terminated customers.
        # See if we find any unpaid invoices, and get the due date of the oldest one
        min_date = self.invoice_documents.is_unpaid().aggregate(first_date=Min("due_date"))
        return min_date["first_date"] or None

    @property
    def has_orders_pending_payment(self) -> bool:
        # See if we find any unpaid invoices linked to sales orders
        return (
            self.invoice_documents.is_unpaid()
            .filter(
                sales_order__isnull=False,
                sales_order__customer=self,
            )
            .exclude(
                sales_order__status__in=[
                    SalesOrderStatus.DELETED,
                ],
            )
            .exists()
        )

    @property
    def help_center_url(self) -> Optional[str]:
        return self.nofence_company.get_url(self.country, NofenceCompany.URLType.HELP_CENTER)

    @property
    def feedback_url(self) -> Optional[str]:
        return self.nofence_company.get_url(self.country, NofenceCompany.URLType.FEEDBACK)

    @property
    def collar_not_reporting_url(self) -> Optional[str]:
        return self.nofence_company.get_url(self.country, NofenceCompany.URLType.COLLAR_NOT_REPORTING)

    @property
    def beacon_info_url(self) -> Optional[str]:
        return self.nofence_company.get_url(self.country, NofenceCompany.URLType.BEACON_INFO)

    @property
    def cattle_forum_url(self) -> Optional[str]:
        return self.nofence_company.get_url(self.country, NofenceCompany.URLType.CATTLE_FORUM)

    @property
    def sheep_and_goat_forum_url(self) -> Optional[str]:
        return self.nofence_company.get_url(self.country, NofenceCompany.URLType.SHEEP_AND_GOAT_FORUM)

    @property
    def sales_page_url(self) -> Optional[str]:
        return self.nofence_company.get_url(self.country, NofenceCompany.URLType.SALES_PAGE)

    @property
    def sales_contact_us_email(self) -> Optional[str]:
        return self.nofence_company.get_url(self.country, NofenceCompany.URLType.SALES_CONTACT_US_EMAIL)

    @property
    def sales_contact_us_phone(self) -> Optional[str]:
        return self.nofence_company.get_url(self.country, NofenceCompany.URLType.SALES_CONTACT_US_PHONE)

    @property
    def support_contact_us_phone(self) -> Optional[str]:
        return self.nofence_company.get_url(self.country, NofenceCompany.URLType.SUPPORT_CONTACT_US_PHONE)

    @property
    def show_solar_storm_info(self) -> bool:
        return AppAlert.objects.filter(
            active=True,
            type=AppAlert.Type.SOLAR_STORM,
            countries__contains=self.country,
        ).exists()

    @property
    def show_collar_network_issue_info(self) -> bool:
        return AppAlert.objects.filter(
            active=True,
            type=AppAlert.Type.NETWORK_ISSUE,
            countries__contains=self.country,
        ).exists()

    @property
    def support_phone_number(self) -> Optional[str]:
        return self.nofence_company.support_contact_us_phone

    @property
    def support_email(self) -> Optional[str]:
        return "<EMAIL>"

    @property
    def latest_invoice_document(self):
        invoice_documents = self.invoice_documents.filter(
            sales_order__isnull=False,
            sales_order__customer=self,
        ).exclude(
            sales_order__status__in=[
                SalesOrderStatus.DELETED,
            ],
        )
        if not invoice_documents.exists():
            return None
        return invoice_documents.latest("created_at")

    @property
    def latest_proposal(self):
        return self.sales_orders.filter(order_type=SalesOrderStatus.PROPOSAL).latest("created_at")

    @property
    def collar_count(self) -> int:
        return self.owned_collars.count()

    def is_card_payment_enabled_in_store(self) -> bool:
        if self.can_use_card_payment is not None:
            return self.can_use_card_payment
        return self.nofence_company in [NOFENCE_US, NOFENCE_NO, NOFENCE_UK]

    def is_card_payment_enabled_for_invoices(self) -> bool:
        if self.can_use_card_payment is not None:
            return self.can_use_card_payment
        return self.nofence_company in [NOFENCE_US, NOFENCE_UK]

    def has_setup_stripe_off_session_payments(self) -> bool:
        return bool(self.stripe_default_payment_method)

    def get_available_stripe_payment_methods(self):
        from .cardpayments import StripePaymentMethod

        payment_methods: list[StripePaymentMethod] = list(self.stripe_payment_methods.order_by("-id").all())
        return payment_methods

    def get_latest_terms_and_conditions(self) -> "TermsAndConditions":
        return self._get_latest_terms_and_condition_by_type(TermsAndConditions.Type.TERMS_AND_CONDITIONS)

    def get_latest_privacy_policy(self) -> "TermsAndConditions":
        return self._get_latest_terms_and_condition_by_type(TermsAndConditions.Type.PRIVACY_POLICY)

    def get_latest_warranty_terms(self) -> "TermsAndConditions":
        return self._get_latest_terms_and_condition_by_type(TermsAndConditions.Type.WARRANTY)

    def _get_latest_terms_and_condition_by_type(self, type: "TermsAndConditions.Type") -> "TermsAndConditions":
        return TermsAndConditions.objects.filter(
            type=type,
            nofence_company=self.nofence_company,
        ).latest("created_at")

    def get_usage_collar_count(self, product: str = None) -> int:
        """Returns the current number of collars in use by the customer

        THis also takes into account replacement collars from return and replacement orders
        """
        from .returns import CollarReplacement

        if product is not None:
            count = self.usage_collars.of_type(product).count()
        else:
            count = self.usage_collars.count()

        replacement_collars = CollarReplacement.objects.count_replacement_collar_count_for_customer(
            self,
            product=product,
        )

        # Should normally never become negative, but just in case there are any race conditions we make sure we
        # don't return a negative number.
        return max(0, count - replacement_collars)

    def get_billable_owned_collar_count(self, product: str = None) -> int:
        """Returns the current number of owned collars by the customer

        THis also takes into account replacement collars from return and replacement orders
        """
        from .returns import CollarReplacement

        if product is not None:
            count = self.owned_collars.of_type(product).count()
        else:
            count = self.owned_collars.count()

        replacement_collars = CollarReplacement.objects.count_replacement_collar_count_for_customer(
            self,
            product=product,
        )

        # Should normally never become negative, but just in case there are any race conditions we make sure we
        # don't return a negative number.
        return max(0, count - replacement_collars)

    def get_total_collars_without_subscriptions(self, product: PRODUCT_TYPE = None):
        return max(
            0,
            (
                self.get_billable_owned_collar_count(product=product)
                - self.get_total_pre_paid_subscriptions_count(product=product)
            ),
        )

    @property
    def flexible_price_plan_price(self) -> Optional[Money]:
        return self.get_flexible_price_plan_price_for_collar_count(self.get_billable_owned_collar_count())

    def get_preferred_language_code(self) -> str:
        return settings.DEFAULT_COUNTRY_LANGUAGE_CODES.get(self.country, settings.LANGUAGE_CODE)

    def activate_preferred_translation_language(self):
        """Context manager used to activate the i18n language for the customer.

        This is useful in situations where we want to translate some content in tasks/jobs
        that are not directly initiated by users request
        """
        return translation.override(self.get_preferred_language_code())

    def get_flexible_price_plan_price_for_collar_count(self, collar_count: int) -> Optional[Money]:
        is_t2 = collar_count >= self.T2_PRICE_PLAN_THRESHOLD
        if self.nofence_company == NOFENCE_NO and self.country == "NO":
            return Money("60", "NOK") if is_t2 else Money("85", "NOK")
        elif (
            self.nofence_company == NOFENCE_ES
            or (self.nofence_company == NOFENCE_NO and self.country == "ES")
            or (self.nofence_company == NOFENCE_UK and self.country == "IE")
        ):
            return Money("4.50", "EUR") if is_t2 else Money("6.50", "EUR")
        elif self.nofence_company == NOFENCE_UK:
            return Money("3.50", "GBP") if is_t2 else Money("5.00", "GBP")
        elif self.nofence_company == NOFENCE_US:
            return Money("4.50", "USD") if is_t2 else Money("6.50", "USD")
        return None

    def get_price_level_for_collar_count(self, collar_count: int) -> str:
        return self.PRICE_LEVEL_T1 if collar_count < Customer.T2_PRICE_PLAN_THRESHOLD else self.PRICE_LEVEL_T2

    @property
    def flexible_price_plan_migration_date(self) -> Optional[date]:
        min_date = self.subscriptionchanges.filter(
            price_plan__type__in=[FIXED, VARIABLE, LEASING],
            end_date__gte=date.today(),
        ).aggregate(first_date=Min("end_date"))

        return min_date["first_date"] or date(2023, 2, 28)

    @property
    def price_plan_company_code(self):
        """Used to select price plan image on price plan migration page"""
        if self.nofence_company == NOFENCE_NO and self.country == "ES":
            return "ES"
        if self.country == "IE":
            return "IE"
        return self.nofence_company.code

    @property
    def price_plan_img(self):
        if self.country != "ES" and self.country in settings.COUNTRIES_EUROZONE:
            return "account/img/price-plan-EU.png"

        return f"account/img/price-plan-{self.price_plan_company_code}.png"

    @property
    def has_bought_from_store(self):
        return self.sales_orders.filter(
            status=SalesOrderStatus.DELIVERED,
            source=SalesOrderSource.WEBSHOP,
        ).exists()

    @property
    def has_orders_in_progress(self):
        return self.sales_orders.filter().exists() or self.user.baskets.filter().exists()

    @property
    def flat_rate_scheme_allowed(self):
        return (
            self.country
            in [
                "IE",
            ]
            and self.nofence_company.code == NOFENCE_ES
        )

    def has_sync_failed(self):
        from core.models import SyncStatus, TaskType

        excluded_types = [
            TaskType.BILLY_CUSTOMER_LATEST_QUICK_QUOTE_TO_HUBSPOT_SYNC,
            TaskType.BILLY_CUSTOMER_TO_HUBSPOT_COMPANY_SYNC,
            TaskType.BILLY_CUSTOMER_TO_HUBSPOT_PRIMARY_CONTACT_SYNC,
        ]

        qs = SyncStatus.objects.filter(
            customer=self,
            status=SyncStatus.Status.FAILED,
        ).exclude(task_type__in=excluded_types)

        return qs.exists()

    def get_total_pre_paid_subscriptions_count(self, at_time: date = None, product: str = None) -> int:
        """Returns the total number of pre paid subscriptions for the customer.

        This also counts the old FIXED types as prepaid
        """
        at_time = at_time or date.today()
        return self.get_active_subscription_count(at_time, plan_types=[PREPAID, FIXED, LEASING], product=product)

    def get_total_subscriptions_count(self, at_time: date = None) -> int:
        """Returns the total number of subscriptions for user

        This also includes subscriptions in open orders.
        """
        at_time = at_time or date.today()

        active_subscriptions = self.get_active_subscription_count(at_time)

        subscriptions_in_orders = self.get_subscription_count_in_open_orders(at_time)

        return active_subscriptions + subscriptions_in_orders

    def get_active_subscription_count(
        self,
        at_time: date = None,
        product: PRODUCT_TYPE = None,
        plan_types: List[str] = None,
    ) -> int:
        """Returns the total number of active subscriptions for user"""
        at_time = at_time or timezone.now()

        active_subscriptions = (
            self.get_active_subscriptionchanges(at_time, product=product, plan_types=plan_types).aggregate(
                Sum("quantity_delta"),
            )["quantity_delta__sum"]
            or 0
        )

        return active_subscriptions

    def get_active_subscriptions_in_month(
        self,
        month: date,
        product: PRODUCT_TYPE = None,
        plan_types: List[str] = None,
    ) -> int:
        """Return the max number of subscription that was active at the same time in the specified month"""

        end_of_month = month.replace(day=1) + relativedelta(months=1, days=-1)

        # We assume that all subscriptions will always be ending at the last day of a month,
        # so counting the number of active subscription at the last day of the month should
        # give us the correct number.
        return self.get_active_subscription_count(
            end_of_month,
            product=product,
            plan_types=plan_types,
        )

    def get_subscription_count_in_open_orders(
        self,
        at_time: date,
        product_type: Optional[Union[CATTLE, SHEEP_GOAT]] = None,
    ):
        """Returns the number of subscriptions in open orders that was created in the
        season represented by the specified at_time.

        Optionally filtered by product type.
        """

        grazing_season = get_grazing_season_from_timestamp(at_time)

        subscription_skus_qs = PricePlan.objects

        if product_type is not None:
            subscription_skus_qs = subscription_skus_qs.filter(product=product_type)

        subscription_skus = subscription_skus_qs.values_list("sku", flat=True)

        subscriptions_in_orders = (
            self.sales_orders.filter(
                # We only includes order to be delivered in the season
                estimated_delivery_date__gte=grazing_season.start_time,
                estimated_delivery_date__lt=grazing_season.end_time_exclusive,
                # We need to include pending delivery to get the right number when
                # an order moved from confirmed to pending delivery. The subscriptions
                # added to other orders in pending delivery will be removed by the
                # subscriptionchange__isnull check below.
                status__in=[
                    SalesOrderStatus.CONFIRMED,
                    SalesOrderStatus.PENDING_PAYMENT,
                    SalesOrderStatus.PENDING_DELIVERY,
                ],
                lines__sku__in=subscription_skus,
                lines__status=SalesOrderLineStatus.ACTIVE,
            )
            .filter(
                # Skip the order if it already have a subscription change as we assume
                # that it will be counted in the active subscriptions.
                # This should normally not happen, but it may happen if an order changes status
                # from pending delivery back to confirmed, or if someone adds a subscription
                # change manually.
                ~Exists(SubscriptionChange.objects.filter(sales_order=OuterRef("pk"))),
            )
            .aggregate(in_order_sum=Sum("lines__quantity"))
            .get("in_order_sum", 0)
            or 0
        )

        return subscriptions_in_orders

    def get_total_subscriptions_discount(self, at_time: date = None) -> Decimal:
        """Calculates the current total discount the customer should have on subscriptions

        The discount is calculated from the total sum of current active subscriptions in the current
        season + subscriptions in active order with status confirmed where that order was confirmed
        in the current season.
        """
        return self._get_discount(self.get_total_subscriptions_count(at_time))

    def _get_discount(self, subscription_count: int) -> Decimal:
        # discount is calculated from 10 subs up, 0.5% per collar capped at 50%
        return min(50, max(subscription_count - 10, 0) * Decimal("0.5"))

    def get_active_subscriptionchanges(
        self,
        at_time: date,
        product: PRODUCT_TYPE = None,
        plan_types: List[str] = None,
    ) -> QuerySet:
        subs = self.subscriptionchanges.filter(
            start_date__lte=at_time,
            end_date__gte=at_time,
        )

        if product:
            subs = subs.filter(price_plan__product=product)
        if plan_types:
            subs = subs.filter(price_plan__type__in=plan_types)

        return subs

    def get_suggested_subscription_quantity(self, product_type: PRODUCT_TYPE):
        """
        Returns the suggested number of subscriptions for the next season.
        """
        active_subs = self.get_active_subscriptions(product_type, [VARIABLE, FIXED])
        num_collars = self.owned_collars.of_type(product_type).count()
        collars_except_leasing = max(num_collars - self.get_active_leasing_subscriptions(product_type), 0)
        if active_subs == 0:
            return collars_except_leasing
        return min(active_subs, collars_except_leasing)

    def get_monthly_collar_usage_summary(self, month: date, lock=False) -> Optional["CollarUsageMonthly"]:
        month = month.replace(day=1)
        try:
            if lock:
                return self.collarusagemonthly.select_for_update().get(month=month)
            return self.collarusagemonthly.get(month=month)
        except CollarUsageMonthly.DoesNotExist:
            return None

    def get_flexible_billing_free_usage_cap_for_month(self, month: date) -> int:
        """Returns the number of free usage collars for the customer at the given month

        The usage cap is the max number of collars used for a minimum of 8 months in the given
        year starting 1st of January each year.
        """

        if month < date(2023, 1, 1):
            # Price caps was only added in 2023, so no need to check old dates
            return 0

        month = month.replace(day=1)
        if month.month < 9:
            # Shortcut, as the earliest possible month for reaching the cap is September.
            return 0

        year = month.replace(month=1)
        return (
            self.collarusagemonthly.filter(
                month__gte=year,
                month__lt=month,
            )
            .values_list("flexible_usage", flat=True)
            .order_by(
                "-flexible_usage",
            )[7:]
            .first()
            or 0
        )

    def find_collar_usage_summaries_in_month(self, month: date, product: str = None) -> QuerySet:
        month = month.replace(day=1)
        summaries = self.collarusagesummaries.filter(date__gte=month, date__lt=month + relativedelta(months=1))

        if product is not None:
            summaries = summaries.filter(product=product)

        return summaries

    def get_last_invoiced_flexible_usage_month(self) -> Optional[date]:
        return InvoicedUsage.objects.filter(
            usage__customer=self,
        ).aggregate(
            max_month=Max("usage__month"),
        )["max_month"]

    def can_use_purchase_order_numbers(self) -> bool:
        """Purchase order numbers are currently available in all countries, except NO"""
        return self.nofence_company != NOFENCE_NO

    def is_invoice_purchase_order_flow_enabled(self) -> bool:
        if self.has_active_direct_debit():
            return False
        return (
            self.nofence_company.accounting_system == SalesOrderAccountingSystem.XERO
            and self.customer_type == Customer.Type.BUSINESS
            and self.purchase_order_flow is not None
        )

    def get_initial_purchase_order_flow_state(self) -> "Invoice.PurchaseOrderFlowState":
        if self.purchase_order_flow == Customer.PurchaseOrderFlow.DRAFT_FLOW:
            return Invoice.PurchaseOrderFlowState.DRAFT
        return Invoice.PurchaseOrderFlowState.SUBMITTED

    def get_max_collar_usage_in_month(
        self,
        month: date,
        product: str = None,
        invoiced_only=False,
    ) -> Tuple[Optional[date], int]:
        month = month.replace(day=1)
        return self.get_max_collar_usage_in_date_range(
            month,
            month + relativedelta(months=1),
            product=product,
            invoiced_only=invoiced_only,
        )

    def get_max_collar_usage_in_date_range(
        self,
        start_date: date,
        end_date: date,
        product: str = None,
        invoiced_only=False,
    ) -> Tuple[Optional[date], int]:
        """Returns the max number of used collars on 4 consecutive day between start and end date.

        start_date inclusive, end_date exclusive

        if invoiced_only is True, only invoiced usage will be checked.

        We look for the highest usage in the period. We define usage as a minimum of 4 consecutive days.

        We sort the usage by date, and use a 4-day sliding window to move through the set. If the 4 days in the
        window are consecutive we have usage, and we use the lowest common value of the 4 as the usage for the window,
        and we keep track of the highest value we've seen in the dataset. That will be our highest usage.
        """

        summaries = self.collarusagesummaries.filter(date__gte=start_date, date__lt=end_date)

        if invoiced_only:
            summaries = summaries.filter(invoice__isnull=False)

        if product is not None:
            summaries = summaries.filter(product=product)

        summaries = (
            summaries.values("date")
            .annotate(
                total_usage=Sum("total_usage"),
            )
            .order_by("date")
        )

        num_days = 4

        stack = deque(maxlen=num_days)

        max_consecutive_usage = None, 0

        for summary in summaries:
            stack.append(summary)

            if len(stack) < num_days:
                continue

            delta = stack[num_days - 1]["date"] - stack[0]["date"]

            # if the delta is num_days - 1, the dates are consecutive and we check for max usage in the period
            if delta.days == num_days - 1:
                # We are interested in the max common value for the consecutive days, that's the min usage
                consecutive_usage = min(s["total_usage"] for s in stack)

                if consecutive_usage > max_consecutive_usage[1]:
                    max_consecutive_usage = stack[0]["date"], consecutive_usage

        return max_consecutive_usage

    def get_max_owned_collars_in_month(self, month: date, product: str = None) -> int:
        """Returns the max number of collars owned by the customer in the month represented by the date"""
        month = month.replace(day=1)
        qs = self.collarcounts.filter(date__gte=month, date__lt=month + relativedelta(months=1))
        if product is not None:
            qs = qs.filter(product=product)

        value = qs.values("date").annotate(max_count=Sum("collar_count")).order_by("-max_count").first()
        if value is not None:
            return value["max_count"] or 0
        return 0

    def get_suggested_price_plan_type(self, product_type: PRODUCT_TYPE):
        active_subs_var = self.get_active_variable_subscriptions(product_type)
        active_subs_fixed = self.get_active_fixed_subscriptions(product_type)
        if active_subs_fixed and active_subs_var:
            return FIXED
        return VARIABLE if active_subs_var else FIXED

    def get_variable_price_plan_price(self, product: PRODUCT_TYPE):
        return PricePlanPrice.objects.get(
            countries__contains=self.country,
            price_plan__product=product,
            price_plan__type=VARIABLE,
        )

    def get_price_plan_price(self, product: PRODUCT_TYPE):
        """based on the customers data work out which price plan price we should use for new subs"""

        price_plan_sku = self.get_price_plan_sku(product)

        # now that we know what type of price plan we should use we need to return the correct price
        return PricePlanPrice.objects.get(
            countries__contains=self.country,
            price_plan__sku=price_plan_sku,
        )

    def get_price_plan_sku(self, product: str) -> str:
        """based on the customers data work out which price plan sku we should use for new subs"""

        plan_skus = {
            CATTLE: {
                FIXED: "C-fixed-annualfee",
                VARIABLE: "C-var-annualfee",
            },
            SHEEP_GOAT: {
                FIXED: "SG-fixed-annualfee",
                VARIABLE: "SG-var-annualfee",
            },
        }

        # customer is NORMAL, meaning 10plus price plans. For these plans there are fixed and variable options.
        return plan_skus[product][self.get_price_plan_preference(product)]

    def get_price_plan_preference(self, product_type):
        if self.nofence_company != NOFENCE_NO:
            raise RuntimeError("Price plan selections is only available for Nofence AS")

        """Returns the customer's preferred normal price plan type"""
        if product_type == CATTLE and self.c_price_plan_preference is not None:
            return self.c_price_plan_preference
        elif product_type == SHEEP_GOAT and self.sg_price_plan_preference is not None:
            return self.sg_price_plan_preference

        # Fallback if preference is not set.
        # The default is FIXED (Harald), but if the customer already have VARIABLE we'll use VARIABLE.
        if self.subscriptions.filter(
            price_plan__type=VARIABLE,
            price_plan__product=product_type,
            quantity__gt=0,
        ).exists():
            return VARIABLE
        return FIXED

    def get_active_variable_subscriptions(self, product: PRODUCT_TYPE = None):
        if not product:
            return self.get_active_subscriptions(CATTLE, [VARIABLE]) + self.get_active_subscriptions(
                SHEEP_GOAT,
                [VARIABLE],
            )
        return self.get_active_subscriptions(product, [VARIABLE])

    def get_active_fixed_subscriptions(self, product: PRODUCT_TYPE = None):
        if not product:
            return self.get_active_subscriptions(CATTLE, [FIXED]) + self.get_active_subscriptions(SHEEP_GOAT, [FIXED])
        return self.get_active_subscriptions(product, [FIXED])

    def get_active_prepaid_subscriptions(self, product: PRODUCT_TYPE = None):
        if not product:
            return self.get_active_subscriptions(CATTLE, [PREPAID]) + self.get_active_subscriptions(
                SHEEP_GOAT,
                [PREPAID],
            )
        return self.get_active_subscriptions(product, [PREPAID])

    def get_active_leasing_subscriptions(self, product: PRODUCT_TYPE):
        return self.get_active_subscriptions(product, [LEASING])

    def get_active_subscriptions(self, product: PRODUCT_TYPE, plan_types=None):
        if plan_types is None:
            plan_types = [VARIABLE, FIXED, LEASING, PREPAID]
        return (
            self.subscriptions.filter(price_plan__product=product, price_plan__type__in=plan_types).aggregate(
                Sum("quantity"),
            )["quantity__sum"]
            or 0
        )

    def get_subscription_periods(self):
        if self.nofence_company == NOFENCE_NO:
            return NOFENCE_NO_PERIODS
        elif self.nofence_company == NOFENCE_UK:
            return NOFENCE_UK_PERIODS

    def refresh_subscriptions(self):
        return customer_refresh_subscriptions(self)

    def get_subscription_billing_start_datetime(self, pending_delivery_time: date) -> date:
        """Returns the billing start date of a new subscription based on the order delivery time"""
        if self.nofence_company == NOFENCE_UK:
            # the 1st the next month
            return (pending_delivery_time.replace(day=1) + timedelta(days=32)).replace(day=1)
        # For norwegian customers the start datetime should be the 1st of the starting month of the
        # current billing periode in the season (there are 3 parts see self._get_subscription_fraction_no).
        # A the moment we just return the delivery time unmodified as this should work as expected in
        # the current code. The correct price faction will be calculated correctly in the
        # get_subscription_prorated_fraction function
        return pending_delivery_time

    def get_pre_paid_subscription_end_date(self, delivery_date: date) -> date:
        """Calculates the end date of a pre paid subscription created at the delivery date

        The subscription should start the 1st of the next month and last for 1 year.
        """
        # Move the start date to the 1st and add an extra month to compensate
        return delivery_date.replace(day=1) + relativedelta(years=1, months=1, days=-1)

    def get_subscription_prorated_fraction(self, start_date: date) -> float:
        """Returns the fraction of the full subscription price the customer should pay when the
        subscription starts at the specified datetime.
        """
        if self.nofence_company == NOFENCE_UK:
            return self._get_subscription_fraction_uk(start_date)
        return self._get_subscription_fraction_no(start_date)

    def get_primary_billing_email(self) -> str:
        return self.invoice_contact_email or self.email

    def get_all_billing_email_values(self) -> List[str]:
        return [
            self.get_primary_billing_email(),
            *[e.email_address for e in self.get_additional_billing_emails()],
        ]

    def get_additional_billing_emails(self) -> List["CustomerEmail"]:
        return list(
            self.additional_emails.filter(email_type=CustomerEmail.Type.BILLING_EMAIL, subscribed=True).order_by("id"),
        )

    def _get_subscription_fraction_no(self, subscription_start: date) -> float:
        """
        For norwegian customers the season is divided into three parts.

        E.g for season 2021-2022:
        [2021-03-01 - 2021-08-01) => 1
        [2021-08-01 - 2021-11-01) => 0.5
        [2021-11-01 - 2022-03-01) => 0
        """
        if 3 <= subscription_start.month < 8:
            return 1
        elif 8 <= subscription_start.month < 11:
            return 0.5
        else:
            return 0

    def _get_subscription_fraction_uk(self, start_date: date) -> float:
        """
        For UK customers the season is divided into 1/12 parts and the fraction is calculated
        based on the number of months remaining in the season.

        E.g for season 2021-2022:
        [2021-03-01 - 2021-04-01) => 12/12
        [2021-04-01 - 2021-05-01) => 11/12
        ...
        [2022-01-01 - 2022-02-01) => 2/12
        [2022-02-01 - 2022-03-01) => 1/12
        [2022-03-01 - 2022-04-01) => 12/12
        """
        if start_date.month < 3:
            return (3 - start_date.month) / 12
        return (12 + 3 - start_date.month) / 12

    def get_nofence_account_created_at(self):
        if not self.user_no:
            return None

        """ parse the mongo object id for a creation timestamp """
        try:
            user_doc = UserDocument.objects.get(user_no=self.user_no)
            return user_doc.created_at
        except DoesNotExist:
            return "NOT_FOUND"

    def get_user_document(self) -> Optional[UserDocument]:
        if not self.user_no:
            return None
        try:
            return UserDocument.objects.get(user_no=self.user_no)
        except DoesNotExist:
            pass

    def update_customer_status(self, suggested_status: Status, reason: str = None) -> bool:
        """
        Rules: https://nofence.atlassian.net/browse/SW-2268

        Updates the customer status to the suggested status if appropriate.

        returns True if the status changed else False
        """

        # We only update the status in increasing order, we never go back to an old status
        # The numbers doesn't matter, they are just need to be ordered in the order we
        # want the statuses to flow.
        status_order = {
            Customer.Status.PROSPECT: 0,
            Customer.Status.UNQUALIFIED_CUSTOMER: 0,  # Allow unqualified customers to be qualified later
            Customer.Status.LOST_CUSTOMER: 0,  # Allow lost customer to be enabled again
            Customer.Status.LOST_PROSPECT: 0,  # Allow lost prospect to be enabled again
            Customer.Status.PROSPECT_UNREACHABLE: 0,  # Allow prospect unreachable to be qualified later
            Customer.Status.PROSPECT_QUALIFIED: 1,
            Customer.Status.PROSPECT_ACCOUNT: 2,
            Customer.Status.PROSPECT_WEBSHOP: 3,
            Customer.Status.PROSPECT_BASKET: 4,
            Customer.Status.PROSPECT_ORDER: 5,
            Customer.Status.NEW_CUSTOMER: 6,
            Customer.Status.CUSTOMER_Y2: 7,
        }

        valid_for_lost_customer = [
            Customer.Status.NEW_CUSTOMER,
            Customer.Status.CUSTOMER_Y2,
        ]

        if (
            self.customer_status == Customer.Status.LOST_CUSTOMER
            or self.customer_status == Customer.Status.LOST_PROSPECT
        ) and suggested_status not in valid_for_lost_customer:
            # We only allow lost customers to be moved to the customer statuses.
            # Without this, a lost customer logging in to the account pages, would
            # be moved back and forth between the prospect account status and
            # lost customer by the nightly status update.
            return False

        # Allow move to lost customer from any status
        if suggested_status != Customer.Status.LOST_CUSTOMER and suggested_status != Customer.Status.LOST_PROSPECT:
            existing_status_order = status_order.get(self.customer_status, None)

            if self.customer_status is None or existing_status_order is None:
                # Don't know the current level.
                return False

            suggested_status_order = status_order.get(suggested_status, None)

            if suggested_status_order is None:
                # Unknown suggested status
                return False

            if suggested_status_order < existing_status_order:
                # Not going back to a previous status
                return False

        if self.customer_status != suggested_status:
            logger.info(
                f"Updated customer {self.id} - {self.name} status from {self.customer_status} to {suggested_status}."
                f" {reason=}",
            )
            self.customer_status = suggested_status
            self._customer_status_change_reason = reason
            self._change_reason = "Upgraded customer status"
            try:
                self.save(update_fields=["customer_status", "customer_status_changed_at", "updated_at"])

            finally:
                del self._customer_status_change_reason
            return True
        return False

    def send_account_activation_flow_email(self):
        from core.default_services import create_email_service

        with self.activate_preferred_translation_language():
            create_email_service().send_simple_email(
                email_type=EmailType.ACCOUNT_CREATION_FLOW,
                to_email=self.email,
                subject=str(_("Activate your Nofence Account")),
                template_context={
                    "create_account_url": self.get_create_account_url(),
                    "title": str(_("Activate your Nofence Account")),
                    "full_name": self.user.name,
                },
                customer=self,
            )
        logger.info(f"Sent account activation flow email to {self}")

        if self.user.activation_status not in [
            User.ActivationStatus.ACTIVATED_FROM_ACCOUNT_CREATION_FLOW,
            User.ActivationStatus.ACTIVATED_FROM_BILLY,
        ]:
            self.user.activation_status = User.ActivationStatus.ACCOUNT_CREATION_EMAIL_SENT
            self.user.save()

        self.update_customer_status(Customer.Status.PROSPECT_QUALIFIED)

    def get_create_account_url(self):
        return (
            urljoin(
                settings.ACCOUNT_BASE_URL,
                reverse("account-creation:get-started"),
            )
            + f"?token={self.create_signed_access_token()}"
        )

    def create_signed_access_token(self) -> str:
        """Returns token used to authenticate customer when creating account"""
        return signing.dumps(self.id, salt="customer-signer")

    @property
    def get_number_of_faulty_batteries(self):
        from django.db.models.functions import Coalesce
        from django.db.models import Sum, Value, IntegerField

        shipped_extra_batteries = ShipmentLine.objects.filter(
            sku="C2-B2.1-20Ah",
            shipment__delivered_date__lt="2024-01-01",
            shipment__delivered_date__gt="2023-01-01",
            shipment__customer_id=self.id,
            shipment__customer__country="NO",
        ).aggregate(
            sum_extra_batteries=Coalesce(
                Sum("quantity", output_field=IntegerField()),
                Value(0, output_field=IntegerField()),
            ),
        )
        return (
            shipped_extra_batteries["sum_extra_batteries"]
            + Collar.objects.filter(
                produced_at__gt="2022-12-31",
                produced_at__lt="2024-01-01",
                model=Product.ProductModel.C25,
                owner=self.pk,
            ).count()
        )

    @staticmethod
    def get_customer_from_token(token: str, max_age: int = 60 * 60 * 24 * 30) -> Optional["Customer"]:
        try:
            customer_id = signing.loads(token, salt="customer-signer", max_age=max_age)
            customer = Customer.objects.get(id=customer_id)
            return customer
        except signing.BadSignature:
            logger.error(f"Invalid token {token} for customer")
            return None
        except Customer.DoesNotExist:
            return None

    @staticmethod
    def is_new_business_reg_no_free_for_use(business_reg_no: str, current_customer: "Customer" = None):
        if VATNumberConfig.is_duplicates_allowed(business_reg_no):
            return True
        qs = (
            Customer.objects.filter(
                business_reg_no__iexact=business_reg_no,
            )
            .exclude(
                # allow for 'UE' business registration number duplicates in Norway
                # NOTE: A customer from ES or US using the UE number will currently also block the use in Norway.
                Q(nofence_company__code=NOFENCE_NO) & Q(business_reg_no__iexact="ue"),
            )
            .exclude(
                # allow multiple customers in the UK to use the same business registration number
                Q(nofence_company__code=NOFENCE_UK),
            )
        )
        # Current customer should be passed in when editing the business reg no for a customer,
        # as we don't want to fail the validation if the number is the same as their current number.
        if current_customer is not None:
            qs = qs.exclude(
                Q(pk=current_customer.pk),
            )
        return not qs.exists()

    def is_valid_business_reg_no(self, business_reg_no) -> bool:
        # skip validation for UK customers
        if self.country == "NO":
            return no.orgnr.is_valid(business_reg_no)
        elif self.country == "ES":
            return vatin.is_valid(business_reg_no)
        elif self.country == "IE" and not self.is_staff:
            return vatin.is_valid(business_reg_no)
        return True

    def compact_business_reg_no(self, value: str | None) -> str | None:
        if not value:
            return value

        try:
            if self.country == "NO":
                return no.orgnr.compact(value)
            return vatin.compact(value)
        except Exception as e:
            logger.warning(f"Could not compact business reg number {value} for {self}, error: {e}")

        # remove whitespace
        return re.sub(r"\s+", "", value)

    def is_business_customer(self) -> bool:
        return self.customer_type == Customer.Type.BUSINESS

    def get_usage_caps(self, product, at_date):
        """
        the usage cap is calculated by checking the number of collars used per day. If the number of days goes
        over the usage cap we can use the lowest collar count as the number of caps
        """

        season = get_grazing_season_from_timestamp(at_date)
        cap = CAPS.get(product)
        res = (
            self.collarusages.filter(product=product, date__lt=at_date, date__gte=season.start_time)
            .values("date", "product")
            .annotate(total=Count("collar_id"))
            .order_by("-total")
        )[:cap]

        caps = 0
        if len(res) == cap:
            # yay - the customer has used the product over the cap,
            # now we need to figure out how many collars have crossed
            caps = res[cap - 1]["total"]

        return caps

    def get_hubspot_payment_status(self):
        """Returns the payment status value to be synced to HubSpot

        This is synced to the company property weeks_past_oldest_invoice_due_date
        on HubSpot.

        The returned values must match the values defined in the hubspot property enumeration.
        """

        oldest_unpaid_invoice = self.invoice_documents.is_overdue().aggregate(
            oldest_date=Min("due_date"),
        )["oldest_date"]

        if oldest_unpaid_invoice is not None:
            delta = timezone.now().date() - oldest_unpaid_invoice
            if delta.days > 7 * 4:
                return "4+ weeks past due date"
            elif delta.days > 7 * 2:
                return "2-4 weeks past due date"
            elif delta.days > 0:
                return "0-2 weeks past due date"
        return "No unpaid invoice past due date"

    def is_price_plan_selection_open(self) -> bool:
        now = timezone.now()
        if self.nofence_company == NOFENCE_NO:
            if now.month >= 3:
                return False
            return True

    def get_purchase_order_number_for_date(self, date: date) -> Optional["PurchaseOrderNumber"]:
        purchase_order_number = PurchaseOrderNumber.objects.filter(
            customer=self,
            start_date__lte=date,
            end_date__gte=date,
        ).first()
        return purchase_order_number

    def has_purchase_order_number(self) -> bool:
        return hasattr(self, "purchase_order_number")

    def has_active_direct_debit(self):
        return self.direct_debit_mandates.is_active().exists()

    def get_active_direct_debit_mandate(self):
        return self.direct_debit_mandates.is_active().first()

    def get_first_available_product(self, products: QuerySet["Product"]) -> Optional["Product"]:
        for product in products:
            if product.is_available_for_customer(self):
                return product

    def can_change_nofence_company(self) -> list[str]:
        """Return a list of errors

        If the list is empty, the customer can be moved.
        """
        errors = []
        if self.has_unpaid_invoices:
            errors.append("Customer has unpaid invoices")

        if self.stripe_default_payment_method:
            errors.append(
                "Customer has a saved Stripe payment method. "
                "The payment method should be removed before moving the customer. "
                "This is done from the account pages."
            )
        elif self.stripe_id:
            # Only show one of the strip errors, as we want the payment method to be cleared before
            # removing the stripe id.
            errors.append(
                "Customer has a Stripe customer id. "
                "The Stripe customer should be removed before moving the customer. "
                "This is done from the admin."
            )

        from core.models import DirectDebitMandate

        if DirectDebitMandate.objects.filter(customer=self).is_active().exists():
            errors.append(
                "Customer has an active GoCardless direct debit mandate. "
                "This should be cancelled before moving the customer. "
                "This is done from the account pages."
            )
        elif self.go_cardless_id:
            errors.append(
                "Customer has a go cardless id. "
                "This should be removed before moving the customer. "
                "This is done from the admin."
            )

        return errors

    def change_nofence_company(self, new_nofence_company: NofenceCompany) -> bool:
        old_nofence_company = self.nofence_company

        if new_nofence_company == old_nofence_company:
            return False

        new_accounting_system = new_nofence_company.accounting_system

        if SalesOrderAccountingSystem.is_visma_system(new_accounting_system):
            existing_id = self._find_existing_accounting_system_id(
                new_nofence_company, SalesOrderAccountingSystem.VISMA_PREPAID
            )
            # If the external id does not already exist, we will clear the existing id if it it is already set,
            # which is what we want, as we want a new external customer to be created.
            self.visma_prepaid_id = existing_id

        elif new_accounting_system == SalesOrderAccountingSystem.XERO:
            existing_id = self._find_existing_accounting_system_id(
                new_nofence_company, SalesOrderAccountingSystem.XERO
            )
            # If the external id does not already exist, we will clear the existing id if it it is already set,
            # which is what we want, as we want a new external customer to be created.
            self.xero_id = existing_id
        else:
            raise RuntimeError(f"Unknown accounting system {new_accounting_system}")

        self.nofence_company = new_nofence_company
        self._change_reason = "Change nofence company"
        self.save(update_fields=["nofence_company", "xero_id", "visma_prepaid_id", "updated_at"])

        from .shop import Basket

        deleted_baskets = self.user.baskets.filter(status=Basket.BasketStatus.PENDING).delete()
        logger.info(f"Deleted pending baskets when moving customer to {new_nofence_company=}. {deleted_baskets=}")

        return True

    def _find_existing_accounting_system_id(
        self, new_nofence_company: NofenceCompany, new_accounting_system: SalesOrderAccountingSystem
    ) -> str | None:
        existing = CustomerAccountingSystem.objects.filter(
            customer=self,
            nofence_company=new_nofence_company,
            accounting_system=new_accounting_system,
        ).first()

        if existing is not None:
            is_reused_external_id = CustomerAccountingSystem.objects.filter(
                customer=self,
                accounting_system=new_accounting_system,
                external_id=existing.external_id,
            ).exclude(
                nofence_company=new_nofence_company,
            )
            if is_reused_external_id:
                # This can happen if the Nofence Company on the customer has been changed
                # without clearing the visma/xero id on the customer first. In this case the
                # external id of the old nofence company would have been copied from old
                # nofence company to the new one. So we delete the existing entry, as it
                # will be the wrong value for the new nofence company.
                logger.info(
                    f"Deleting customer accounting system {existing.id=}, {existing.external_id=} for customer={self}"
                )
                existing.delete()
            else:
                # The existing external id is unique, so we assume that this is the correct id for the customer.
                # This can happen if the customer has been set to the new nofence company before.
                return existing.external_id

    @property
    def is_herdnet_enabled(self) -> bool:
        return True

    class Meta:
        indexes = [
            models.Index(fields=["user_no"]),
            models.Index(fields=["visma_id"]),
            # We override this index creation in the migration.
            models.Index(fields=["approved_for_post_payment_by"], name="idx_approved_post_pmt_by"),
        ]

        unique_together = [
            # Assumes that multiple companies will not share the same Xero account.
            ("xero_id", "nofence_company"),
        ]

    class HistoryMeta:
        add_history_speed_up_index = True

        # We override this index creation in the migration.
        field_overrides = {"approved_for_post_payment_by": {"db_index": False}}
        extra_indexes = [
            models.Index(fields=["approved_for_post_payment_by"], name="idx_hist_approved_post_pmt_by"),
        ]


class CustomerStatusEvent(BaseHistoryModel):
    customer = models.ForeignKey(Customer, on_delete=models.CASCADE, related_name="status_events")

    status = models.CharField(max_length=300, choices=CustomerStatus.choices)

    # We already have the default created_at timestamp, but for existing customers
    # we may want to insert historical records.
    status_changed_at = models.DateTimeField()

    # Optional explanation for the change. Mainly used for debugging of the nightly status update.
    change_reason = models.CharField(max_length=300, null=True, blank=True)


class CustomerAccountingSystem(BaseHistoryModel):
    """Keep track of external ids that have been linked to the customer

    When we move a customer from one nofence company to another, where both companies use
    Xero as the accouting system, the customer will need to get a new xero_id that represents
    the contact in Xero.
    When this happens we need to keep track of the old ID so that we still can link old
    resources, e.g. invoices, to the correct customer using the old xero_id that we keep
    in this model.
    """

    customer = models.ForeignKey(Customer, on_delete=models.CASCADE, related_name="accounting_system_ids")
    nofence_company = models.ForeignKey(NofenceCompany, on_delete=models.CASCADE)
    accounting_system = models.CharField(max_length=300, choices=SalesOrderAccountingSystem.choices)
    external_id = models.CharField(max_length=300)

    class Meta:
        constraints = [
            # The external id should be unique per customer
            models.UniqueConstraint(
                fields=["nofence_company", "accounting_system", "external_id"],
                name="unq_accounting_system_ids",
            ),
            # Each customer should only have one external id per accounting system
            # If neccessary we can remove this to allow a customer to have multiple
            # Ids in the same accounting system.
            models.UniqueConstraint(
                fields=["customer", "nofence_company", "accounting_system"],
                name="unq_customer_accounting_system_id",
            ),
        ]


class UserAccountAccessInviteTokenGenerator(tokens_generator.CustomTimeoutTokenGenerator):
    def get_token_timeout(self):
        return settings.ACCOUNT_INVITE_TOKEN_TIMEOUT


user_account_access_invite_token_generator = UserAccountAccessInviteTokenGenerator()


class UserAccountAccessManager(models.Manager):
    def get_from_base64_id(self, uidb64: str) -> Optional["UserAccountAccess"]:
        """Used in invite url"""
        try:
            uid = urlsafe_base64_decode(uidb64).decode()
            user_account_access = self.get(pk=uid)
        except (ValueError, UserAccountAccess.DoesNotExist):
            user_account_access = None
        return user_account_access


class UserAccountAccessQuerySet(models.QuerySet):
    def order_by_type(self):
        return self.alias(
            type_order=Case(
                When(type=UserAccountAccess.PermissionsType.PRIMARY, then=Value(0)),
                When(type=UserAccountAccess.PermissionsType.ADMIN, then=Value(1)),
                When(type=UserAccountAccess.PermissionsType.FINANCE, then=Value(2)),
                When(type=UserAccountAccess.PermissionsType.APP_ONLY, then=Value(3)),
                When(type=UserAccountAccess.PermissionsType.APP_READ_ONLY, then=Value(4)),
            ),
        ).order_by("type_order")


class UserAccountAccess(BaseHistoryModel):
    """
    Adds permissions and links User model and Customer
    """

    class PermissionsType(models.TextChoices):
        PRIMARY = "PRIMARY", _("PRIMARY ADMIN")
        ADMIN = "ADMIN", _("ADMIN")
        APP_ONLY = "APP_ADMIN", _("APP ONLY")
        APP_READ_ONLY = "APP_READ_ONLY", _("APP READ ONLY")
        FINANCE = "FINANCE", _("FINANCE")

    app_users_permissions = [str(PermissionsType.APP_ONLY), str(PermissionsType.APP_READ_ONLY)]

    type = models.CharField(max_length=16, choices=PermissionsType.choices)
    user = models.ForeignKey(User, on_delete=models.CASCADE)
    # This is actually for referencing user_no in customer, we made the decision to collapse
    # the concept of an NofenceAccount model and Customer into one model
    customer = models.ForeignKey(Customer, on_delete=models.CASCADE)
    is_accepted = models.BooleanField(default=False)

    objects: UserAccountAccessManager = UserAccountAccessManager().from_queryset(UserAccountAccessQuerySet)()

    class Meta:
        # A user can have only one permission type to each customer
        unique_together = ("user", "customer")

    def __str__(self):
        return f"{self.user.name} - {self.type} - {self.customer.name}"

    def save(self, *args, **kwargs):
        if self.type == self.PermissionsType.PRIMARY:
            existing_primary_access = UserAccountAccess.objects.filter(
                customer=self.customer,
                type=self.PermissionsType.PRIMARY,
                is_accepted=True,
            ).exclude(id=self.id)
            if existing_primary_access.exists():
                raise ValueError("There can be only one PRIMARY user for this customer.")
        super(UserAccountAccess, self).save(*args, **kwargs)

    def switch_primary_admin(self, new_primary_admin_id) -> bool:
        if self.type == self.PermissionsType.PRIMARY:
            try:
                new_primary_admin = UserAccountAccess.objects.get(
                    id=new_primary_admin_id,
                    customer=self.customer,
                    is_accepted=True,
                )
                self.type = self.PermissionsType.ADMIN
                new_primary_admin.type = self.PermissionsType.PRIMARY
                self.customer.user = new_primary_admin.user
                if not hasattr(self.user, "app_user"):
                    from .appusers import AppUser

                    AppUser.objects.create(user=self.user)
                self.customer.save()
                self.save()
                new_primary_admin.save()
                return True
            except Exception:
                logger.exception(f"Could not switch primary admin for customer {self.customer}.")
                raise
        return False

    def get_legacy_permission_mapping(self):
        if self.type in [self.PermissionsType.PRIMARY, self.PermissionsType.ADMIN, self.PermissionsType.APP_ONLY]:
            return "readwrite"
        return "read"

    def send_invitation_email(self, request):
        from core.default_services import create_email_service

        with self.customer.activate_preferred_translation_language():
            return create_email_service().send_simple_email(
                email_type=EmailType.USER_ACCOUNT_ACCESS_INVITE,
                to_email=self.user.email,
                subject=str(_("Nofence Account Invitation")),
                template_context={
                    "user_name": self.user.name if self.user.is_active else "",
                    "customer_name": self.customer.name,
                    "invited_by_email": request.user.email,
                    "user_type": self.get_type_display(),
                    "accept_url": self.create_invite_accept_url(request),
                },
                customer=self.customer,
            )

    def send_deleted_mail(self, deleted_by_user: User):
        from core.default_services import create_email_service

        with self.customer.activate_preferred_translation_language():
            return create_email_service().send_simple_email(
                email_type=EmailType.USER_ACCOUNT_ACCESS_DELETED,
                to_email=self.user.email,
                subject=str(_("Removed from Nofence account")),
                template_context={
                    "user_name": self.user.name if self.user.is_active else "",
                    "customer_name": self.customer.name,
                    "deleted_by_email": deleted_by_user.email,
                },
                customer=self.customer,
            )

    def send_getting_started_mail(self):
        from core.default_services import create_email_service

        help_center_link = self.customer.help_center_url

        with self.customer.activate_preferred_translation_language():
            create_email_service().send_simple_email(
                email_type=EmailType.GETTING_STARTED,
                to_email=self.user.email,
                subject=str(_("Getting started")),
                template_context={
                    "user_name": self.user.name,
                    "customer_name": self.customer.name,
                    "account_pages_link": urljoin(settings.ACCOUNT_BASE_URL, reverse("account:index")),
                    "help_center_link": help_center_link,
                    "download_app_link": settings.DOWNLOAD_APP_ONELINK,
                    "hide_download_app_section": self.type == self.PermissionsType.FINANCE,
                },
                customer=self.customer,
            )
        logger.info(f"Sent getting started email to {self.user}")

    def create_invite_accept_url(self, request) -> str:
        """Create a token the same ways as django's password reset function"""
        return request.build_absolute_uri(
            reverse(
                "account:users-accept-invite",
                kwargs={
                    "uidb64": self.base64_encoded_id(),
                    "token": self.create_invite_token(),
                },
            ),
        )

    def create_invite_token(self) -> str:
        return user_account_access_invite_token_generator.make_token(self.user)

    def check_invite_token(self, token: str) -> bool:
        return user_account_access_invite_token_generator.check_token(self.user, token)

    def base64_encoded_id(self) -> str:
        """Used in invite url"""
        return urlsafe_base64_encode(force_bytes(self.id))


def customer_refresh_subscriptions(customer: Customer):
    """get all the sub changes and summarise by price_plan_id"""
    totals = (
        customer.get_active_subscriptionchanges(date.today())
        .values("customer_id", "price_plan_id")
        .annotate(quantity=Sum("quantity_delta"))
    )

    """ delete all the subscriptions """
    customer.subscriptions.all().delete()

    """ build new ones """
    subs = [
        Subscription(
            customer_id=total["customer_id"],
            price_plan_id=total["price_plan_id"],
            quantity=total["quantity"],
        )
        for total in totals
    ]

    customer.subscriptions.bulk_create(subs)


class CustomerEmail(BaseHistoryModel):
    """Additional emails for customer"""

    class Type(models.TextChoices):
        BILLING_EMAIL = "BILLING_EMAIL"

    customer = models.ForeignKey(Customer, CASCADE, related_name="additional_emails")
    email_type = models.CharField(max_length=300, choices=Type.choices)
    email_address = own_fields.EmailField()
    subscribed = models.BooleanField(default=True)

    def create_signed_access_token(self) -> str:
        """Returns token use to authenticate Additional billing email access requests"""
        return signing.dumps(self.id, salt=f"billing-email-unsubscribe-{self.id}")

    def check_signed_access_token(self, token) -> bool:
        """Token is valid for 30 days"""
        try:
            return (
                signing.loads(token, salt=f"billing-email-unsubscribe-{self.id}", max_age=60 * 60 * 24 * 30) == self.id
            )
        except signing.BadSignature as e:
            logger.info(f"Invalid signed access token checked for billing email {self.email_address}: {e}")
            return False


class CustomerCollarCount(BaseModel):
    customer = models.ForeignKey(Customer, CASCADE, related_name="collarcounts")
    date = models.DateField()
    product = models.CharField(max_length=100, choices=PRODUCT_TYPE)

    # NOTE: replacement_collar_count is already subtracted from the saved collar_count value
    collar_count = models.IntegerField()

    # When a customer is assigned a replacement collar from a RR order the customer may
    # be registered as the owner of both the old and the new collar at the same time. We
    # want this to count as only owning one collar, so that it doesn't alter the customers
    # price level etc.
    # We subtract the number of replacement collar from the total count and keep track of the
    # value in case we need it for something later.
    replacement_collar_count = models.IntegerField(default=0)

    class Meta:
        unique_together = [
            ("customer", "date", "product"),
        ]


class SubscriptionChange(BaseHistoryModel):
    origin_invoice = "visma_invoice"
    origin_credit_note = "visma_credit_note"
    origin_debit_note = "visma_debit_note"
    origin_manual = "manual"
    origin_overuse = "overuse"
    origin_annual = "annual"
    origin_sales_order = "sales_order"
    ORIGINS = (
        (origin_invoice, "Visma Invoice"),
        (origin_credit_note, "Visma Credit Note"),
        (origin_debit_note, "Visma Debit Note"),
        (origin_manual, "Manual"),
        (origin_overuse, "Overuse"),
        (origin_annual, "Annual Subscription"),
        (origin_sales_order, "Sales Order"),
    )

    start_date = models.DateField(default=date.today)
    end_date = models.DateField(blank=True, null=True)

    """ we create subscription changes in 2 ways. One way is with a billy sales order, these come from hubspot.
    the second way is from manual corrections in visma or overuse / usage billing in Billy. The latter
    creates an invoice in billy and then transfers that to visma as a Sales Order for tracking. Ideally we would
    create billy sales orders for everything but the changes are significant. SW-410. We track 2 links to visma,
     one for the sales order and one for the invoice """
    visma_document_id = models.CharField(max_length=12, blank=True)
    visma_sales_order_id = models.CharField(max_length=12, blank=True)
    origin = models.CharField(max_length=30, choices=ORIGINS)
    note = models.TextField(max_length=200, blank=True)
    price_plan = models.ForeignKey(PricePlan, CASCADE)
    customer = models.ForeignKey(Customer, CASCADE, related_name="subscriptionchanges")
    invoice = models.ForeignKey("Invoice", CASCADE, null=True, blank=True, related_name="subscriptionchanges")
    sales_order = models.ForeignKey("SalesOrder", CASCADE, null=True, blank=True, related_name="subscriptionchanges")
    hubspot_id = models.CharField(max_length=12, blank=True)
    quantity_delta = models.IntegerField(default=0)

    def __str__(self):
        return f"{self.customer} - {self.quantity_delta} - {self.price_plan}"

    def clean(self):
        if self.end_date is not None:
            # Some of our tests use strings for the dates
            end_date = self.end_date if isinstance(self.end_date, date) else date.fromisoformat(self.end_date)
            tomorrow = end_date + timedelta(days=1)
            # If we are at the last day, tomorrow should be in a new month
            if end_date.month == tomorrow.month:
                if end_date.day == 1:
                    # If the end date is set to the 1st, the last of the previous month was probably the intended date
                    suggested_date = end_date + relativedelta(days=-1)
                else:
                    suggested_date = end_date.replace(day=1) + relativedelta(months=1, days=-1)
                raise ValidationError(
                    {
                        "end_date": _(
                            "End date {end_date} should be the last day of the month that the subscription "
                            "is active. Did you mean {suggested_date}?",
                        ).format(end_date=end_date, suggested_date=suggested_date),
                    },
                )

    def save(self, *args, **kwargs):
        if not self.end_date:
            """ based on the start_date we want to find the end of the season """
            start_year = self.start_date.year

            if self.start_date >= date(start_year, 3, 1):
                """ we've gone past the start of the season the start year """
                end_year = start_year + 1
                self.end_date = date(end_year, 2, calendar.monthrange(end_year, 2)[1])
            else:
                self.end_date = date(start_year, 2, calendar.monthrange(start_year, 2)[1])

        # We should probably also validate end date of subscriptions created outside of
        # the admin, but a lot of tests and other things may break. So skipping for now.

        super(SubscriptionChange, self).save(*args, **kwargs)


class Subscription(BaseModel):
    """
    We don't bother with subscription expiration and recurrence yet,
    we just model the simple Norwegian March - March subs
    """

    price_plan = models.ForeignKey(PricePlan, CASCADE, related_name="subscriptions")
    customer = models.ForeignKey(Customer, CASCADE, related_name="subscriptions")
    quantity = models.IntegerField(default=0)

    def __str__(self):
        return f"{self.customer} - {self.quantity} - {self.price_plan}"


class Invoice(BaseHistoryModel):
    STATE_DRAFT = "draft"  # Can be deleted from this state only
    STATE_QUEUED = "queued"  # Will be picked up and transferred to the accounting system
    STATE_TRANSFERRED = "transferred"  # Transferred to accounting system, cannot be changed after this has happened.
    STATE_PAID = "paid"
    STATE_FAILED = "failed"
    STATE_WRITTEN_OFF = "written_off"  # for whatever reason we've decided not to collect on this invoice
    STATES = (
        (STATE_DRAFT, "Draft"),
        (STATE_QUEUED, "Queued"),
        (STATE_TRANSFERRED, "Transferred"),
        (STATE_PAID, "Paid"),
        (STATE_FAILED, "Failed"),
        (STATE_WRITTEN_OFF, "Written off"),
    )

    class Provider(models.TextChoices):
        VISMA = "visma", "Visma"
        VISMA_PREPAID = "visma_prepaid", "Visma Prepaid"
        XERO = "xero", "Xero"

        @classmethod
        def is_visma_system(cls, self):
            return self in [cls.VISMA, cls.VISMA_PREPAID]

    # A lot of code is using these fields on the class
    VISMA = Provider.VISMA
    XERO = Provider.XERO

    class Type(models.TextChoices):
        FLEXIBLE_USAGE = "FLEXIBLE_USAGE"

    class PurchaseOrderFlowState(models.TextChoices):
        # When in draft state, the usage invoice email will not be sent
        DRAFT = "DRAFT"
        # When the draft period ends the state is updated to submitted. This
        # means that the real usage invoice is no long blocked. So the usage
        # invoice will be sent on the next invoice document update.
        # To manually enable po edits on a invoice, the state can be set to submitted.
        SUBMITTED = "SUBMITTED"

    customer = models.ForeignKey(Customer, CASCADE, related_name="invoices")
    nofence_company = models.ForeignKey(NofenceCompany, PROTECT, blank=True, null=True)
    state = models.CharField(max_length=30, choices=STATES, default=STATE_DRAFT)
    description = models.TextField(help_text="this text will be printed on the invoice in visma", blank=True)
    invoice_provider = models.CharField(max_length=100, blank=True, choices=Provider.choices)
    invoice_provider_id = models.CharField(max_length=255, blank=True)
    invoice_provider_ref = models.CharField(max_length=255, blank=True)
    note = models.TextField(help_text="this is for internal use only", max_length=255, blank=True)
    invoice_type = models.CharField(max_length=100, blank=True, null=True, choices=Type.choices)

    # Used to enable po edits and keep track of state in the purchase order number update flow
    # PO number updates are enabled if not None.
    purchase_order_flow_state = models.CharField(
        max_length=300,
        blank=True,
        null=True,
        choices=PurchaseOrderFlowState.choices,
        help_text=(
            "If not empty, po edits will be enabled. Set to submitted to manually enable edits of of issued invoices."
        ),
    )

    @property
    def currency(self):
        for line in self.invoice_lines.all():
            if line.item_price.currency:
                return str(line.item_price.currency)

    @property
    def sum_total(self):
        return sum([line.value for line in self.invoice_lines.all()])

    def is_flexible_usage_invoice(self) -> bool:
        return self.invoice_type == Invoice.Type.FLEXIBLE_USAGE

    def is_purchase_order_flow_draft(self) -> bool:
        return self.purchase_order_flow_state == Invoice.PurchaseOrderFlowState.DRAFT

    def is_purchase_order_update_enabled(self) -> bool:
        if self.invoice_provider != Invoice.XERO:
            return False
        return self.purchase_order_flow_state is not None

    def get_flexible_usage_month(self) -> Optional[date]:
        """Returns the usage month that this invoice was created from.

        This assumes that there is one invoice per usage month. In theory we
        can merge multiple usage months into a single invoice, but at the moment
        we don't do this.

        If there are multiple usage months, we don't know what month to return so
        we return None.
        """
        invoiced_usage = self.get_invoiced_usage()
        if invoiced_usage is not None:
            return invoiced_usage.usage.month

    def get_invoiced_usage(self) -> Optional["InvoicedUsage"]:
        """Returns the invoiced usage month this invoice was created from.

        This assumes that there is one invoice per usage month. In theory we
        can merge multiple usage months into a single invoice, but at the moment
        we don't do this.

        If there are multiple usage months, we don't know what month to return so
        we return None.
        """
        if self.invoice_type != Invoice.Type.FLEXIBLE_USAGE:
            return None

        invoiced_usage = self.invoiced_usage.all()
        if len(invoiced_usage) == 1:
            return invoiced_usage[0]

    def total_quantity(self, skus: List[str] = None) -> int:
        qs = self.invoice_lines
        if skus is not None:
            qs = qs.filter(sku__in=skus)

        return qs.aggregate(sum=Sum("quantity"))["sum"] or 0

    def get_invoice_document(self):
        return getattr(self, "invoice_document", None)

    def get_tax_calculation(self) -> Optional["TaxCalculation"]:
        try:
            return self.taxcalculation_set.latest("id")
        except TaxCalculation.DoesNotExist:
            pass

    def get_lines(self) -> list["InvoiceLine"]:
        return list(self.invoice_lines.order_by("id").all())

    def __str__(self):
        return f"Invoice {self.id} for {self.customer}"

    class Meta:
        indexes = [
            models.Index(fields=["invoice_provider_id"]),
        ]


class InvoiceLine(BaseHistoryModel):
    invoice = models.ForeignKey(Invoice, CASCADE, related_name="invoice_lines")
    sku = models.CharField(max_length=100)
    description = models.CharField(max_length=1000, null=True, blank=True)
    quantity = models.IntegerField()
    discount = models.DecimalField(max_digits=5, decimal_places=1)
    item_price = MoneyField(max_digits=14, decimal_places=2, default_currency="NOK")

    @property
    def value(self) -> Money:
        if not self.pk:
            return Money(0, self.item_price.currency)

        return (self.quantity * self.item_price) * (1 - (self.discount / 100))

    def __str__(self):
        return f"{self.sku}: {self.quantity} x {self.item_price}"


class CollarQuerySet(models.QuerySet):
    def of_type(self, product_type: PRODUCT_TYPE):
        return self.filter(product=product_type)


class Collar(BaseHistoryModel):
    class xModemUratArg(models.TextChoices):
        SINGLE_2G = "9", "2G Only"
        SINGLE_4G = "7", "4G Only"
        MULTI_2G_4G = "9,7", "2G/4G"
        MULTI_4G_2G = "7,9", "4G/2G"

    # Error code used in validation errors to identify the error type
    ERROR_CODE_MISSING_OWNER = object()
    ERROR_CODE_BLANK = object()

    produced_at = models.DateTimeField(blank=True, null=True)
    serial_no = models.BigIntegerField(unique=True)
    alias = models.CharField(max_length=255, null=True, blank=True)
    sim_card_id = models.CharField(max_length=24, null=True, blank=True)  # make unique eventually
    product = models.CharField(max_length=2, choices=PRODUCT_TYPE)
    model = models.CharField(max_length=10, blank=True)
    model_internal = models.CharField(max_length=20, blank=True)
    hw_version_name = models.CharField(max_length=20, blank=True)
    ems_provider = models.IntegerField(null=True, blank=True, choices=EMS_PROVIDERS)
    pcb_revision = models.IntegerField(null=True, blank=True)
    mec_revision = models.IntegerField(null=True, blank=True)
    product_record = models.IntegerField(null=True, blank=True)
    # Owner is the customer, i.e. the entity that pays for the collar subscription.
    owner = models.ForeignKey(Customer, null=True, blank=True, on_delete=models.SET_NULL, related_name="owned_collars")
    # User (should probably be renamed) is the entity that currently manage, use, keep (which verb?) the collar.
    user = models.ForeignKey(Customer, null=True, blank=True, on_delete=models.SET_NULL, related_name="usage_collars")

    modem_model = models.CharField(max_length=100, blank=True, null=True)
    modem_version = models.CharField(max_length=100, blank=True, null=True)
    modem_upgrade_status = models.CharField(max_length=100, blank=True)
    modem_upgrade_status_created_at = models.DateTimeField(null=True, blank=True)
    modem_upgrade_status_download_restarts = models.IntegerField(default=0)
    modem_upgrade_status_upgrade_restarts = models.IntegerField(default=0)

    last_recorded_activity = models.DateTimeField(null=True, blank=True)

    config_x_modem_urat_arg = models.CharField(
        max_length=300,
        null=True,
        blank=True,
        verbose_name="xModemUratArg",
        choices=xModemUratArg.choices,
    )
    config_x_modem_urat_arg_ack_at = models.DateTimeField(null=True, blank=True, verbose_name="xModemUratArgAckAt")

    # Copied from the mongodb document. Not updated in realtime
    firmware = models.CharField(max_length=50, blank=True, null=True)
    firmware_date = models.DateTimeField(null=True, blank=True)
    # Field is synced only on full sync
    server_ip = models.CharField(max_length=50, blank=True, null=True)

    objects = models.Manager.from_queryset(CollarQuerySet)()

    # Used to check for modification to collar fields
    tracker = FieldTracker(fields=["user_id", "owner_id", "firmware"])

    target_pasture = models.ForeignKey(
        "PastureGeometryVersion",
        null=True,
        blank=True,
        on_delete=models.SET_NULL,
        related_name="targeted_by_collars",
    )
    target_pasture_fence_definition_version = models.IntegerField(null=True, blank=True)
    target_pasture_updated_at = models.DateTimeField(null=True, blank=True)
    target_pasture_update_reason = models.PositiveSmallIntegerField(
        choices=PastureUpdateReason.choices(),
        null=True,
        blank=True,
    )

    reported_pasture = models.ForeignKey(
        "PastureGeometryVersion",
        null=True,
        blank=True,
        on_delete=models.SET_NULL,
        related_name="reported_by_collars",
    )
    reported_pasture_fence_definition_version = models.IntegerField(null=True, blank=True)
    reported_pasture_received_at = models.DateTimeField(null=True, blank=True)
    reported_pasture_report_source = models.PositiveSmallIntegerField(
        choices=PastureReportSource.choices(),
        null=True,
        blank=True,
    )

    activated_pasture = models.ForeignKey(
        "PastureGeometryVersion",
        null=True,
        blank=True,
        on_delete=models.SET_NULL,
        related_name="activated_on_collars",
    )
    activated_pasture_fence_definition_version = models.IntegerField(null=True, blank=True)
    activated_pasture_received_at = models.DateTimeField(null=True, blank=True)
    activated_pasture_report_source = models.PositiveSmallIntegerField(
        choices=PastureReportSource.choices(),
        null=True,
        blank=True,
    )

    # Is used to set source pasture to the app on a move
    source_pasture = models.ForeignKey(
        "PastureGeometryVersion",
        null=True,
        blank=True,
        on_delete=models.SET_NULL,
    )

    position_time = models.DateTimeField(null=True, blank=True)
    position_lat = models.FloatField(null=True, blank=True)
    position_lon = models.FloatField(null=True, blank=True)
    position_accuracy = models.IntegerField(null=True, blank=True)
    battery_voltage = models.PositiveSmallIntegerField(null=True, blank=True)
    fence_status = models.PositiveSmallIntegerField(null=True, blank=True)
    collar_status = models.PositiveSmallIntegerField(null=True, blank=True)
    mode = models.PositiveSmallIntegerField(null=True, blank=True)

    owner_device_id = models.PositiveSmallIntegerField(null=True, blank=True)
    owner_device_id_owner_id = models.IntegerField(null=True, blank=True)

    target_firmware_version = models.PositiveIntegerField(blank=True, null=True)
    target_firmware_version_updated_at = models.DateTimeField(null=True, blank=True)
    reported_firmware_version = models.PositiveIntegerField(blank=True, null=True)
    reported_firmware_version_received_at = models.DateTimeField(null=True, blank=True)
    # The connection id for the last time the collar contacted the server and was given a new firmware version in the
    # response. By tracking this we can count how many attempts the collar has made to download the firmware.
    last_firmware_upgrade_connection_id = models.CharField(max_length=100, blank=True, null=True)
    firmware_upgrade_attempts = models.PositiveIntegerField(default=0)

    reported_config = models.JSONField(null=True, editable=False)
    herd_poll_settings = models.JSONField(null=True, editable=False)

    def __str__(self):
        return f"#{self.serial_no}"

    def clean(self):
        if self.pk is None:
            # Validation for collars being created
            self.clean_new_collar()

        if self.owner is None and self.user is not None:
            # We need to wrap the message in a validation error to keep track of the code
            raise ValidationError(
                {
                    "owner": ValidationError(
                        _("The owner can not be empty when the collar is assigned to a user"),
                        code=self.ERROR_CODE_MISSING_OWNER,
                    ),
                },
            )

        # See core.collar_assignment.skip_pasture_validation
        if not getattr(self, "_skip_active_pasture_validation", False):
            if self.owner is None and self.is_assigned_to_pasture():
                raise ValidationError(
                    {"owner": _("The owner can not be empty when the collar is assigned to a pasture")},
                )
            if self.user is None and self.is_assigned_to_pasture():
                raise ValidationError(
                    {"user": _("The user can not be empty when the collar is assigned to a pasture")},
                )
            if self.tracker.has_changed("user_id") and self.is_assigned_to_pasture():
                raise ValidationError(
                    {"user": _("The user can not be changed when the collar is assigned to a pasture")},
                )

        if self.pk is not None:
            if self.tracker.has_changed("user_id"):
                if self.user is not None and self.is_due_for_replacement():
                    raise ValidationError(
                        {"user": _("A collar due for replacement can not be assigned to a new user or owner")},
                    )
            if self.tracker.has_changed("owner_id"):
                if self.owner is not None and self.is_due_for_replacement():
                    raise ValidationError(
                        {"owner": _("A collar due for replacement can not be assigned to a new owner or user")},
                    )

    def clean_new_collar(self):
        errors = []

        for fieldname in NEW_COLLAR_REQUIRED_FIELDS:
            try:
                guard_against_none(self, fieldname)
            except (ValidationError, AttributeError) as e:
                errors.append(e)

        try:
            guard_against_zero(self, "product_record")
        except (ValidationError, AttributeError) as e:
            errors.append(e)

        if errors:
            raise ValidationError(errors)

    def save(self, *args, **kwargs):
        # Can only be changed using core.collar_assignment.update_collars_owner_and_user
        if self.tracker.has_changed("owner_id") or self.tracker.has_changed("user_id"):
            # This attribute is set by core.collar_assignment.update_collars_owner_and_user
            # during the save operation as a safety check to prevent the owner or user from
            # being overwritten by accident by other scripts.
            if not self._state.adding and not getattr(self, "_enable_owner_and_user_update", False):
                raise RuntimeError(
                    "Collar owner and user can only be changed using "
                    "core.collar_assignment.update_collars_owner_and_user "
                    "or in some cases by using "
                    "core.collar_assignment.enable_owner_and_user_update",
                )

        return super().save(*args, **kwargs)

    def is_assigned_to_pasture(self) -> bool:
        return (
            CollarDocument.objects.filter(
                serial_no=self.serial_no,
                current_fence_definition__pasture__id__ne=None,
            ).first()
            is not None
        )

    def is_blocked_from_sales(self) -> bool:
        """Returns true if the collar for some reason should be blocked from getting sold"""
        return self._is_blocked_by_tag() or self._is_blocked_by_test_firmware() or self._is_blocked_by_bom()

    def get_blocked_reason(self) -> str:
        if self._is_blocked_by_tag():
            for tag in self.tags.all():
                for collar_tag in CollarTag.objects.filter(name=tag.tag, block_shipping=True):
                    return collar_tag.block_shipping_msg
        elif self._is_blocked_by_test_firmware():
            return "Has test firmware"
        elif self._is_blocked_by_bom():
            return "Is blocked by bom"
        return ""

    def _is_blocked_by_bom(self) -> bool:
        exists = CollarBOMBlock.objects.filter(
            ems_provider=self.ems_provider,
            product=self.product,
            product_record=self.product_record,
            mec_revision=self.mec_revision,
            pcb_revision=self.pcb_revision,
        ).exists()

        return exists

    def _is_blocked_by_tag(self) -> bool:
        for tag in self.tags.all():
            if CollarTag.objects.filter(name=tag.tag, block_shipping=True).exists():
                return True

    def _is_blocked_by_test_firmware(self) -> bool:
        from core.models.fota import RolloutFirmware

        try:
            return RolloutFirmware.objects.get(
                application_version=int(self.firmware),
            ).is_test_firmware
        except (RolloutFirmware.DoesNotExist, ValueError, TypeError):
            return False

    @property
    def last_seen_timestamp(self) -> Optional[datetime]:
        if self.document is not None and self.document.last_received_timestamp:
            try:
                return datetime.fromtimestamp(self.document.last_received_timestamp // 1000, UTC)
            except ValueError:
                pass

    @property
    def warranty(self) -> Optional["CollarWarranty"]:
        if hasattr(self, "collar_warranty"):
            return self.collar_warranty
        else:
            return None

    @property
    def get_max_warranty_period(self):
        # if collar is legacy or first 2.5 return 2 years
        # the rest timeline should be coverd by pickand pack warranty creation
        if self.produced_at <= datetime(2023, 5, 1, tzinfo=UTC):
            logger.info(f"collar {self.serial_no} max waranty 2 years 180 days")
            return relativedelta(years=2, months=6)
        if self.is_legacy_collar():
            logger.info(f"collar {self.serial_no} is legacy collar, max waranty 2 years 30 days")
            return relativedelta(years=2, days=30)
        logger.info(f"collar {self.serial_no} max waranty 5 years 30days")
        # default warranty now is 5 years
        return relativedelta(years=5, days=30)

    @cached_property
    def document(self) -> Optional[CollarDocument]:
        try:
            return CollarDocument.objects.get(serial_no=self.serial_no)
        except DoesNotExist:
            return None

    def get_positions(self, start: datetime, end: datetime):
        start = start.astimezone(UTC)
        end = end.astimezone(UTC)
        if start >= end:
            raise ValidationError("start must be before end")
        if (end - start) > timedelta(days=180):
            raise ValidationError("The maximum query range is 180 days")
        device_id = self.serial_no

        query = """
            -- Position events
            SELECT
                'position' as event_type,
                fromUnixTimestamp(message.datePos.ulUnixTimestamp) as event_time,
                message.datePos.lLat as lat,
                message.datePos.lLon as lon
            FROM message_json_by_type_device
            WHERE message_type = 'poll_message_req'
            AND time >= {time_start:DateTime} AND time <= {time_end:DateTime}
            AND device_id = {device_id:UInt64}
            AND message.datePos.lLat IS NOT NULL
            AND message.datePos.lLon IS NOT NULL
            AND event_time >= {time_start:DateTime} AND event_time <= {time_end:DateTime}
            UNION ALL
            -- Pulse events
            SELECT
                'pulse' as event_type,
                time as event_time,
                message.xDatePos.lLat as lat,
                message.xDatePos.lLon as lon
            FROM message_json_by_type_device
            WHERE message_type = 'client_zap_message'
            AND time >= {time_start:DateTime} AND time <= {time_end:DateTime}
            AND device_id = {device_id:UInt64}
            AND message.xDatePos.lLat IS NOT NULL
            AND message.xDatePos.lLon IS NOT NULL
            UNION ALL
            -- Warning events
            SELECT
                'warning' as event_type,
                time as event_time,
                message.xDatePos.lLat as lat,
                message.xDatePos.lLon as lon
            FROM message_json_by_type_device
            WHERE message_type = 'client_warning_message'
            AND time >= {time_start:DateTime} AND time <= {time_end:DateTime}
            AND device_id = {device_id:UInt64}
            AND message.xDatePos.lLat IS NOT NULL
            AND message.xDatePos.lLon IS NOT NULL
            UNION ALL
            -- Escape events
            SELECT
                'escape' as event_type,
                time as event_time,
                message.datePos.lLat as lat,
                message.datePos.lLon  as lon
            FROM message_json_by_type_device
            WHERE message_type = 'status_msg'
            AND time >= {time_start:DateTime} AND time <= {time_end:DateTime}
            AND device_id = {device_id:UInt64}
            AND message.eFenceStatus = 'Escaped'
            AND message.eReason = 'WARNSTOPREASON_ESCAPED'
            AND message.datePos.lLat IS NOT NULL
            AND message.datePos.lLon IS NOT NULL
            ORDER BY event_time
        """

        parameters = {
            "time_start": start,
            "time_end": end,
            "device_id": device_id,
        }
        # Use the new efficient query_rows method
        rows = clickhouse_collarserver.query_df(query, parameters)
        if not rows.empty and "event_time" in rows.columns:
            rows = rows.sort_values("event_time")
            rows["event_time"] = pd.to_datetime(rows["event_time"], errors="coerce")
            rows["event_time"] = rows["event_time"].dt.strftime("%Y-%m-%dT%H:%M:%S")
            rows["device_id"] = device_id
        return rows.to_dict("records")

    @property
    def supported_network(self) -> Optional[str]:
        if not self.model:
            return None
        if self.model == "N/A":
            # N/A models are old
            return "2G"
        model = float(self.model.replace("C", "").replace("SG", "").replace("S", ""))
        if model >= 2.1:
            return "2G/4G"
        else:
            return "2G"

    def get_replaced_collar(self) -> Optional["Collar"]:
        """Returns the collar that this collar replaced, if any"""
        replacement = getattr(self, "replaced", None)
        if replacement is not None:
            return replacement.collar

    def get_replaced_by_collar(self) -> Optional["Collar"]:
        """Returns the collar that replaced this collar, if any"""
        replacement = getattr(self, "returned", None)
        if replacement is not None:
            return replacement.replacement_collar

    def get_owner(self, at_date: date = None):
        """
        find out who owned this collar at the given time. this is important for billing.
        We're tracking owners in billy from a certain date, before that we only have the data in
        mongo to rely on. So if collar.collarowner is empty or collar.owner and collar.user is in sync,
        then we need to go to mongo.

        Technically a collar can have multiple owners for a given date, but we will use the first one we see
        for the purpose of ownership that day and invoicing.
        """

        """ we're looking at present data, return the current owner, which can be None """
        if not at_date or at_date == date.today() or at_date == date.today() - timedelta(days=1):
            return self.owner

        """ we need to work out who owned the collar at a given datetime """
        owner = None
        for collarowner in self.collarowner_set.all().order_by("created_at"):
            # we've got a potential owner, and the current collarowner
            if owner and collarowner.created_at.date() >= at_date:
                return owner

            # the collar owner assignment is before the date so this is a potential owner
            if collarowner.created_at.date() <= at_date:
                owner = collarowner.owner

        if owner:
            return owner

        """ no collarowner found in billy - default to the mongo data """
        collar_document = CollarDocument.objects.get(serial_no=self.serial_no)
        ownership = collar_document.get_ownership(at_date)

        owner = Customer.objects.get(user_no=ownership.user_no) if ownership else None

        return owner

    def has_tag(self, tag: str) -> bool:
        return self.tags.filter(tag=tag).exists()

    def is_due_for_replacement(self) -> bool:
        return self.has_tag("DUE_FOR_REPLACEMENT")

    def has_acked_pasture(self):
        """Returns if the collar has received the current target pasture"""
        # Note that collars that have never had a pasture will have None for both values, this is captured by the
        # checks on "self.target_pasture_updated_at and self.reported_pasture_received_at".

        # Also keep in mind that when we assign a new target pasture to a collar we don't clear the reported pasture as
        # the intention is for the reported pasture to always keep track of what the collar last reported, because of
        # this we must check that the reported_pasture_received_at value is more recent than the
        # target_pasture_updated_at time.
        return (
            self.target_pasture_fence_definition_version == self.reported_pasture_fence_definition_version
            and self.target_pasture_updated_at
            and self.reported_pasture_received_at
            and self.reported_pasture_received_at >= self.target_pasture_updated_at
        )

    def has_activated_pasture(self):
        """Returns if the collar has registered a position within the current target pasture"""
        return (
            self.target_pasture_fence_definition_version == self.activated_pasture_fence_definition_version
            and self.target_pasture_updated_at
            and self.activated_pasture_received_at
            and self.activated_pasture_received_at >= self.target_pasture_updated_at
        )

    @staticmethod
    def start_warranty_for_collars(collars: List["Collar"]):
        first_assigned = timezone.now()
        warranty_end_date = first_assigned + relativedelta(years=5, days=30)

        for collar in collars:
            # collar never had a warranty
            if collar.warranty is None:
                CollarWarranty.objects.create(
                    collar=collar,
                    start_date=first_assigned,
                    end_date=warranty_end_date,
                )
                logger.info(f"Collar {collar} warranty created with {first_assigned} - {warranty_end_date}")
            else:
                # collar is sold as new, gets a new warranty period
                collar.warranty.start_date = first_assigned
                collar.warranty.end_date = warranty_end_date
                collar.warranty.save()
                logger.info(f"Collar {collar} warranty updated with {first_assigned} - {warranty_end_date}")

    def is_legacy_collar(self) -> bool:
        # Needs to be updated for Switchgrass
        return not self.model.endswith("2.5")

    class Meta:
        indexes = [
            models.Index(fields=["serial_no"]),
        ]


def guard_against_none(collar: Collar, fieldname: str) -> None:
    if operator.attrgetter(fieldname)(collar) is None:
        error = {}
        error[fieldname] = ValidationError(_("%(fieldname)s is None"), code="BLANK", params={"fieldname": fieldname})
        raise ValidationError(error)


def guard_against_zero(collar: Collar, fieldname: str) -> None:
    if operator.attrgetter(fieldname)(collar) == 0:
        error = {}
        error[fieldname] = ValidationError(
            _("%(fieldname)s can not be 0"),
            code="ZERO",
            params={"fieldname": fieldname},
        )
        raise ValidationError(error)


class CollarWarranty(BaseHistoryModel):
    collar = models.OneToOneField(Collar, on_delete=models.CASCADE, related_name="collar_warranty")
    start_date = models.DateField()
    end_date = models.DateField()
    inherited_from_collar = models.ForeignKey(Collar, on_delete=models.SET_NULL, blank=True, null=True)

    def __str__(self):
        start_date = date_format(self.start_date, "DATE_FORMAT")
        end_date = date_format(self.end_date, "DATE_FORMAT")
        return f"{start_date} - {end_date}"


class CollarTagFromMongo(BaseModel):
    collar = models.ForeignKey(Collar, CASCADE, related_name="tags")
    tag = models.CharField(max_length=50)

    def __str__(self):
        return f"{self.collar}: {self.tag}"


class CollarOwner(BaseHistoryModel):
    """keep a record of who owned collars at given times"""

    collar = models.ForeignKey(Collar, CASCADE)
    owner = models.ForeignKey(Customer, CASCADE, null=True)


class CollarUsage(BaseModel):
    date = models.DateField()
    product = models.CharField(max_length=2, choices=PRODUCT_TYPE)
    collar = models.ForeignKey(Collar, CASCADE)
    customer = models.ForeignKey(Customer, CASCADE, related_name="collarusages")
    usage_summary = models.ForeignKey("CollarUsageSummary", SET_NULL, related_name="collarusages", null=True)

    class Meta:
        unique_together = ["date", "customer", "collar", "product"]
        indexes = [
            models.Index(fields=["created_at", "customer"], name="core_collar_created_at_idx"),
        ]

    def __str__(self):
        return f"{self.date} - {self.product} - {self.collar.serial_no}"


class CollarUsageSummary(BaseModel):
    date = models.DateField("Usage date")
    customer = models.ForeignKey(Customer, CASCADE, related_name="collarusagesummaries")
    product = models.CharField(max_length=2, choices=PRODUCT_TYPE)
    total_usage = models.IntegerField(default=0)
    total_caps_reached = models.IntegerField(default=0)
    fixed_price_usage = models.IntegerField(default=0)
    variable_price_usage = models.IntegerField(default=0)
    usage_caps_applied = models.IntegerField(default=0)
    usage_billed = models.IntegerField(default=0)
    missing_subscriptions = models.IntegerField(default=0)
    # Keep track of usage where both the replacement collar and the old collar was
    # used on the same day. This value is already subtracted from the saved
    # total_usage value, this way we don't have to update queries that are calculating
    # usage based on the total_usage value.
    return_and_replacement_double_usage = models.IntegerField(default=0)
    invoice = models.ForeignKey(Invoice, SET_NULL, related_name="collarusagesummaries", null=True)

    class Meta:
        unique_together = ["date", "customer", "product"]
        indexes = [
            models.Index(fields=["customer", "date", "invoice"]),
            models.Index(fields=["customer", "updated_at", "date"]),
        ]
        verbose_name_plural = "Collar usage summaries"

    def __str__(self):
        return f"{self.date} - {self.customer}: {self.get_product_display()} {self.usage_billed}/{self.total_usage}"


class CollarUsageMonthly(BaseHistoryModel):
    """Monthly summary collar usage information per customer"""

    month = models.DateField("Usage month")
    customer = models.ForeignKey(Customer, CASCADE, related_name="collarusagemonthly")

    total_usage = models.IntegerField(default=0)
    pre_paid_usage = models.IntegerField(default=0)

    # This is basically total_usage - pre_paid_usage, but added as a pre calculated
    # value here to make some queries simpler
    flexible_usage = models.IntegerField(default=0)

    tracker = FieldTracker(
        fields=[
            "total_usage",
            "pre_paid_usage",
            "flexible_usage",
        ],
    )

    class Meta:
        unique_together = ["customer", "month"]
        indexes = [
            models.Index(fields=["customer", "updated_at", "month"]),
            models.Index(fields=["month"]),
        ]

        verbose_name_plural = "Collar usage monthly"

    def __str__(self):
        return f"{self.month}:total:{self.total_usage=}"

    def clean(self):
        if self.month.day != 1:
            raise ValidationError({"month": _("Date must be set to the first day of the month")})

    def save(self, *args, **kwargs) -> None:
        self.clean()
        return super().save(*args, **kwargs)

    def get_product_usage(self, product: str) -> Optional["CollarUsageMonthlyProduct"]:
        try:
            return self.product_usage.get(product=product)
        except CollarUsageMonthlyProduct.DoesNotExist:
            return None

    def get_product_usages(self) -> List["CollarUsageMonthlyProduct"]:
        return list(self.product_usage.order_by("product").all())

    def get_total_available_free_usage_caps(self) -> int:
        return self.customer.get_flexible_billing_free_usage_cap_for_month(self.month)

    def get_applicable_free_usage(self) -> int:
        """Returns the number of free usage caps that is applicable for this summary"""
        free_usage_available = self.customer.get_flexible_billing_free_usage_cap_for_month(self.month)
        # We can not apply more free usage than, the actual flexible usage
        return min(self.flexible_usage, free_usage_available)

    def get_chargeable_usage(self) -> int:
        return self.flexible_usage - self.get_applicable_free_usage()

    def get_pre_paid_actual_usage(self) -> int:
        # The pre paid usage value contains the, total usage the customer has,
        # here we find the actual usage of was covered by the pre paid usage.
        return min(self.total_usage, self.pre_paid_usage)

    def is_already_invoiced(self) -> bool:
        return self.invoiced_usage.exists()


class CollarUsageMonthlyProduct(BaseHistoryModel):
    """Monthly invoiced usage information per customer

    This is used to keep track of invoiced usage for flexible invoicing, to be
    able to implement the yearly price cap for flexible usage
    """

    total = models.ForeignKey(CollarUsageMonthly, CASCADE, related_name="product_usage", null=True, blank=True)
    product = models.CharField(max_length=2, choices=PRODUCT_TYPE)

    total_usage = models.IntegerField(default=0)
    pre_paid_usage = models.IntegerField(default=0)

    flexible_usage = models.IntegerField(default=0)

    tracker = FieldTracker(
        fields=[
            "total_id",
            "total_usage",
            "pre_paid_usage",
            "flexible_usage",
        ],
    )

    class Meta:
        unique_together = [
            ("total", "product"),
        ]

    def __str__(self):
        return f"ProductUsage:{self.get_product_display()}:{self.total_usage=}"

    def is_empty(self):
        """Returns true if we consider this summary to have no useful data.

        We use this to only save the summary if it actually contains any data
        """
        return self.total_usage == 0 and self.pre_paid_usage == 0


class InvoicedUsage(BaseModel):
    """
    Basically just a snapshot of the monthly usage data that was available when the invoice
    was created.

    Together with some additional information.

    This could be useful in case the usage data changes and we need to recreate the original invoice,
    or to implement invoicing of late arriving usage data.
    """

    usage = models.ForeignKey(CollarUsageMonthly, CASCADE, related_name="invoiced_usage")

    invoice = models.ForeignKey(Invoice, CASCADE, related_name="invoiced_usage")

    total_usage = models.IntegerField(default=0)
    pre_paid_usage = models.IntegerField(default=0)
    flexible_usage = models.IntegerField(default=0)
    available_usage_caps = models.IntegerField(default=0)
    owned_collars = models.IntegerField(default=0)

    billed_usage = models.IntegerField(default=0)
    applied_usage_caps = models.IntegerField(default=0)

    class Meta:
        unique_together = ["usage", "invoice"]

    def set_from_monthly_usage(self, usage: CollarUsageMonthly):
        self.total_usage = usage.total_usage
        self.pre_paid_usage = usage.pre_paid_usage
        self.flexible_usage = usage.flexible_usage

    def get_num_flexible_subs(self) -> int:
        return max(0, self.owned_collars - self.pre_paid_usage)


class InvoicedProductUsage(BaseModel):
    invoiced_usage = models.ForeignKey(InvoicedUsage, CASCADE, related_name="invoiced_product_usage")
    product_usage = models.ForeignKey(CollarUsageMonthlyProduct, CASCADE, related_name="invoiced_product_usage")

    billed_usage = models.IntegerField(default=0)
    total_usage = models.IntegerField(default=0)
    pre_paid_usage = models.IntegerField(default=0)

    class Meta:
        unique_together = ["invoiced_usage", "product_usage"]

    def set_from_product_usage(self, usage: CollarUsageMonthlyProduct):
        self.total_usage = usage.total_usage
        self.pre_paid_usage = usage.pre_paid_usage
        self.flexible_usage = usage.flexible_usage


class State(BaseModel):
    name = models.CharField(max_length=200, primary_key=True)
    value = models.TextField()

    def __str__(self):
        return f"{self.name}"


class WarehouseManager(models.Manager):
    def simpro(self) -> "Warehouse":
        return self.get(reference_id=Warehouse.SIMPRO)

    def hapro(self) -> "Warehouse":
        return self.get(reference_id=Warehouse.HAPRO)

    def eu(self) -> "Warehouse":
        return self.get(reference_id="EU")


class TransporterName(models.TextChoices):
    BRING = "BRING"
    DHL = "DHL"
    DHL_EXPRESS = "DHL_EXPRESS"
    CORREOS = "CORREOS"
    PARCEL_FORCE = "PARCEL_FORCE"
    UPS = "UPS"
    OTHER = "OTHER"


class Warehouse(BaseModel):
    class Meta:
        ordering = ["-is_active", "name"]

    class InventoryMode(models.TextChoices):
        INTERNALLY_MANAGED = "INTERNALLY_MANAGED", _("Managed in Billy")
        EXTERNAL_SYNC = "EXTERNAL_SYNC", _("External Sync (Read only in admin)")

    # Visma IDs
    SIMPRO = "1"
    BATNFJORD = "2"
    HAPRO = "3"

    objects: WarehouseManager = WarehouseManager()

    name = models.CharField(max_length=200)
    code = models.CharField(
        max_length=100,
        null=True,
        blank=True,
        choices=[(INISSION, INISSION)],
        help_text="If special handling of receipts or other this needs to be set",
    )

    # The id used by external systems, currently visma
    reference_id = models.CharField(max_length=100, unique=True)

    using_late_assembly = models.BooleanField(default=False)

    is_active = models.BooleanField(
        default=True,
        help_text=(
            "Checked if the warehouse is in active use. "
            "Currently this mainly affects the ordering and visibility of the warehouse in the admin. "
            "Active warehouses are ordered first in the views, and inactive warehouses are by default filetered out "
            "from some views in the admin."
        ),
    )

    default_transporter = models.CharField(max_length=100, blank=True, null=True, choices=TransporterName.choices)

    nofence_company = models.ForeignKey(NofenceCompany, PROTECT, blank=True, null=True)

    inventory_mode = models.CharField(
        max_length=100,
        choices=InventoryMode.choices,
        default=InventoryMode.INTERNALLY_MANAGED,
        help_text=_(
            "Currently controls whether the inventory in the warehouse can be manually updated in the admin or not.",
        ),
    )

    contact_person_name = models.CharField(
        max_length=100,
        blank=True,
        null=True,
        help_text="Name of contact person at warehouse. Will be displayed on proforma.",
    )
    contact_person_email = own_fields.EmailField(
        blank=True,
        null=True,
        help_text="Email of contact person at warehouse. Will be displayed on proforma.",
    )
    contact_person_phone_number = models.CharField(
        max_length=100,
        blank=True,
        null=True,
        help_text="Phone number of contact person at warehouse. Will be displayed on proforma.",
    )

    stock_buffer = models.IntegerField(default=10, help_text="Stock buffer for the warehouse")

    address = RelatedObjectOrNone("address")

    company_name = models.CharField(
        max_length=64,
        blank=True,
        null=True,
        help_text="Company name as appears on contract with delivery company, used for generating shipping labels",
    )
    country = CountryField(default="NO")

    # Used to limit access to shipments from certain warehouses for users in
    # the selected groups. If a user is a member of a group that is linked to
    # some warehouses, the user will ony have access to shipments from those
    # warehouses in the admin.
    group_access = models.ManyToManyField(
        "auth.Group",
        blank=True,
        related_name="warehouse_access",
    )

    def __str__(self):
        return f"{self.name}"

    @property
    def primary_location(self):
        return self.get_location(WarehouseLocation.LocationType.PRIMARY)

    @property
    def reservation_location(self):
        return self.get_location(WarehouseLocation.LocationType.RESERVATION)

    @property
    def web_shop_primary_location(self):
        return self.get_first_active_location_by_type(
            [WarehouseLocation.LocationType.WEB_SHOP_PRIMARY, WarehouseLocation.LocationType.PRIMARY]
        )

    @property
    def web_shop_reservation_location(self):
        return self.get_first_active_location_by_type(
            [WarehouseLocation.LocationType.WEB_SHOP_RESERVATION, WarehouseLocation.LocationType.RESERVATION]
        )

    @property
    def in_transit_location(self):
        return self.get_location(WarehouseLocation.LocationType.IN_TRANSIT)

    def get_location(self, locaction_type: "WarehouseLocation.LocationType") -> Optional["WarehouseLocation"]:
        return self.locations.filter(location_type=locaction_type).first()

    def get_first_active_location_by_type(
        self, location_types: list["WarehouseLocation.LocationType"]
    ) -> Optional["WarehouseLocation"]:
        locations = {
            loc.location_type: loc for loc in self.locations.filter(location_type__in=location_types, is_active=True)
        }
        for loc_type in location_types:
            if loc_type in locations:
                return locations[loc_type]

    def is_editable_in_admin(self):
        return self.inventory_mode in [Warehouse.InventoryMode.INTERNALLY_MANAGED]

    @property
    def is_internally_managed(self):
        return self.inventory_mode in [Warehouse.InventoryMode.INTERNALLY_MANAGED]


class WarehouseLocationManager(models.Manager):
    def get_or_create_primary(self, warehouse: Warehouse):
        return WarehouseLocation.objects.get_or_create(
            warehouse=warehouse,
            location_type=WarehouseLocation.LocationType.PRIMARY,
            defaults={
                "name": "Default",
            },
        )


class WarehouseLocation(BaseModel):
    class LocationType(models.TextChoices):
        # Stock is taken from the primary location
        PRIMARY = "PRIMARY"
        # For the pre paid flow stock is moved from the primary location into the
        # reservation location when waiting for payments.
        RESERVATION = "RESERVATION"
        # Inventory that is in transit into a warehouse
        IN_TRANSIT = "IN_TRANSIT"
        # Other locations for manually handled inventory
        OTHER = "OTHER"
        # Inventory that is reserved for web-shop
        WEB_SHOP_RESERVATION = "WEB_SHOP_RESERVATION", "Web Shop Reservation (Deprecated)"
        # Inventory that is reserved for web-shop
        WEB_SHOP_PRIMARY = "WEB_SHOP_PRIMARY", "Web Shop Primary (Deprecated)"

    # These are the main location types that should always be present
    PRIMARY_LOCATION_TYPES = (
        LocationType.PRIMARY,
        LocationType.RESERVATION,
    )

    warehouse = models.ForeignKey(Warehouse, CASCADE, related_name="locations")
    name = models.CharField(max_length=200)

    location_type = models.CharField(
        max_length=100,
        choices=LocationType.choices,
        default=LocationType.OTHER,
        help_text=_(
            "The primary location is used when "
            "adjusting and checking available inventory from scripts."
            "The reservation location is used to keep track of reserve inventory "
            "when waiting for payments."
            "The in transit location is used to keep track of inventory "
            "in transit into a warehouse.",
        ),
    )

    is_active = models.BooleanField(
        default=True,
        help_text=(
            "Checked if the location is in active use. "
            "Active locations are ordered first in the views, and inactive locations are by default filtered out "
            "from some views in the admin. "
            "For web shop locations, if the web shop locations are set to inactive, they will no longer be used by the web shop, "
            "the shop will use the primary/reservation locations instead."
        ),
    )

    objects = WarehouseLocationManager()

    class Meta:
        constraints = [
            models.UniqueConstraint(
                fields=["warehouse_id", "location_type"],
                condition=Q(location_type__in=["PRIMARY", "RESERVATION", "IN_TRANSIT"]),
                name="warehouse_location_type_unique",
            ),
        ]

    def __str__(self):
        return f"{self.warehouse} - {self.name}"

    def validate_unique(self, exclude=None):
        if self.location_type not in [
            WarehouseLocation.LocationType.PRIMARY,
            WarehouseLocation.LocationType.RESERVATION,
            WarehouseLocation.LocationType.IN_TRANSIT,
        ]:
            return

        location = (
            WarehouseLocation.objects.filter(warehouse=self.warehouse, location_type=self.location_type)
            .exclude(id=self.id)
            .first()
        )
        if location is not None:
            raise ValidationError(
                {
                    "location_type": _(
                        f"A warehouse can only have a single location of type {self.location_type}. "
                        f'This location type is already in use by the location "{location.name}"',
                    ),
                },
            )

    def clean(self):
        if self.location_type in self.PRIMARY_LOCATION_TYPES and not self.is_active:
            raise ValidationError(
                {"is_active": f"Locations of type {self.location_type} can not be disabled."},
            )


class IncoTerms(models.TextChoices):
    EXW = "EXW", "EXW"
    DAP = "DAP", "DAP"
    DDP = "DDP", "DDP"
    FCA = "FCA", "FCA"
    FAS = "FAS", "FAS"
    FOB = "FOB", "FOB"
    CFR = "CFR", "CFR"
    CIF = "CIF", "CIF"
    CPT = "CPT", "CPT"
    CIP = "CIP", "CIP"
    DAT = "DAT", "DAT"


def get_product_image_file_storage():
    return storages["s3_product_images"]


class ProductManager(models.Manager):
    def get_by_sku_or_none(self, sku: str) -> Optional["Product"]:
        return Product.objects.filter(
            sku=sku,
        ).first()


class Product(BaseHistoryModelForModelTranslations):
    class ProductCategory(models.TextChoices):
        COLLAR = "COLLAR"
        FREIGHT = "FREIGHT"
        EXTRA = "EXTRA"
        BATTERY = "BATTERY"
        CHARGER = "CHARGER"
        BEACON = "BEACON"
        NECK_STRAP = "NECK_STRAP"
        BRACKET = "BRACKET"
        SPARE_PART = "SPARE_PART"
        CHAIN = "CHAIN"
        CHAIN_EXTENSION = "CHAIN_EXTENSION"
        SUBSCRIPTION = "SUBSCRIPTION"
        DEPRECATED = "DEPRECATED"

        # Special category used to implement buyable subscriptions in the web shop.
        BUYABLE_SUBSCRIPTION = "BUYABLE_SUBSCRIPTION"

    class ProductModel(models.TextChoices):
        C20 = "C2.0", "C2.0"
        C21 = "C2.1", "C2.1"
        C22 = "C2.2", "C2.2"
        C25 = "C2.5", "C2.5"
        C26 = "C2.6", "C2.6"
        SG20 = "SG2.0", "SG2.0"
        SG21 = "SG2.1", "SG2.1"
        SG209 = "SG2.09", "SG2.09"
        SG22 = "SG2.2", "SG2.2"
        SG25 = "SG2.5", "SG2.5"

    class ProductStatus(models.TextChoices):
        ACTIVE = "ACTIVE"
        INACTIVE = "INACTIVE"

    sku = models.CharField(max_length=300, unique=True)
    item_number = models.CharField(
        max_length=100,
        blank=True,
        null=True,
        unique=True,
        help_text="Number linked to Arena.",
    )
    hs_code = models.CharField(
        max_length=20,
        blank=True,
        null=True,
        help_text="Harmonized System code to classify the exact type of goods to ship.",
    )
    origin_country = CountryField(
        default="NO",
        help_text="Country of origin for the product. Displayed on proforma invoice.",
    )
    name = models.CharField(max_length=300)
    description = models.CharField(max_length=300, blank=True)
    status = models.CharField(max_length=100, choices=ProductStatus.choices, default=ProductStatus.ACTIVE)
    category = models.CharField(
        max_length=300,
        choices=ProductCategory.choices,
        default=ProductCategory.EXTRA,
        help_text="Important that this is correct as it is used for suggested order and more.",
    )
    product_type = models.CharField(
        max_length=300,
        choices=ProductCategory.choices,
        blank=True,
        null=True,
        help_text="Extended product type, used in invoices, emails",
    )
    tax_category = models.ForeignKey(
        TaxCategory,
        CASCADE,
        default=lookup_default_tax_category,
        help_text="The tax category is used to send the decide the tax codes to use when "
        "creating invocies for the products.",
    )
    model = models.CharField(max_length=300, choices=ProductModel.choices, null=True, blank=True)

    # We use this to e.g. add the correct subscription types when a customer buys collars or subscriptions.
    collar_product_type = models.CharField(
        max_length=300,
        choices=PRODUCT_TYPE,
        null=True,
        blank=True,
        help_text="The type of collar product this product relates to. This is not only for collars, but also related "
        "products like batteries, subscriptions etc. Important that this is correct as it is used for suggested "
        "orders and more.",
    )

    is_inventory_product = models.BooleanField(
        default=True,
        help_text="Uncheck for non-physical products like freight or subscriptions.",
    )
    is_subscription_product = models.BooleanField(
        default=False,
        help_text="True if 12 months subscription is to be added when a customer buys this product.",
    )
    include_in_weee = models.BooleanField(
        default=False,
        help_text="This field is used to determine if the product "
        "should be included in the WEEE report for battery usage.",
    )
    image = models.ImageField(
        storage=get_product_image_file_storage,
        help_text=_("File can be PNG, JPG, JPEG or GIF"),
        null=True,
        blank=True,
    )
    compound_product = models.BooleanField(
        default=False,
        help_text="Is this product made up of other products, for example is this a complete collar with battery.",
    )
    draft = models.BooleanField(
        default=False,
        help_text="Is this product automatically created by the order sync",
    )

    child_products = models.ManyToManyField(
        "Product",
        through="CompoundProductLink",
        through_fields=("compound_product", "child_product"),
        related_name="parent_products",
    )

    shop_markets = models.ManyToManyField(
        NofenceCompany,
        blank=True,
        related_name="shop_products",
        help_text="The product will show in the web shop for the selected markets.",
    )
    shop_description = models.TextField(blank=True, default="")
    shop_exclude_countries = CountryField(
        multiple=True,
        blank=True,
        default="",
        help_text="This product will not be show in in the web shop for the specified countries.",
    )

    unit_weight = models.PositiveIntegerField(
        null=True,
        blank=True,
        help_text="Weight of a single unit in grams. "
        "Must be specified for extra items sold in the web shop. This is used to calculate freight.",
    )

    prices: QuerySet["ProductPrice"]

    objects: ProductManager = ProductManager()

    class Meta:
        constraints = [
            models.UniqueConstraint(
                fields=["category", "collar_product_type"],
                condition=Q(category="BUYABLE_SUBSCRIPTION"),
                name="unq_buyable_subscription_product",
            ),
        ]

    def __str__(self):
        return self.sku

    def get_stock_web_shop(self, warehouse: Warehouse) -> int:
        if not self.is_inventory_product:
            # Not an inventory product, so we always have stock.
            return 99999
        from .inventory import Inventory

        warehouse_location = warehouse.web_shop_primary_location
        if warehouse.using_late_assembly and self.compound_product:
            quantity_list = []
            for child_product in self.child_products.all():
                child_inventory = Inventory.objects.filter(product=child_product, location=warehouse_location).first()
                if child_inventory:
                    stock_buffer = (
                        child_inventory.stock_buffer
                        if child_inventory.stock_buffer is not None
                        else warehouse.stock_buffer
                    )
                    quantity_list.append(child_inventory.available_quantity - stock_buffer)
                else:
                    return 0
            return max(min(quantity_list if quantity_list else [0]), 0)
        inventory = Inventory.objects.filter(product=self, location=warehouse_location).first()
        if not inventory:
            return 0
        stock_buffer = inventory.stock_buffer if inventory.stock_buffer is not None else warehouse.stock_buffer
        return max(inventory.available_quantity - stock_buffer, 0)

    def get_shop_data(self, customer: Customer):
        from .shop import Basket, BasketLine

        basket: Basket = customer.user.baskets.filter(status=Basket.BasketStatus.PENDING).first()
        basket_line: BasketLine = basket.basket_lines.filter(product=self).first()
        if self.is_buyable_subscription():
            price = basket.get_subscription_y2_product_for_current_basket(
                self.get_collar_product_type(),
            ).get_price_for_country(customer.country)
        else:
            price = self.get_price_for_country(customer.country)
        return {
            "product": self,
            "price": price,
            "web_shop_stock": self.get_stock_web_shop(
                customer.nofence_company.get_shipment_warehouse(customer.country),
            ),
            "quantity": basket_line.quantity if basket_line else 0,
            "can_quantity_be_decreased_in_basket": basket_line.can_quantity_be_decreased_in_basket()
            if basket_line
            else False,
        }

    def get_related_buyable_subscription_product(self):
        return Product.objects.get(
            category=Product.ProductCategory.BUYABLE_SUBSCRIPTION,
            collar_product_type=self.collar_product_type,
        )

    def is_buyable_subscription(self) -> bool:
        return self.category == Product.ProductCategory.BUYABLE_SUBSCRIPTION

    def is_freight_product_type(self) -> bool:
        return self.product_type == Product.ProductCategory.FREIGHT

    def is_subscription_product_type(self) -> bool:
        return self.product_type == Product.ProductCategory.SUBSCRIPTION

    def is_available_for_customer(self, customer):
        if customer.country in self.shop_exclude_countries:
            return False

        is_available_in_market = customer.nofence_company in self.shop_markets.all()
        country_price_exists = customer.country in [price.country for price in self.prices.all()]

        if customer.country in settings.COUNTRIES_EUROZONE:
            eurozone_price_exists = self.prices.filter(country="EZ").exists()
            country_price_exists = eurozone_price_exists or country_price_exists

        # SW-2898 - special case for the cattle product 900-001 and C25.50-B21-NS2gn32 due to 0V
        # meaning we'll price up both in ES, then hide one or the other
        if customer.nofence_company == NOFENCE_ES:
            if customer.country != "ES" and self.sku in ("C25.50-B21-NS2gn32", "C2-B2.1-20Ah"):
                return False
            if customer.country == "ES" and self.sku in ("27-0124"):
                return False

        return is_available_in_market and country_price_exists

    @property
    def image_url(self):
        if self.image:
            return self.image.url
        return None

    @property
    def image_url_with_fallback(self):
        return self.image_url or static("default-product-image.svg")

    @property
    def supports_late_assembly(self) -> bool:
        # Allow late assembly for all compound product for now
        return self.compound_product

    @property
    def product_display_name(self):
        if not hasattr(self, "display_name"):
            return self.name
        return self.display_name.name

    def is_non_collar_item(self):
        """True for any non collar item."""
        return self.category not in [Product.ProductCategory.COLLAR]

    def _get_product_price_for_country(self, country: str, nofence_company_code: str = None) -> "ProductPrice":
        try:
            # TODO: we still need to use freight prices for IE when the nofence_company is NOFENCE_UK
            if (
                self.category == Product.ProductCategory.FREIGHT
                and nofence_company_code == NOFENCE_UK
                and country == "IE"
            ):
                return self.prices.get(country="IE")

            if country in settings.COUNTRIES_EUROZONE:
                price = self.prices.filter(country="EZ").first()
                if price:
                    return price
                return self.prices.get(country=country)
            return self.prices.get(country=country)
        except ProductPrice.DoesNotExist:
            raise ValueError(f"No price information for country {country} for product {self}")

    def get_price_for_country(self, country: str, nofence_company_code: str = None) -> Money:
        # We just let it crash if we are missing price information for the country
        return self._get_product_price_for_country(
            country=country,
            nofence_company_code=nofence_company_code,
        ).price

    def _get_flat_rate_tax_definition(self, customer: Customer) -> TaxDefinition:
        if self.category in [Product.ProductCategory.SUBSCRIPTION, Product.ProductCategory.BUYABLE_SUBSCRIPTION]:
            rate = TaxCategory.TaxCategoryType.FLAT_RATE_SCHEME_SUBS
        else:
            rate = TaxCategory.TaxCategoryType.FLAT_RATE_SCHEME_HW
        return customer.nofence_company.tax_definitions.get(country=customer.country, tax_category__category_type=rate)

    def _get_tax_definition_for_customer(self, customer: Customer) -> TaxDefinition:
        try:
            if customer.flat_rate_scheme:
                return self._get_flat_rate_tax_definition(customer)
            if customer.country == "IE" and customer.nofence_company.code == "ES" and not customer.vies_valid:
                # If the IE customer doesn't have a valid VIES, we use the tax definition for ES, i.e. 21%
                if self.tax_category.category_type == TaxCategory.TaxCategoryType.SERVICES:
                    return TaxDefinition.objects.get(
                        nofence_company=NofenceCompany.objects.get(code=NOFENCE_ES),
                        country="IE",
                        tax_category=self.tax_category,
                    )
                else:
                    return TaxDefinition.objects.get(
                        nofence_company=NofenceCompany.objects.get(code=NOFENCE_ES),
                        country="ES",
                        tax_category=self.tax_category,
                    )
            return customer.nofence_company.tax_definitions.get(
                country=customer.country,
                tax_category=self.tax_category,
            )
        except TaxDefinition.DoesNotExist:
            logger.info(
                f"No tax definition for {customer.nofence_company}, country {customer.country} and "
                f"tax category {self.tax_category}",
            )
            try:
                return customer.nofence_company.tax_definitions.get(
                    country=customer.country,
                    tax_category__default=True,
                )
            except TaxDefinition.DoesNotExist:
                msg = (
                    f"No tax definition for {customer.nofence_company}, country {customer.country} and "
                    f" {self.tax_category} or default tax category"
                )
                logger.exception(msg)
                raise TaxDefinition.DoesNotExist(msg)

    def get_vat_rate_for_customer(self, customer: Customer) -> Decimal:
        return self._get_tax_definition_for_customer(customer).tax_rate

    def get_vat_for_customer(self, customer: Customer) -> Money:
        price = self.get_price_for_country(customer.country)
        tax = self._get_tax_definition_for_customer(customer)
        return price * tax.tax_rate / Decimal("100.0")

    def get_tax_code_for_customer(self, customer: Customer):
        return self._get_tax_definition_for_customer(customer).get_tax_code()

    def get_tax_message_for_customer(self, customer: Customer) -> str:
        """Returns the tax message that we should show for this product on the vat description on the invoice line.

        We use this when rendering e.g. a payment request where we might not yet have access to the invoice document.
        """
        tax = self._get_tax_definition_for_customer(customer)
        return tax.get_tax_message()

    def get_tax_message_for_tax_code(
        self,
        nofence_company: NofenceCompany,
        customer: Customer,
        tax_code: str,
    ) -> Optional[str]:
        """Returns the tax message that we should show for this product on the vat description on the invoice line.

        We use this when rendering e.g. a invoice document where we have access to the invoice document from the
        accounting system. This way we the rendering should match what we have defined in the external system.
        """
        try:
            # We could take the nofence company from the customer, but then old tax codes might break
            # if we move a customer between nofence companies.
            tax_def: TaxDefinition = nofence_company.tax_definitions.get(
                country=customer.country,
                tax_category=self.tax_category,
                tax_code__tax_code=tax_code,
            )
            # We only return the message if we have a definition with a matching tax code.
            # E.g. if we define a new tax code for certain products, we don't want to render
            # old invoices with the new message if they didn't use the new tax code. So we
            # take the tax code from the invoice document and find a matching tax definition.
            return tax_def.get_tax_message()
        except TaxDefinition.DoesNotExist:
            pass

    def is_collar_product(self) -> bool:
        return self.category == Product.ProductCategory.COLLAR

    def get_collar_type(self) -> Optional[str]:
        if self.is_collar_product() and self.model:
            if self.model.startswith("C"):
                return CATTLE
            elif self.model.startswith("SG"):
                return SHEEP_GOAT

    def get_collar_product_type(self) -> Optional[str]:
        return self.collar_product_type

    def clean(self):
        if self.pk:
            if not self.compound_product:
                child_products = list(self.child_products.all())
                if len(child_products) > 0:
                    raise ValidationError(
                        {
                            "compound_product": "Cannot remove compond_product flag, "
                            f"because the product has the child products: {child_products}. "
                            "Child products must be removed before the compound flag can be removed",
                        },
                    )
            else:
                parents = list(self.parent_products.all())
                if len(parents) > 0:
                    raise ValidationError(
                        {
                            "compound_product": "Cannot set as compond product "
                            f"because this product is a child product of {parents}. "
                            "Nested compound products are not supported.",
                        },
                    )

        if self.collar_product_type is None:
            if self.is_buyable_subscription():
                raise ValidationError(
                    {"collar_product_type": "Collar product type must be set for buyable subscriptions"},
                )
            if self.is_collar_product():
                raise ValidationError({"collar_product_type": "Collar product type must be set for collars"})

        if self.is_buyable_subscription():
            if self.is_inventory_product:
                raise ValidationError(
                    {"is_inventory_product": "Buyable subscriptions should not be inventory products"},
                )

        return super().clean()


class ProductDisplayName(BaseModel):
    product = models.OneToOneField(Product, CASCADE, blank=True, null=True, related_name="display_name")
    name = models.CharField(
        max_length=100,
        blank=True,
        null=True,
        help_text="Name to be displayed on customer facing stuff like packing list and orders.",
    )


class CompoundProductLink(BaseHistoryModel):
    compound_product = models.ForeignKey(
        Product,
        PROTECT,
        limit_choices_to={"compound_product": True},
        related_name="compound_product_links",
    )
    child_product = models.ForeignKey(
        Product,
        PROTECT,
        limit_choices_to={"compound_product": False},
        related_name="child_product_links",
    )

    class Meta:
        constraints = [
            models.UniqueConstraint(
                fields=["compound_product", "child_product"],
                name="unique_compound_prouct_link",
            ),
        ]

    def clean(self):
        # Ideally we should have this on the db level, but not sure how to do that without
        # resorting to triggers.
        if self.child_product.compound_product:
            raise ValidationError(
                f"Product {self.child_product} is a compound product. Nested compound products not supported.",
            )

        return super().clean()


class AllCountries(Countries):
    """
    This class supports custom country (EZ) (Euro Zone) for the CountryField
    """

    first = settings.COUNTRIES_FIRST + ["EZ"]

    override = {
        # EZ -> countries in Euro Zone
        "EZ": _("Euro Zone"),
    }


class ProductPrice(BaseHistoryModel):
    product = models.ForeignKey(Product, CASCADE, related_name="prices")
    country = CountryField(multiple=False, countries=AllCountries)
    price = MoneyField(max_digits=14, decimal_places=2)

    class Meta:
        constraints = [
            models.UniqueConstraint(fields=["product", "country"], name="unq_price_product_country"),
        ]

    def __str__(self):
        return f"{self.product} - {self.country} - {self.price}"


class ShipmentQuerySet(models.QuerySet):
    def annotate_with_is_sg(self):
        return self.annotate(
            annotated_is_sg=Exists(
                ShipmentLine.objects.filter(
                    shipment=OuterRef("pk"),
                    product__category=Product.ProductCategory.COLLAR,
                    product__collar_product_type=SHEEP_GOAT,
                ),
                then=True,
            ),
        )

    def annotate_with_is_cattle(self):
        return self.annotate(
            annotated_is_cattle=Exists(
                ShipmentLine.objects.filter(
                    shipment=OuterRef("pk"),
                    product__category=Product.ProductCategory.COLLAR,
                    product__collar_product_type=CATTLE,
                ),
            ),
        )

    def annotate_with_is_bracket_replacement(self):
        return self.annotate(
            annotated_is_bracket_replacement=Exists(
                ShipmentLine.objects.filter(
                    shipment=OuterRef("pk"),
                    product__sku__in=SKUS_REPLACEMENT_BRACKET,
                ),
            ),
        )


class ShipmentBaseManager(BaseManager.from_queryset(ShipmentQuerySet)):
    pass


class ShipmentAnnotatedManager(ShipmentBaseManager):
    def get_queryset(self):
        return (
            super()
            .get_queryset()
            .annotate_with_is_sg()
            .annotate_with_is_cattle()
            .annotate_with_is_bracket_replacement()
        )


def get_shipment_labels_file_storage():
    return storages["s3_shipment_labels"]


class Shipment(BaseHistoryModel):
    class Status(models.TextChoices):
        CANCELLED = "CANCELLED"
        COMPLETED = "COMPLETED"
        CONFIRMED = "CONFIRMED"
        DELETED = "DELETED"
        DELIVERED = "DELIVERED"
        INVOICED = "INVOICED"
        OPEN = "OPEN"
        IN_PROGRESS = "IN_PROGRESS"
        PENDING_DELIVERY = "PENDING_DELIVERY"
        RECEIVED = "RECEIVED"

    class PickingStatus(models.TextChoices):
        READY_TO_PICK = "READY_TO_PICK"
        PICKING_IN_PROGRESS = "PICKING_IN_PROGRESS"
        REGISTRATION_COMPLETED = "REGISTRATION_COMPLETED"
        COMPLETED = "COMPLETED"

    PICKING_IN_PROGRESS_STATUSES = [
        PickingStatus.PICKING_IN_PROGRESS,
        PickingStatus.REGISTRATION_COMPLETED,
    ]

    # Shipment statuses that we consider to be completed
    # Hapro shipments uses DELIVERED and DONE, the rest uses COMPLETED
    # DONE is the final status on the hapro order object, but we are currently
    # using the status of the shipments linked to the hapro order, so the hapro
    # shipments will probably always have the final status set to DELIVERED, but
    # if the shipments list on the hapro order is empty, the status could be set to DONE
    DELIVERED_STATUSES = [
        Status.COMPLETED,
        Status.DELIVERED,
    ]

    # These statuses indicate that the processing of this shipment is done
    FINAL_STATUSES = [
        Status.COMPLETED,
        Status.DELIVERED,
        Status.CANCELLED,
        Status.DELETED,
    ]

    objects = ShipmentBaseManager()
    objects_annotated = ShipmentAnnotatedManager()

    shipment_no = models.CharField(max_length=100, unique=True)

    packing_started_at = models.DateTimeField(blank=True, null=True)
    registration_completed_at = models.DateTimeField(blank=True, null=True)
    packing_completed_at = models.DateTimeField(blank=True, null=True)

    status = models.CharField(max_length=100, blank=True, choices=Status.choices)
    delivered_date = models.DateTimeField(blank=True, null=True)
    label_file = models.FileField(storage=get_shipment_labels_file_storage, blank=True, null=True)
    label_request_payload = models.JSONField(default=dict, blank=True, null=True)
    label_response = models.JSONField(default=dict, blank=True, null=True)

    transporter = models.CharField(max_length=200, blank=True, null=True, choices=TransporterName.choices)
    tracking_number = models.CharField(max_length=200, blank=True)
    return_label_tracking_number = models.CharField(max_length=200, blank=True)
    inco_terms = models.CharField(max_length=10, choices=IncoTerms.choices, null=True, blank=True)

    from_warehouse = models.ForeignKey(Warehouse, CASCADE, related_name="shipments")
    customer = models.ForeignKey(Customer, CASCADE, null=True, blank=True, related_name="shipments")
    sales_order = models.ForeignKey("SalesOrder", CASCADE, null=True, blank=True, related_name="shipments")

    # Used for shipments between Nofence Companies
    invoice_document = models.OneToOneField(
        "InvoiceDocument",
        CASCADE,
        null=True,
        blank=True,
        help_text="Used for shipments between Nofence Companies",
    )

    # Used to detect deleted shipments in Visma. Visma sometimes returns the wrong status so we have to
    # check at least twice before marking a shipment as deleted. This timestamp is used to record the
    # last time we detected the shipments as deleted so that we can do another check a bit later.
    deleted_first_check_at = models.DateTimeField(null=True, blank=True)

    tracker = FieldTracker()

    lines: models.QuerySet["ShipmentLine"]
    picked_units: models.QuerySet["ShipmentPickedUnit"]

    def __str__(self):
        return f"{self.shipment_no}"

    @property
    def picking_status(self) -> str:
        status = self.PickingStatus.READY_TO_PICK
        if self.packing_completed_at:
            status = self.PickingStatus.COMPLETED
        elif self.registration_completed_at:
            status = self.PickingStatus.REGISTRATION_COMPLETED
        elif self.packing_started_at:
            status = self.PickingStatus.PICKING_IN_PROGRESS
        return status

    @property
    def ready_to_pick(self) -> bool:
        return self.picking_status == self.PickingStatus.READY_TO_PICK

    @property
    def registration_completed(self) -> bool:
        return self.picking_status == self.PickingStatus.REGISTRATION_COMPLETED

    @property
    def has_registered_all_items(self) -> bool:
        res = self.lines.annotate(total_picked_quantity=Sum("picked_units__quantity")).filter(
            total_picked_quantity=F("quantity"),
        )

        return res.count() == self.lines.count()

    @property
    def item_quantity(self) -> int:
        res = self.lines.aggregate(quantity=Sum("picked_units__quantity"))
        return res["quantity"]

    @property
    def total_units(self):
        if not self.lines:
            return None
        return sum([so.quantity for so in self.lines.all()])

    def shipment_details(self) -> dict:
        if self.invoice_document:
            return {
                "reference_number": self.invoice_document.reference_number,
                "total": self.total,
                "freight_total": self.invoice_document.freight_total,
                "freight_currency": self.invoice_document.total.currency,
            }
        elif self.sales_order:
            return {
                "reference_number": self.sales_order.generated_order_number,
                "total": self.total,
                "freight_total": self.sales_order.freight_total,
                "freight_currency": self.sales_order.currency,
            }
        return {}

    @property
    def total(self) -> Money:
        if self.sales_order:
            return self.sales_order.total_for_shipment
        elif self.invoice_document:
            return self.invoice_document.data.total_no_tax
        return None

    @staticmethod
    def is_all_shipments_done(shipments: List["Shipment"]) -> bool:
        return all(shipment.status in Shipment.FINAL_STATUSES for shipment in shipments)

    @staticmethod
    def is_at_least_one_shipment_delivered(shipments: List["Shipment"]) -> bool:
        return any(shipment.status in Shipment.DELIVERED_STATUSES for shipment in shipments)

    def guard_field(self, property):
        if getattr(self, property, None) is None:
            raise ValueError(f"Shipment must be annotated with {property}, have you used the annotated manager?")
        return getattr(self, property)

    def is_in_final_status(self):
        return self.status in Shipment.FINAL_STATUSES

    def is_delivered(self):
        return self.status in Shipment.DELIVERED_STATUSES

    def is_from_visma(self):
        return self.sales_order is None or self.sales_order.nofence_company == NOFENCE_NO

    def is_return_and_replacement(self):
        return (
            self.sales_order is not None
            and self.sales_order.order_type == SalesOrder.Type.RR
            and self.sales_order.return_and_replacement is not None
        )

    def return_old_collars(self):
        return self.is_return_and_replacement() and self.sales_order.return_and_replacement.return_old_collars

    def is_cattle_collar_shipment(self) -> bool:
        return self.guard_field("annotated_is_cattle")

    def is_sg_collar_shipment(self) -> bool:
        return self.guard_field("annotated_is_sg")

    def is_bracket_replacement_shipment(self) -> bool:
        return self.guard_field("annotated_is_bracket_replacement")

    def is_intercompany_shipment(self):
        """True if the shipment is between two Nofence Companies."""
        return (
            self.invoice_document is not None
            and self.customer is not None
            and self.customer.is_nofence_company_shipment_receiver
        )

    def get_in_transit_warehouse(self) -> Optional[Warehouse]:
        """returns the location that the in transit stock is stored for the shipment.

        Only useful for inter nofence company shipments

        NOTE: This is just a hard coded value at the moment, if we decide to change the
              logic warehouse here, historical shipments will also start returning the
              new warehouse.
        """
        # The inventory tracked under that sending warehouse until it is received.
        return self.from_warehouse

    def get_destination_warehouse(self) -> Optional[Warehouse]:
        """returns the destination warehouse of the shipment.

        Only useful for inter nofence company shipments
        """
        # Special case for hapro
        if "Hapro" in self.customer.name:
            try:
                return Warehouse.objects.hapro()
            except Warehouse.DoesNotExist:
                pass
        # The inventory is sent to the receiving customer's warehouse.
        nofence_company = self.customer.nofence_company
        return nofence_company.get_shipment_warehouse(self.customer.country)

    def save(self, *args, **kwargs):
        if self.tracker.has_changed("status"):
            if self.status in self.DELIVERED_STATUSES:
                if self.packing_started_at is None:
                    self.packing_started_at = timezone.now()
                if self.packing_completed_at is None:
                    self.packing_completed_at = timezone.now()
                if self.registration_completed_at is None:
                    self.registration_completed_at = timezone.now()
                if self.delivered_date is None:
                    self.delivered_date = timezone.now()

        super().save(*args, **kwargs)

    @transaction.atomic
    def set_delivered(self):
        self.status = "DELIVERED"
        self._change_reason = "Shipment delivered"
        self.save()

    def link_picked_serials(self, collars: List[Collar]):
        """Creates a picked unit with the serial from each collar

        Currently we don't have the information needed to link the serial to the correct sku if there are multiple
        collar skus on the shipment. So we make a best effort guess.

        Normally we will have only a single collar sku on shipment so it should be a big problem.
        """

        collar_lines: List[ShipmentLine] = [
            line for line in self.lines.order_by("id").all() if products.is_collar_sku(line.sku)
        ]

        collar_skus = []
        for line in collar_lines:
            collar_skus.extend([line.sku] * int(line.quantity))

        units = []
        for i, collar in enumerate(collars):
            sku = ""
            if collar_skus:
                # If we have more collars than expected skus, someone have probably picked too much,
                # but for now we just reuse the last sku in this case
                sku = collar_skus[i] if i < len(collar_skus) else collar_skus[-1]
            units.append((sku, collar.serial_no))

        self.link_picked_units(units)

    def link_picked_units(self, units: List[Tuple[str, str]]):
        """Creates a picked unit for each specified unit
        where unit is a (sku, serial_no)
        we assume a non empty string to be a collar and search though the Collar objects for it
        """
        for sku, serial_no in units:
            collar = None
            if serial_no != "":
                try:
                    collar = Collar.objects.get(serial_no=serial_no)
                except Exception as e:
                    logger.warn(f"Cannot find collar {serial_no} {e}")
            self.picked_units.create(
                sku=sku,
                serial_no=serial_no,
                quantity=1,
                collar=collar,
            )

    def get_picked_collars(self) -> List[Collar]:
        return [pu.collar for pu in self.picked_units.filter(collar__isnull=False).select_related("collar").all()]

    def get_barcode_svg(self) -> str:
        barcode = BytesIO()
        ShipmentBarcode(
            str(self.shipment_no),
            writer=BarcodeSVGWriter(),
        ).write(barcode, text="")
        return barcode.getvalue().decode("utf-8")

    @property
    def tracking_url(self) -> Optional[str]:
        if self.label_response:
            if "tracking_urls" in self.label_response:
                return self.label_response["tracking_urls"][0]

        urls = {
            TransporterName.BRING: "https://tracking.bring.com/tracking/{tracking_number}",
            TransporterName.DHL: "https://www.dhl.com/no-en/home/<USER>/tracking-express.html?tracking-id={tracking_number}&submit=1",
            TransporterName.DHL_EXPRESS: "https://www.dhl.com/no-en/home/<USER>/tracking-express.html?tracking-id={tracking_number}&submit=1",
            TransporterName.CORREOS: "https://www.correos.es/es/es/herramientas/localizador/envios/detalle?"
            "tracking-number={tracking_number}",
            TransporterName.PARCEL_FORCE: "https://www.parcelforce.com/track-trace?trackNumber={tracking_number}",
            TransporterName.OTHER: "{tracking_number}",
        }
        transporter = self.transporter
        if self.tracking_number and transporter in urls:
            return urls.get(transporter).format(tracking_number=self.tracking_number)
        return None


class ShipmentLine(BaseHistoryModel):
    shipment = models.ForeignKey(Shipment, CASCADE, related_name="lines")
    product = models.ForeignKey(Product, PROTECT, null=True, related_name="shipment_lines")

    sku = models.CharField(max_length=100)
    quantity = models.DecimalField(max_digits=14, decimal_places=2, blank=False, default=1)

    # Should no longer be used, but currently kept for old data:
    serial_no = models.CharField(max_length=100, blank=True)

    def __str__(self):
        return f"{self.sku} - #{self.shipment.shipment_no} - {self.quantity}"

    def clean(self):
        if self.product and not self.product.is_inventory_product:
            raise ValidationError({"product": "Cannot add none inventory/shippable product to shipment"})
        super(ShipmentLine, self).clean()

    @property
    def picked_units_quantity(self):
        return self.picked_units.aggregate(Sum("quantity"))["quantity__sum"] or 0

    @property
    def shelf_location(self):
        from .inventory import Inventory

        inventory = Inventory.objects.filter(
            product=self.product,
            location__warehouse=self.shipment.from_warehouse,
        ).first()
        if inventory:
            return inventory.shelf

    def order_price(self) -> str:
        from .invoices import InvoiceDocument

        invoice: InvoiceDocument = self.shipment.invoice_document
        if invoice:
            lines = invoice.data.lines
            for line in lines:
                if line.item == self.product.sku:
                    return line.price

        elif self.shipment.sales_order:
            return self.product.get_price_for_country(
                self.shipment.customer.country,
                self.shipment.customer.nofence_company.code,
            )
        return "0"

    def is_using_late_assembly(self) -> bool:
        return len(self.get_individual_parts_for_late_assembly()) > 0

    def get_individual_parts_for_late_assembly(self) -> List[Product]:
        from .inventory import SalesOrderLineReservation

        if self.shipment.sales_order is None or self.product is None:
            return []
        return [
            it.product
            for it in SalesOrderLineReservation.objects.filter(
                sales_order_line__order=self.shipment.sales_order,
                sales_order_line__product=self.product,
            ).order_by("product__name")
        ]

    def get_total_picked_quantity(self) -> int:
        return self.picked_units.aggregate(total=Sum("quantity"))["total"] or 0


class ShipmentPickedUnit(BaseHistoryModel):
    shipment = models.ForeignKey(Shipment, CASCADE, related_name="picked_units")
    line = models.ForeignKey(ShipmentLine, CASCADE, null=True, blank=True, related_name="picked_units")

    sku = models.CharField(max_length=100)
    serial_no = models.CharField(max_length=100, blank=True)
    quantity = models.IntegerField(blank=False, default=1)
    collar = models.ForeignKey(Collar, SET_NULL, related_name="picked_collar", null=True, blank=True, default=None)

    def __str__(self):
        return f"{self.sku} - {self.serial_no} - {self.quantity}"


class ShipmentTrackingData(BaseHistoryModel):
    """Data extracted from ftp tracking file uploads from Inission

    This model is used to backup the uploaded data before the files are deleted
    from the FTP disk. This allows us to lookup and link tracking numbers to
    shipments that do not yet exists in billy when the shipment file is uploaded.

    This model is not linked directly to the shipment model, instead the
    shipment numbers are copied into the shipments when a match is found.
    """

    shipment_no = models.CharField(max_length=300)
    tracking_number = models.CharField(max_length=300, db_index=True)

    receiver_postal_code = models.CharField(max_length=300)
    receiver_address = models.CharField(max_length=300)
    receiver_name = models.CharField(max_length=300)
    parcels = models.IntegerField(default=1)

    is_linked = models.BooleanField(default=False)

    raw_data = models.BinaryField(default=b"")

    class Meta:
        unique_together = ["shipment_no", "tracking_number"]

    def __str__(self):
        return f"{self.shipment_no} - {self.tracking_number}"


class ShipmentTrackingFileLog(BaseModel):
    """Used for logging the content of the all the files uploaded to the shipment tracking ftp EFS

    Just for debugging.
    """

    filename = models.CharField(max_length=500)
    file_created_at = models.DateTimeField()
    content = models.BinaryField(default=b"")


class SalesOrderSource(models.TextChoices):
    """The source of the sales order.

    Currently only hubspot. In the future if we make it possible
    to order directly in billy we could have a BILLY source etc.
    """

    HUBSPOT = "HUBSPOT"
    MANUAL = "MANUAL"
    WEBSHOP = "WEBSHOP"


class SalesOrderStatus(models.TextChoices):
    CONFIRMED = "CONFIRMED", _("Confirmed")
    PROPOSAL = "PROPOSAL", _("PROPOSAL")
    PENDING_PAYMENT = "PENDING_PAYMENT", _("Pending Payment")
    PENDING_DELIVERY = "PENDING_DELIVERY", _("Pending Delivery")
    DELIVERED = "DELIVERED", _("Delivered")
    RETURNED = "RETURNED", _("Returned")
    DELETED = "DELETED", _("Deleted")


class SalesOrderLineStatus(models.TextChoices):
    ACTIVE = "ACTIVE", _("Active")
    DELETED = "DELETED", _("Deleted")


class SalesOrderManager(models.Manager):
    @property
    def from_hubspot(self):
        return self.filter(source=SalesOrderSource.HUBSPOT)

    @property
    def visma_legacy_order(self):
        return self.filter(
            accounting_system=SalesOrderAccountingSystem.VISMA,
        )


@dataclasses.dataclass
class ProductCounts:
    collars: int
    chargers: int
    is_rr_order: bool = False

    @property
    def format_display(self):
        values = []
        if self.collars > 0:
            if self.is_rr_order:
                values.append(
                    ngettext(
                        "%(count)d Replacement Collar",
                        "%(count)d Replacement Collars",
                        self.collars,
                    )
                    % {"count": self.collars},
                )
            else:
                values.append(
                    ngettext(
                        "%(count)d Collar",
                        "%(count)d Collars",
                        self.collars,
                    )
                    % {"count": self.collars},
                )
        if self.chargers > 0:
            values.append(
                ngettext(
                    "%(count)d Charger",
                    "%(count)d Chargers",
                    self.chargers,
                )
                % {"count": self.chargers},
            )

        return ", ".join(values)


@dataclasses.dataclass
class MyOrderLine:
    """User for displaying the order on the myorders page"""

    product_type: str
    contract_expiry: str
    sku: str
    quantity: int
    warranty_expiry: date = None


class SalesOrder(BaseHistoryModel):
    class PaymentStatus(models.TextChoices):
        PENDING_PAYMENT = "PENDING_PAYMENT"
        MISSING_INVOICE = "MISSING_INVOICE"
        PAID = "PAID"
        PROPOSAL = "PROPOSAL", "PROPOSAL"

    class Type(models.TextChoices):
        SO = "SO", "SO"
        RR = "RR", "RR"
        PROPOSAL = "PROPOSAL", "PROPOSAL"

    class PaymentMethod(models.TextChoices):
        CARD = "CARD"
        BANK_TRANSFER = "BANK_TRANSFER"

        # This assumes that the payment has been handled outside of billy.
        # When this is set the order is assumed to be paid, and will transition
        # to pending delivery etc.
        MANUALLY_PAID = "MANUALLY_PAID"

    customer = models.ForeignKey(Customer, PROTECT, related_name="sales_orders")
    currency = CurrencyField(choices=CURRENCY_CHOICES)
    dealname = models.CharField(max_length=300, blank=True)
    description = models.CharField(max_length=300, blank=True)
    status = models.CharField(max_length=300, choices=SalesOrderStatus.choices)
    payment_status = models.CharField(
        max_length=300,
        choices=PaymentStatus.choices,
        default=PaymentStatus.PENDING_PAYMENT,
    )
    payment_method = models.CharField(max_length=300, null=True, blank=True, choices=PaymentMethod.choices)
    financing_partner = models.ForeignKey(
        "FinancingPartner",
        models.SET_NULL,
        help_text="if set, the invoice will be sent to the linked financing provider",
        related_name="sales_orders",
        null=True,
        blank=True,
    )
    estimated_delivery_date = models.DateField(null=True, blank=True)

    # Keep track of when the order was first set as delivered. This is currently used by the collar blocking
    # to only block newly delivered orders.
    delivered_at = models.DateTimeField(null=True, blank=True)

    # Track when key status changes first occurred
    booked_at = models.DateTimeField(
        null=True, blank=True, help_text="When payment status first changed to PENDING_PAYMENT"
    )
    paid_at = models.DateTimeField(null=True, blank=True, help_text="When payment status first changed to PAID")
    deleted_at = models.DateTimeField(null=True, blank=True, help_text="When status first changed to DELETED")
    shipped_at = models.DateTimeField(null=True, blank=True, help_text="When status first changed to DELIVERED")

    order_expiry_date = models.DateField(null=True, blank=True)

    source = models.CharField(max_length=300, choices=SalesOrderSource.choices)
    source_id = models.CharField(max_length=300, blank=True)

    order_type = models.CharField(max_length=300, choices=Type.choices, default=Type.SO)

    accounting_system = models.CharField(
        max_length=300,
        choices=SalesOrderAccountingSystem.choices,
        null=True,
        blank=True,
    )
    # The id and ref is references to the sales order in the external systems
    # The ref value is intended for human readable references if available.
    # For old visma pre paid order the order_id is the VAT invoice, but this was changed
    # in SW-2119, for orders after this the order_id points to the payment request.
    # If the order_id is set, but the payment_request_id is not set, this indicates
    # that the invoice was created before SW-2119.
    # If the accounting_payment_request_id value is set, it will always be the same
    # as the accounting_order_id.
    accounting_order_id = models.CharField(max_length=300, default="", blank=True)
    accounting_order_ref = models.CharField(max_length=300, default="", blank=True)

    accounting_credit_note_id = models.CharField(max_length=300, default="", blank=True)
    accounting_credit_note_applied = models.BooleanField(max_length=300, default=False)

    # For visma we create two invoice documents, one payment request and one vat invoice.
    accounting_payment_request_id = models.CharField(max_length=300, default="", blank=True, null=True)
    accounting_vat_invoice_id = models.CharField(max_length=300, default="", blank=True, null=True)

    # At the moment only used for Xero
    order_confirmation_email_sent = models.BooleanField(default=False)
    vat_invoice_email_sent = models.BooleanField(default=False)
    payment_request_email_sent = models.BooleanField(default=False)
    proposal_email_sent = models.BooleanField(default=False)
    # A customer may have an external purchase order number he needs on his order/invoice
    customer_purchase_order_ref = models.CharField(max_length=300, default="", blank=True)

    inco_terms = models.CharField(max_length=10, choices=IncoTerms.choices, null=True, blank=True)

    shipment_warehouse = models.ForeignKey(
        Warehouse,
        PROTECT,
        null=True,
        blank=True,
        help_text="The products for the shipment of the order will be taken from this warehouse",
    )

    # Storing nofence company on the order, we could get this through the customer, but then if we change the
    # company on the customer, this will also change the company on all order existing order, which may not
    # be what we want.
    nofence_company = models.ForeignKey(NofenceCompany, PROTECT)

    pre_paid = models.BooleanField(
        default=False,
        help_text=_(
            "This sales orders must be fully paid before it can move on to PENDING_DELIVERY. "
            "Don't change this unless you know what you're doing",
        ),
    )

    # We could have used the existing pre_paid flag, but that is also used for old orders
    # created before the pre-paid flow was implemented.
    post_payment = models.BooleanField(
        default=False,
        help_text="True if this order is allowed to bypass the pre-paid flow.",
    )

    return_and_replacement = models.OneToOneField(
        "ReturnAndReplacement",
        models.CASCADE,
        null=True,
        blank=True,
        related_name="sales_order",
    )

    # Order number generated by billy.
    generated_order_number = models.CharField(max_length=300, blank=True, null=True)

    invoice_address_value = RelatedObjectOrNone("invoice_address")
    delivery_address_value = RelatedObjectOrNone("delivery_address")

    # NOTE: The main contact email is the email from the Customer.
    invoice_contact_email = own_fields.EmailField(default="", blank=True)
    delivery_contact_email = own_fields.EmailField(default="", blank=True)

    terms_and_conditions = models.ManyToManyField("TermsAndConditions", blank=True)

    # A flag used to track if we have blocked any collars on this order
    collars_blocked_by_pending_payment = models.BooleanField(
        default=False,
        help_text="True if collars on this order has been marked as blocked because of pending payment",
    )

    # Internal flag used to only process collar blocking once, in case the block is removed manually, we don't
    # want to re-enable the block again. So we use this flag to only do the blocking on the order once.
    collar_block_processed = models.BooleanField(
        default=False,
        help_text="True if this has been processed by the collar block",
    )

    hubspot_pipeline = models.ForeignKey(HubSpotPipelineModel, PROTECT, null=True, blank=True)
    hubspot_pipeline_stage = models.ForeignKey(HubSpotPipelineStageModel, PROTECT, null=True, blank=True)
    hubspot_create_date = models.DateField(null=True, blank=True)

    # Only used by web shop orders to keep track of the deal created in hubspot
    hubspot_deal_id = models.CharField(
        max_length=300,
        null=True,
        blank=True,
        help_text="Hubspot deal id. Internal value, only used for web shop orders "
        "to keep track of the deal created in hubspot",
    )

    cancel_reason = models.CharField(max_length=300, null=True, blank=True)
    cancel_explanation = models.CharField(max_length=300, null=True, blank=True)
    status_when_cancelled = models.CharField(max_length=300, null=True, blank=True, choices=SalesOrderStatus.choices)
    collar_in_shipment_active = models.BooleanField(
        default=False,
        null=True,
        blank=True,
        help_text="Set to true if we recorded any activity in shipment, updated when customer access orders view",
    )
    referral_code = models.CharField(max_length=50, null=True, blank=True)
    reward_referral_code = models.CharField(
        max_length=50,
        null=True,
        blank=True,
        help_text=(
            "Set when a customer claimed the reward for a referral."
            "The value is the referral code of the customer referral that "
            " reward was claimed from."
        ),
    )

    impersonator = models.ForeignKey(
        settings.AUTH_USER_MODEL,
        PROTECT,
        null=True,
        blank=True,
        help_text="Set if the order is created by a user impersonating the customer.",
    )
    lines: models.QuerySet["SalesOrderLine"]

    objects = SalesOrderManager()
    tracker = FieldTracker(
        fields=[
            "status",
            "payment_status",
            "hubspot_pipeline_id",
            "hubspot_pipeline_stage_id",
            "hubspot_deal_id",
            "booked_at",
            "paid_at",
            "deleted_at",
            "shipped_at",
        ],
    )

    # When the order enters these statuses the order line skus are mapped to specific skus
    # see generic_skus.py and signals.py
    STATUSES_FOR_SKU_MAPPING = [
        SalesOrderStatus.PENDING_PAYMENT,
        SalesOrderStatus.PENDING_DELIVERY,
        SalesOrderStatus.DELIVERED,
    ]

    # shipment warehouse will only updated when the order has these statuses
    STATUSES_FOR_SHIPMENT_ROUTING = [SalesOrderStatus.PENDING_DELIVERY, SalesOrderStatus.PENDING_PAYMENT]

    class Meta:
        unique_together = ["source", "source_id"]
        constraints = [
            models.UniqueConstraint(
                functions.Lower("generated_order_number"),
                name="so_generated_order_number_ci_unique_idx",
            ),
        ]

    def get_collar_in_shipments_activity(self):
        if self.collar_in_shipment_active:
            return True
        shipments = self.get_related_shipments()
        for shipment in shipments:
            collars = shipment.get_picked_collars()
            for collar in collars:
                if collar.last_recorded_activity and collar.last_recorded_activity >= timezone.now() - timedelta(
                    weeks=1,
                ):
                    self.collar_in_shipment_active = True
                    self.save()
        return self.collar_in_shipment_active

    # Override get_status_display to diferentiate between delivered/shipped
    def get_status_display(self):
        status_map = dict(SalesOrderStatus.choices)
        if self.status == SalesOrderStatus.DELIVERED:
            # if we have active collars linked to sales order return default value
            collars_active = self.get_collar_in_shipments_activity()
            if collars_active:
                return status_map[self.status]
            # display shipped if delivered at is within 3 weeks and no activity
            if not collars_active and self.delivered_at and self.delivered_at >= timezone.now() - timedelta(weeks=3):
                return _("Shipped")
        return status_map[self.status]

    def sum_total(self) -> Money:
        return reduce(operator.add, (line.discounted_sum() for line in self.lines.all()), Money("0", self.currency))

    def vat_total(self):
        return reduce(
            operator.add,
            (line.discounted_vat_sum() for line in self.lines.all()),
            Money("0", self.currency),
        )

    # We do not support multiple products with different TAX rates
    def sum_total_with_vat(self):
        return self.sum_total() + self.vat_total()

    @property
    def total(self):
        return sum(line.discounted_sum() for line in self.lines.all())

    @property
    def total_for_shipment(self):
        return sum(
            line.total_for_shipment()
            for line in self.lines.exclude(
                product__category__in=[
                    Product.ProductCategory.FREIGHT,
                    Product.ProductCategory.SUBSCRIPTION,
                    Product.ProductCategory.BUYABLE_SUBSCRIPTION,
                ],
            )
        )

    @property
    def total_discount_value(self):
        return sum(line.discount_value() for line in self.lines.all())

    @property
    def has_discount(self):
        return self.total_discount_value > Money(0, self.currency)

    @property
    def freight_total(self) -> Money:
        return self.lines.filter(
            product__category=Product.ProductCategory.FREIGHT,
        ).aggregate(Sum("price"))["price__sum"] or Money(0, self.currency)

    def get_total_quantity_by_category(self, category: Product.ProductCategory):
        return sum(line.quantity for line in self.lines.filter(product__category=category))

    def is_zero_amount_order(self):
        return self.sum_total().amount == 0

    def set_accounting_refs(self, system: SalesOrderAccountingSystem, order_id: str, order_ref: str = ""):
        self.accounting_system = system
        self.accounting_order_id = order_id
        self.accounting_order_ref = order_ref

    def get_payment_request_document(self):
        from .invoices import InvoiceDocument

        doc: InvoiceDocument = self.invoice_documents.get(doc_id=self.accounting_payment_request_id)
        return doc

    def get_vat_invoice_document(self):
        from .invoices import InvoiceDocument

        doc: InvoiceDocument = self.invoice_documents.get(doc_id=self.accounting_vat_invoice_id)
        return doc

    def get_tax_calculation(self) -> Optional["TaxCalculation"]:
        try:
            return self.taxcalculation_set.latest("id")
        except TaxCalculation.DoesNotExist:
            pass

    def is_using_card_payment(self) -> bool:
        return self.payment_method == SalesOrder.PaymentMethod.CARD

    def order_reference(self):
        if self.accounting_order_ref:
            return self.accounting_order_ref
        return self.accounting_order_id

    def generate_and_set_suffix_order_number(self, suffix="RR") -> str:
        if self.generated_order_number:
            return self.generated_order_number
        for i in range(20):
            try:
                with transaction.atomic():
                    self.generated_order_number = f"{suffix}{os.urandom(2).hex().upper()}"
                    self.save(update_fields=["generated_order_number", "updated_at"])
                    return self.generated_order_number
            except IntegrityError:
                pass
        raise RuntimeError(f"Failed to generate {suffix} order number. Maybe we have run out of free order numbers?")

    def is_shipment_managed_in_billy(self):
        """Returns true if the shipment of the sales order is managed by billy

        This is true for xero environments and visma pre paid. For the old visma system, the shipments where
        managed by visma, or if the sales order is return and replacement
        """
        # The shipments for orders in the old visma instance (VISMA) was managed by visma and synced to billy
        if self.accounting_system == SalesOrderAccountingSystem.VISMA and not self.return_and_replacement:
            return False
        # Shipments for all other orders are managedby billy now
        return True

    @property
    def is_first_sales_order(self):
        first_sales_order = (
            self.customer.sales_orders.filter(
                status__in=[SalesOrderStatus.PENDING_DELIVERY, SalesOrderStatus.DELIVERED],
            )
            .order_by("created_at")
            .first()
        )
        return first_sales_order == self

    @property
    def order_number(self):
        if self.generated_order_number:
            return self.generated_order_number
        return self.source_id

    @cached_property
    def product_counts(self) -> ProductCounts:
        chargers = 0
        collars = 0
        for line in self.lines.all():
            if products.is_collar_sku(line.sku):
                collars += line.quantity
            elif products.is_charger_sku(line.sku):
                chargers += line.quantity

        return ProductCounts(
            collars=collars,
            chargers=chargers,
            is_rr_order=self.is_rr_order(),
        )

    @property
    def my_order_lines(self) -> List[MyOrderLine]:
        """
        Used in my orders page
        """

        from .returns import CollarReplacement

        lines = []

        order_lines = self.lines.display_ordered.all()

        if self.is_rr_order():
            for replacement in self.return_and_replacement.collars.all():
                replacement: CollarReplacement

                old_collar: Collar = replacement.collar
                lines.append(
                    MyOrderLine(
                        product_type=_("Replacement for collar {serial_no}").format(serial_no=old_collar.serial_no),
                        # Show the warranty end date from the original collar
                        warranty_expiry=old_collar.warranty.end_date if old_collar.warranty else None,
                        contract_expiry="",
                        sku=replacement.replacement_product.sku,
                        quantity=1,
                    ),
                )

            # We add the collar lines from the RR, so remove the collar skus from the order lines
            order_lines = [li for li in order_lines if not products.is_collar_sku(li.sku)]

        for line in order_lines:
            line: SalesOrderLine
            lines.append(
                MyOrderLine(
                    product_type=line.my_orders_description,
                    contract_expiry="12m" if products.is_collar_sku(line.sku) and not self.is_rr_order() else "",
                    sku=line.sku,
                    quantity=line.quantity,
                ),
            )

        return lines

    @cached_property
    def my_order_shipment(self) -> Optional["Shipment"]:
        """
        Used in my orders page
        """
        return self.shipments.first()

    @cached_property
    def my_order_invoice(self):
        """
        Used in my orders page
        """
        if self.status == SalesOrderStatus.DELETED:
            return None
        return self.accounting_payment_request_document

    @cached_property
    def my_order_proposal(self):
        """
        Used in my orders page
        """
        if self.status == SalesOrderStatus.PROPOSAL:
            return self.proposal_document
        return None

    @property
    def my_order_invoice_files(self):
        """
        Used in my orders page
        """
        if not self.my_order_invoice:
            return []
        files = []
        if self.accounting_payment_request_id:
            payment_request = self.accounting_payment_request_document
            if payment_request:
                files.extend(payment_request.files.order_by("id").all())
            vat_invoice = self.accounting_invoice_document
            if vat_invoice:
                files.extend(vat_invoice.files.order_by("id").all())
        else:
            files.extend(self.my_order_invoice.files.order_by("id").all())
        return files

    @property
    def my_order_direct_url(self):
        url = f"{reverse('account:orders-list')}#o{self.order_number}"
        return urljoin(settings.ACCOUNT_BASE_URL, url)

    @property
    def accounting_invoice_document(self):
        if self.accounting_payment_request_id or self.accounting_vat_invoice_id:
            from .invoices import InvoiceDocument

            try:
                return self.get_vat_invoice_document()
            except InvoiceDocument.DoesNotExist:
                return None
        return self.invoice_documents.is_invoice().order_by("-id").first()

    @property
    def accounting_payment_request_document(self):
        if self.accounting_payment_request_id:
            from .invoices import InvoiceDocument

            try:
                return self.get_payment_request_document()
            except InvoiceDocument.DoesNotExist:
                return None
        # For old visma order and xero orders the payment request and the vat invoice is the same
        return self.accounting_invoice_document

    @property
    def proposal_document(self):
        return self.files.filter(sales_order=self, type=SalesOrderFile.Type.PROPOSAL).first()

    @property
    def is_fully_paid(self):
        return self.payment_status == SalesOrder.PaymentStatus.PAID

    @property
    def is_post_paid(self):
        return self.post_payment

    @property
    def is_expired(self):
        if self.order_expiry_date and self.order_expiry_date <= date.today():
            return True
        return False

    def is_cancellable_by_user(self):
        from .invoices import InvoiceDocument

        if self.status != SalesOrderStatus.PENDING_PAYMENT:
            return False
        inv: InvoiceDocument = self.my_order_invoice
        if inv is None and self.nofence_company == NOFENCE_US:
            # For US there may be a delay until the invoice is available, so
            # if the invoice document is missing we show the cancel button if
            # we have the invoice document id.
            return bool(self.accounting_order_id)
        return inv and not inv.is_partially_paid()

    def is_rr_order(self):
        return self.order_type == SalesOrder.Type.RR and self.return_and_replacement is not None

    def __str__(self):
        if self.order_type == SalesOrder.Type.RR:
            return f"{self.order_number}"
        return f"{self.id} - {self.order_number}"

    @property
    def get_invoice_address(self):
        return self.invoice_address_value if self.invoice_address_value else self.customer.invoice_address_value

    @property
    def get_vat_number(self):
        return self.financing_partner if self.invoice_address_value else self.customer.invoice_address_value

    @property
    def get_customer_name(self):
        return (
            self.financing_partner.name
            if (self.financing_partner and self.financing_partner.name)
            else self.customer.name
        )

    @property
    def get_vat_identification_number(self):
        return (
            self.financing_partner.tax_number
            if (self.financing_partner and self.financing_partner.tax_number)
            else self.customer.vat_identification_number
        )

    def get_all_billing_email_values(self):
        return (
            [self.financing_partner.email]
            if (self.financing_partner and self.financing_partner.email)
            else self.customer.get_all_billing_email_values()
        )

    def validate_unique(self, *args, **kwargs):
        super().validate_unique(*args, **kwargs)
        if self.generated_order_number:
            qs = SalesOrder.objects.filter(generated_order_number__iexact=self.generated_order_number)
            if not self._state.adding and self.pk is not None:
                qs = qs.exclude(pk=self.pk)
            if qs.exists():
                raise ValidationError({"generated_order_number": _("Order number already exists")})

    def track_revenue_event(self, should_use_current_time=True):
        if self.payment_status != self.PaymentStatus.PAID or self.order_type != self.Type.SO:
            logger.info(
                f"Not tracking revenue event in Amplitude for sales order: {self.pk}, "
                f"payment status: {self.payment_status} is not PAID or order type is not SO",
            )
            return

        logger.info(f"Tracking revenue event in Amplitude for sales order: {self.pk}")
        try:
            order_lines = self.lines.all().select_related("product")

            events = []
            for line in order_lines:
                # time in milliseconds since unix epoch
                event_time = (
                    datetime.now().timestamp() * 1000
                    if should_use_current_time
                    else (
                        self.delivered_at.timestamp() * 1000
                        if self.delivered_at
                        else (
                            self.created_at.timestamp() * 1000
                            if self.created_at
                            else datetime.now().timestamp() * 1000
                        )
                    )
                )
                insert_id = f"revenue_{self.pk}_{line.pk}_{line.product.pk}_v3"

                event = {
                    "user_id": str(self.customer.user.pk),
                    "time": int(event_time),
                    "event_type": "Revenue event",
                    "insert_id": insert_id,
                    "revenue": float(convert_money(line.discounted_sum(), "NOK").amount),
                    "price": float(convert_money(line.discounted_price(), "NOK").amount),
                    "quantity": int(line.quantity),
                    "productId": str(line.product.pk),
                    "revenueType": self.order_type,
                    "event_properties": {
                        "order_id": str(self.pk),
                        "order_type": self.order_type,
                        "currency": self.currency,
                        "product_name": line.product.name,
                        "product_category": line.product.category if line.product else None,
                        "product_sku": line.product.sku,
                        "total_order_items": len(order_lines),
                        "total_order_revenue": float(
                            sum(convert_money(line.discounted_sum(), "NOK").amount for line in order_lines),
                        ),
                        "is_collar": line.product.is_collar_product(),
                        "collar_product_type": line.product.collar_product_type,
                        "market": self.customer.nofence_company.name,
                        "is_campaign": bool(self.referral_code),
                        "campaign_type": (
                            "REFERRER_CAMPAIGN"
                            if self.referral_code and self.referral_code.startswith("NOFENCE-")
                            else ("GENERIC_CAMPAIGN" if self.referral_code else None)
                        ),
                        "campaign_code": self.referral_code if self.referral_code else None,
                    },
                }
                events.append(event)
            set_once_props = {}
            append_props = {}
            if self.referral_code:
                set_once_props.update(
                    {
                        "is_campaign_customer": True,
                        "campaign_type_customer": (
                            "REFERRER_CAMPAIGN" if self.referral_code.startswith("NOFENCE-") else "GENERIC_CAMPAIGN"
                        ),
                    },
                )
                append_props["campaign_code_customer"] = self.referral_code
                append_props["campaigns_participated_customer"] = set_once_props["campaign_type_customer"]

            identify_event = {
                "insert_id": f"identify_revenue_{self.pk}",
                "user_id": str(self.customer.user.pk),
                "event_type": "$identify",
                "user_properties": {
                    "$setOnce": set_once_props,
                    "$preInsert": append_props,
                },
            }
            events.append(identify_event)
            from core.default_api_clients import create_amplitude_client

            response = create_amplitude_client().track_events(events)
            if response:
                event_ids = ", ".join([e["insert_id"] for e in events])
                logger.info(
                    f"Successfully tracked events in Amplitude, for events: {event_ids}",
                    extra={"order_id": self.pk},
                )
        except Exception as e:
            # Log error but don't block the save
            logger.exception(
                f"Failed to track revenue events in Amplitude for sales order: {self.pk}",
                exc_info=e,
            )

    def save(self, update_fields=None, *args, **kwargs):
        if not self.inco_terms:
            if self.customer.country in ("US", "CA"):  # Requested by Harald
                self.inco_terms = IncoTerms.DDP
            elif self.customer.country in ("NO",):
                self.inco_terms = IncoTerms.EXW
            else:
                self.inco_terms = IncoTerms.DAP
        if getattr(self, "nofence_company", None) is None:
            self.nofence_company = self.customer.nofence_company

        if (
            self.tracker.has_changed("status")
            and self.status == SalesOrderStatus.DELIVERED
            and self.delivered_at is None
        ):
            self.delivered_at = timezone.now()
            if update_fields:
                update_fields = set(update_fields)
                update_fields.add("delivered_at")
                update_fields.add("status")
                update_fields.add("updated_at")

        if self.pk is None:
            self.pre_paid = self.nofence_company.pre_paid
        if self._state.adding:
            self.impersonator = get_current_impersonator()

        # Track revenue event if payment_status changes to PAID
        if self.tracker.has_changed("payment_status") and self.payment_status == self.PaymentStatus.PAID:
            self.track_revenue_event()

        # Set date fields when statuses change for the first time
        if self.tracker.has_changed("payment_status"):
            if self.payment_status == self.PaymentStatus.PENDING_PAYMENT and self.booked_at is None:
                self.booked_at = timezone.now()
                if update_fields:
                    update_fields = set(update_fields)
                    update_fields.add("booked_at")
            elif self.payment_status == self.PaymentStatus.PAID and self.paid_at is None:
                self.paid_at = timezone.now()
                if update_fields:
                    update_fields = set(update_fields)
                    update_fields.add("paid_at")

        if self.tracker.has_changed("status"):
            if self.status == SalesOrderStatus.DELETED and self.deleted_at is None:
                self.deleted_at = timezone.now()
                if update_fields:
                    update_fields = set(update_fields)
                    update_fields.add("deleted_at")
            elif self.status == SalesOrderStatus.PENDING_DELIVERY and self.shipped_at is None:
                self.shipped_at = timezone.now()
                if update_fields:
                    update_fields = set(update_fields)
                    update_fields.add("shipped_at")

        return super().save(update_fields=update_fields, *args, **kwargs)

    def add_terms_and_conditions(self):
        latest_general_terms_and_conditions = (
            TermsAndConditions.objects.filter(
                nofence_company=self.nofence_company,
                type=TermsAndConditions.Type.TERMS_AND_CONDITIONS,
            )
            .order_by("-created_at")
            .first()
        )
        if latest_general_terms_and_conditions:
            self.terms_and_conditions.add(latest_general_terms_and_conditions)
        latest_warranty_terms_and_conditions = (
            TermsAndConditions.objects.filter(
                nofence_company=self.nofence_company,
                type=TermsAndConditions.Type.WARRANTY,
            )
            .order_by("-created_at")
            .first()
        )
        if latest_warranty_terms_and_conditions:
            self.terms_and_conditions.add(latest_warranty_terms_and_conditions)
        self.save()

    def recalculate_order_line_display_order(self):
        """Update the order's line items with display order calculated from the skus"""

        lines = sorted(self.lines.all(), key=lambda line: products.product_sku_ordering_key(line.sku))

        for order_index, line in enumerate(lines):
            line.display_order = order_index
            line.save()

    def calculate_payment_status(self) -> PaymentStatus:
        if self.payment_method == SalesOrder.PaymentMethod.MANUALLY_PAID:
            return SalesOrder.PaymentStatus.PAID

        if self.is_post_paid and self.accounting_payment_request_id:
            # For post paid orders we set the order to paid only if the payment request has been paid.
            from .invoices import InvoiceDocument

            try:
                if self.accounting_vat_invoice_id:
                    vat_invoice = self.get_vat_invoice_document()
                    if vat_invoice.is_paid():
                        return SalesOrder.PaymentStatus.PAID
            except InvoiceDocument.DoesNotExist:
                pass
            return SalesOrder.PaymentStatus.PENDING_PAYMENT

        is_paid = [inv.is_paid() for inv in self.invoice_documents.is_invoice().all()]
        if len(is_paid) == 0:
            status = SalesOrder.PaymentStatus.MISSING_INVOICE
        elif all(is_paid):
            status = SalesOrder.PaymentStatus.PAID
        else:
            status = SalesOrder.PaymentStatus.PENDING_PAYMENT

        # If the invoice has not been paid or is missing we check if a card payment has been made
        if status != SalesOrder.PaymentStatus.PAID:
            try:
                from .cardpayments import CardPayment

                cp = CardPayment.objects.get(sales_order=self)
                if cp.is_successful():
                    return SalesOrder.PaymentStatus.PAID
                return SalesOrder.PaymentStatus.PENDING_PAYMENT
            except CardPayment.DoesNotExist:
                pass

        return status

    def update_payment_status(self):
        payment_status = self.calculate_payment_status()
        if payment_status != self.payment_status:
            self.payment_status = payment_status
            self._change_reason = "Update payment status"
            self.save(update_fields=["payment_status", "updated_at"])

    def update_order_expiry_date(self, new_order_expiry_date: date):
        self.order_expiry_date = new_order_expiry_date
        self.save(update_fields=["order_expiry_date", "updated_at"])

    @property
    def last_delivered_shipment(self) -> Optional[Shipment]:
        return self.shipments.filter(status__in=Shipment.DELIVERED_STATUSES).order_by("-id").first()

    def get_related_shipments(self) -> List[Shipment]:
        return list(self.shipments.order_by("id").all())

    def has_linked_invoice_documents(self) -> bool:
        return self.invoice_documents.exists()

    def has_shipped_collars(self) -> bool:
        """Returns true if any linked delivered shipments has picked collars"""
        return self.shipments.filter(
            status__in=Shipment.DELIVERED_STATUSES,
            picked_units__collar__isnull=False,
        ).exists()

    def get_subscription_changes(self) -> List[SubscriptionChange]:
        return list(self.subscriptionchanges.all())

    def delete_subscription_changes(self, reason: str = None):
        for sub_change in self.get_subscription_changes():
            if reason is not None:
                sub_change._change_reason = reason
            sub_change.delete()

    def is_web_shop_order(self) -> bool:
        """Should return True for orders created through the web shop."""
        return self.source == SalesOrderSource.WEBSHOP

    def is_hubspot_order(self) -> bool:
        return self.source == SalesOrderSource.HUBSPOT

    def get_payment_request(self):
        from core.default_services import create_sales_order_service

        return create_sales_order_service().get_payment_request(self)


class SalesOrderLineManager(models.Manager):
    @property
    def display_ordered(self):
        return self.order_by("display_order", "id")

    def get_queryset(self) -> QuerySet:
        return (
            super()
            .get_queryset()
            .exclude(
                status=SalesOrderLineStatus.DELETED,
            )
        )


class SalesOrderLine(BaseHistoryModel):
    """
    SKUs: The sku field represents the specifc sku for the order line.
          A specific sku can be e.g a battery charger sku with a plug
          type used in a specific country.

          Example of generic and specific SKUs:
          C2-PS1-BD2-charger (generic) => C2-PS1-BD2-charger-type-A (specific)

          The sku is only set to a specific sku after the related order
          reaches the PENDING_DELIVERY stage or later. Before this status
          the sku field may be a generic sku. Once the correct status is
          reached, signal listeners maps the sku to specific skus, if necessary (see signals.py).
    """

    order = models.ForeignKey(SalesOrder, CASCADE, related_name="lines")
    name = models.CharField(max_length=300)
    description = models.CharField(max_length=300, default="", blank=True)
    sku = models.CharField(max_length=300)
    product = models.ForeignKey(Product, PROTECT, null=True, related_name="sales_order_line")
    status = models.CharField(max_length=10, choices=SalesOrderLineStatus.choices, default=SalesOrderLineStatus.ACTIVE)
    # Used to save the original sku when mapping skus to specific skus:
    generic_sku = models.CharField(max_length=300, default="", blank=True)
    price = MoneyField(max_digits=14, decimal_places=2)
    quantity = models.DecimalField(max_digits=14, decimal_places=2)
    discount = models.DecimalField(max_digits=14, decimal_places=2)
    discount_percentage = models.DecimalField(max_digits=14, decimal_places=2)

    source_id = models.CharField(max_length=300)

    # Can be used to control the order of the lines when displayed
    display_order = models.IntegerField(default=0)

    # True if inventory has been reserved for the sales order line
    inventory_reserved = models.BooleanField(default=False)

    # Only used by web shop orders to keep track of the deal created in hubspot
    hubspot_line_item_id = models.CharField(
        max_length=300,
        null=True,
        blank=True,
        help_text="Hubspot line item id. Internal value, only "
        "used for web shop orders to keep track of the line "
        "items created in hubspot",
    )

    objects = SalesOrderLineManager()
    objects_with_deleted = models.Manager()

    def discount_value(self):
        return self.price * self.quantity * (self.discount_percentage / 100)

    def total_for_shipment(self):
        return self.quantity * self.price

    def discounted_price(self):
        return (self.price - Money(self.discount, self.price.currency)) * (100 - self.discount_percentage) / 100

    def discounted_sum(self):
        return (
            self.quantity
            * (self.price - Money(self.discount, self.price.currency))
            * (100 - self.discount_percentage)
            / 100
        )

    def discounted_vat_sum(self):
        tax_rate = self.product.get_vat_rate_for_customer(self.order.customer)
        return (
            self.quantity
            * (self.price - Money(self.discount, self.price.currency))
            * (100 - self.discount_percentage)
            / 100
            * (tax_rate / 100)
        )

    def vat(self):
        tax_rate = self.product.get_vat_rate_for_customer(self.order.customer)
        return self.price * (tax_rate / 100)

    def vat_sum(self):
        tax_rate = self.product.get_vat_rate_for_customer(self.order.customer)
        return self.quantity * (self.price - Money(self.discount, self.price.currency)) * (tax_rate / 100)

    def tax_rate(self):
        return self.product.get_vat_rate_for_customer(self.order.customer)

    def tax_message(self):
        return self.product.get_tax_message_for_customer(self.order.customer)

    def discount_percentage_or_calculated(self) -> Decimal:
        """Returns the discount percentage on the line

        This will always return a discount percentage either the lines discount_percentage or
        a calculated percentage from the discount value. This is useful if the discount value
        must be specified as a percentage, e.g. in the Hapro order.

        It uses the same rules as the original visma integration:
        1. Try the discount value if available and calculate the percentage
        2. Fallback to the specified discount_percentage
        """
        if self.discount and self.price.amount > 0:
            return 100 * (self.discount / self.price.amount)
        return self.discount_percentage

    def save(self, *args, **kwargs):
        if not self.generic_sku:
            self.generic_sku = self.sku
        return super().save(*args, **kwargs)

    @property
    def my_orders_description(self):
        if not self.order.is_rr_order():
            product = products.find_product_type_or_none(self.sku)
            if product is not None:
                return product
        return self.name or self.description or self.sku

    class Meta:
        constraints = [
            models.UniqueConstraint(
                fields=["order_id", "source_id"],
                condition=~Q(status=SalesOrderLineStatus.DELETED),
                name="unq_line_so_and_source_id",
            ),
        ]


def get_sales_order_file_storage():
    return storages["s3_sales_orders"]


class SalesOrderFile(BaseModel):
    class Type(models.TextChoices):
        PROPOSAL = "PROPOSAL"

    file = models.FileField(storage=get_sales_order_file_storage, blank=False, null=False)
    sales_order = models.ForeignKey(SalesOrder, models.CASCADE, null=False, blank=False, related_name="files")
    type = models.CharField(max_length=50, choices=Type.choices, null=False, blank=False)

    def __str__(self):
        return f"{self.file.name}"

    class Meta:
        constraints = [
            models.UniqueConstraint(fields=("sales_order", "type"), name="salse_order_file_unique_idx"),
        ]


class KeyValue(BaseModel):
    key = models.CharField(max_length=200, primary_key=True)
    value = models.JSONField()

    class Meta:
        verbose_name = "KeyValue"
        verbose_name_plural = "KeyValue Entries"


class DBObjectLock(models.Model):
    """Used to implement locking using the database.

    This is done by inserting an object that has an unique object id and type
    into this table in a transaction and then deleting the object when done.

    This way only a single transaction will succeed with the insert of the
    same id and type combination and any other transactions will wait.

    Inserting different id and type combinations should work fine in parallel.

    The downside is that the transactions can not detect the lock early if the
    lock is already held and will block until the other transaction is finished,
    but I think it should be fine.
    """

    object_id = models.CharField(null=False, max_length=300)
    object_type = models.CharField(null=False, max_length=300)

    class Meta:
        unique_together = ("object_id", "object_type")


def _get_current_authenticated_user():
    """Returns the current authenticated user, if available

    We are reusing the simple history thread local context to get the
    current user. See simple_history.middleware.HistoryRequestMiddleware.
    """
    try:
        context = HistoricalRecords.context
        if context.request.user.is_authenticated:
            return context.request.user
    except AttributeError:
        pass


def get_current_impersonator():
    try:
        context = HistoricalRecords.context
        if context.request.user.is_impersonate:
            return HistoricalRecords.context.request.user.impersonator
    except AttributeError:
        pass


class CustomerEmailQueue(BaseModel):
    """Queue and Log of emails sent to customers"""

    class Status(models.TextChoices):
        QUEUED = "QUEUED"
        SENT = "SENT"
        FAILED = "FAILED"

    class Type(models.TextChoices):
        FIRST_PAYMENT_REMINDER = "FIRST_PAYMENT_REMINDER"
        SECOND_PAYMENT_REMINDER = "SECOND_PAYMENT_REMINDER"

    customer = models.ForeignKey(Customer, CASCADE)
    status = models.CharField(max_length=20, choices=Status.choices)
    type = models.CharField(max_length=50, choices=Type.choices)

    # An value used to prevent sending duplicate emails to the same customer.
    # Currently used in the payment reminder to only send a single email to a
    # customer if a customer have multiple unpaid unvoices in the same payment
    # reminder run. Null can be used to disable the check.
    batch_id = models.CharField(max_length=100, null=True)

    class Meta:
        unique_together = ("customer_id", "type", "batch_id")

    def __str__(self):
        return f"{self.id} - {self.type}"

    def from_email(self):
        return "Nofence AS <<EMAIL>>"

    def template_id(self) -> str:
        templates = settings.SENDGRID_TEMPLATES.get(self.type)

        if templates is None:
            raise RuntimeError(f"Send grid templates for {self.type} missing in settings")

        country = "nb" if self.customer.country == NOFENCE_NO else "en"
        template_id = templates.get(country)

        if template_id is None:
            raise RuntimeError(f"Send grid template for language {country} missing for {self.type} in settings")

        return template_id

    def reply_to(self) -> Optional[str]:
        """Only used for send grid emails

        Return an appropriate reply to address based on the email type.
        """
        pass

    def categories(self) -> List[str]:
        """Only used for send grid emails

        Return appropriate categories based on the email type.
        """
        return []

    def is_hubspot_email(self) -> bool:
        return self._is_payment_reminder_email()

    def hubspot_email_id(self) -> bool:
        emails = settings.HUBSPOT_EMAILS.get(self.type)

        if emails is None:
            raise RuntimeError(f"Hubspot email ids for {self.type} missing in settings")

        language = self.customer.get_preferred_language_code()
        email_id = emails.get(language)

        if email_id is None:
            raise RuntimeError(f"Hubspot email id for language {language} missing for {self.type} in settings")

        return email_id

    def _is_payment_reminder_email(self):
        return self.type in [
            CustomerEmailQueue.Type.FIRST_PAYMENT_REMINDER,
            CustomerEmailQueue.Type.SECOND_PAYMENT_REMINDER,
        ]


class PurchaseBlockExcludeQuerySet(models.QuerySet):
    def update_hit_count_if_excluded(self, object_type, object_id) -> bool:
        """Checks if a object is excluded and updates the hit count if so"""
        return (
            self.filter(enabled=True, object_type=object_type, object_id=object_id).update(
                hit_count=F("hit_count") + 1,
            )
            > 0
        )

    def is_all_invoices_excluded(self, invoice_docs: list) -> bool:
        excluded_check = []
        for inv in invoice_docs:
            excluded = PurchaseBlockExclude.objects.update_hit_count_if_excluded(
                object_type=PurchaseBlockExclude.object_type_for_invoice(inv),
                object_id=inv.doc_id,
            )
            excluded_check.append(excluded)
            if excluded:
                logger.info(f"Purchase block check excluded for invoice {inv}")
        return all(excluded_check)


class PurchaseBlockExclude(BaseModel):
    """
    This is used to exclude deals/invoices from the purchase block.

    It also functions as a log so we can monitor that it is not abused.

    It should be a little bit cumbersome to add an exclude so that it not
    too easy for people to ignore the block.
    """

    class Type(models.TextChoices):
        HUBSPOT_DEAL = "HUBSPOT_DEAL", _("Hubspot Deal")
        VISMA_INVOICE = "VISMA_INVOICE", _("Visma Invoice")
        VISMA_PREPAID_INVOICE = "VISMA_PREPAID_INVOICE", _("Visma Prepaid Invoice")
        XERO_INVOICE = "XERO_INVOICE", _("Xero Invoice")

    added_by = models.ForeignKey(User, PROTECT)

    object_type = models.CharField(max_length=50, choices=Type.choices)

    object_id = models.CharField(max_length=50, help_text=_("Hubspot deal id or Visma invoice id or Xero invoice id"))

    enabled = models.BooleanField(default=True)

    # How may times the block exclude has been used by the validator
    hit_count = models.IntegerField(default=0)

    reason = models.TextField(
        help_text=_("Reason why the object should be excluded from the purchase block"),
    )

    objects: PurchaseBlockExcludeQuerySet = models.Manager.from_queryset(PurchaseBlockExcludeQuerySet)()

    class Meta:
        unique_together = ("object_type", "object_id")

    def __str__(self):
        label = PurchaseBlockExclude.Type[self.object_type].label
        return f"{label} - {self.object_id}"

    def save(self, *args, **kwargs):
        if not hasattr(self, "added_by"):
            self.added_by = _get_current_authenticated_user()
        return super().save(*args, **kwargs)

    @classmethod
    def object_type_for_invoice(cls, inv):
        if inv.provider == Invoice.Provider.VISMA:
            return cls.Type.VISMA_INVOICE
        elif inv.provider == Invoice.Provider.VISMA_PREPAID:
            return cls.Type.VISMA_PREPAID_INVOICE
        elif inv.provider == Invoice.Provider.XERO:
            return cls.Type.XERO_INVOICE
        raise RuntimeError(f"Unknown invoice provider {inv.provider}")


class OptimisticLockError(RuntimeError):
    pass


class DataExport(BaseHistoryModel):
    """Used for logging data export triggered from billy.

    May be usefull if we want to let the customer download old
    exports.
    """

    class ExportReason(models.TextChoices):
        RESEARCH = "RESEARCH", _("Research")
        GRANTS = "GRANTS", _("Grants")
        CONSERVATION_NEEDS = "CONSERVATION_NEEDS", _("Conservation Needs")

    customer = models.ForeignKey(Customer, CASCADE, related_name="data_exports")
    external_id = models.CharField(
        max_length=300,
        null=True,
        blank=True,
        help_text="Windmill uuid that can be used to lookup the result",
    )
    export_reason = models.CharField(
        max_length=300,
        null=True,
        blank=True,
        choices=ExportReason.choices,
    )

    terms_and_conditions = models.ManyToManyField("TermsAndConditions")

    start_date = models.DateField(null=True, blank=True)
    end_date = models.DateField(null=True, blank=True)
    metadata = models.JSONField(null=True, default=dict)

    # No longer used
    name = deprecate_field(models.CharField(max_length=500, unique=True))
    execution_arn = deprecate_field(models.CharField(max_length=500))

    def __str__(self):
        return f"{self.external_id}"


class XeroAuthConnection(BaseModel):
    tenant_id = models.CharField(max_length=300, unique=True)
    tenant_type = models.CharField(max_length=300)
    tenant_name = models.CharField(max_length=300)

    nofence_company = models.OneToOneField(
        NofenceCompany,
        PROTECT,
        blank=True,
        null=True,
        unique=True,
        related_name="xero_auth",
        help_text=_(
            "When connecting to Xero the tenant ID to use for a company is looked up using this link.",
        ),
    )

    connection_id = models.CharField(max_length=300)
    connection_created_at = models.DateTimeField()
    connection_updated_at = models.DateTimeField()

    def __str__(self):
        return f"{self.tenant_name}"


class XeroAuthToken(BaseModel):
    key = models.CharField(max_length=300)

    token = models.JSONField()

    expires_at = models.DateTimeField(null=True)

    def __str__(self):
        return f"{self.key}"


class MassEmail(BaseModel):
    name = models.CharField(max_length=300, help_text=_("Internal name to identify the the email"))

    template_id = models.CharField(max_length=300, help_text=_("SendGrid template ID"))

    from_email = own_fields.EmailField(max_length=300, help_text=_("The address to use as the sender"))

    data = models.JSONField(default=dict)

    def __str__(self):
        return f"{self.name}"

    @classmethod
    def available_fields(cls):
        return [name for name in dir(MassEmailCommonFields) if not name.startswith("_")]

    @classmethod
    def available_to_email_fields(cls):
        return ["email", "invoice_contact_email", "delivery_contact_email"]

    @property
    def common_fields(self):
        return self.data.get("common_fields", [])

    @common_fields.setter
    def common_fields(self, value):
        self.data["common_fields"] = value or []

    @property
    def to_email_fields(self):
        return self.data.get("to_email_fields", [])

    @to_email_fields.setter
    def to_email_fields(self, value):
        self.data["to_email_fields"] = value or []


class MassEmailCommonFields:
    """Variables that can be selected when creating mass emails in admin and will
    be sent to the email provider when sending emails"""

    def __init__(self, customer: Customer):
        self.customer = customer

    @property
    def customer_name(self) -> str:
        return self.customer.name

    @property
    def collar_count(self) -> int:
        return self.customer.get_billable_owned_collar_count()


class MassEmailRecord(BaseModel):
    class Status(models.TextChoices):
        QUEUED = "QUEUED"
        SENT = "SENT"
        FAILED = "FAILED"

    mass_email = models.ForeignKey(MassEmail, CASCADE)
    customer = models.ForeignKey(Customer, CASCADE)

    status = models.CharField(max_length=300)

    custom_fields = models.JSONField(default=dict)

    def __str__(self):
        return f"{self.customer} - {self.status}"

    @property
    def to_email(self):
        emails = []
        for prop in self.mass_email.to_email_fields:
            value = getattr(self.customer, prop)
            if value:
                emails.append(value)
        if not emails:
            emails.append(self.customer.email)
        return emails

    @property
    def from_email(self):
        return self.mass_email.from_email

    @property
    def template_id(self):
        return self.mass_email.template_id

    @property
    def common_fields(self):
        return self.mass_email.common_fields

    def get_common_fields(self) -> MassEmailCommonFields:
        return MassEmailCommonFields(self.customer)


class CollarTag(BaseModel):
    name = models.CharField(max_length=50, help_text=_("Name to identify the tag"))
    description = models.TextField(max_length=1000, null=True, blank=True)
    created_by = models.ForeignKey(User, SET_NULL, null=True)
    block_shipping = models.BooleanField(
        default=False,
        help_text="Collars with this tag will not go through the pick and pack app",
    )
    block_shipping_msg = models.TextField(max_length=200, null=True, blank=True)

    def __str__(self):
        return f"{self.name}"


class EmailRecordManager(models.QuerySet):
    def create_unique_record(
        self,
        to_email: str,
        customer: Customer,
        email_type: EmailType,
        object_id: str,
        data: Dict = None,
    ) -> Tuple["EmailRecord", bool]:
        return self.get_or_create(
            to_email=to_email,
            email_type=email_type,
            object_id=object_id,
            defaults={
                "customer": customer,
                "data": data if data is not None else {},
            },
        )

    def create_unique_record_for_customer(
        self,
        customer: Customer,
        email_type: EmailType,
        object_id: str,
        default_to_email: str,
        data: Dict = None,
    ) -> Tuple["EmailRecord", bool]:
        """Similar to create_unique_record, but the to_email is not considered in the unique lookup.

        So only a single record will be created for the customer, email_type, object_id combination,
        but changes to the to_email will be ignored. So if the to_email changes, a new email will
        not be sent.
        """

        return self.get_or_create(
            email_type=email_type,
            object_id=object_id,
            defaults={
                "customer": customer,
                "data": data if data is not None else {},
                "to_email": default_to_email,
            },
        )

    def create_record(
        self,
        to_email: str,
        email_type: EmailType,
        customer: Customer = None,
        object_id: str = None,
        data: Dict = None,
    ) -> "EmailRecord":
        return self.create(
            to_email=to_email,
            email_type=email_type,
            customer=customer,
            object_id=object_id,
            data=data if data is not None else {},
        )


def validate_is_dict(value):
    if not isinstance(value, dict):
        raise ValidationError(
            _("%(value)s must be a dict"),
            params={"value": value},
        )


class EmailRecord(BaseHistoryModel):
    """
    Tracking of all the emails that we send.

    We should try to eventually convert all emails over to this model
    """

    class Status(models.TextChoices):
        QUEUED = "QUEUED"
        SENT = "SENT"
        FAILED = "FAILED"

    to_email = own_fields.EmailField()
    email_type = models.CharField(max_length=300, choices=EmailType.choices)
    customer = models.ForeignKey(Customer, CASCADE, null=True, blank=True)
    status = models.CharField(max_length=300, choices=Status.choices, default=Status.QUEUED)

    # Any extra information that may be needed in specific emails.
    data = models.JSONField(default=dict, blank=True, validators=[validate_is_dict])

    # In some cases we only want to send a single email for a certain object, e.g usage invoice, sales order etc,
    # by sepecifying an id that identifies the object we can use this to check if the email recoed already
    # exists when creating new emails
    object_id = models.CharField(
        max_length=300,
        null=True,
        blank=True,
        help_text=_(
            "In some cases we only want to send a single email for a certain object. If specified, this object id is"
            " is used to make sure we only create a single email record for the the same object.",
        ),
    )

    objects: EmailRecordManager = models.Manager.from_queryset(EmailRecordManager)()

    class Meta:
        constraints = [
            models.UniqueConstraint(
                fields=["to_email", "email_type", "customer", "object_id"],
                name="unq_email_record_customer_object_id",
            ),
            models.UniqueConstraint(
                fields=["to_email", "email_type", "object_id"],
                condition=Q(customer__isnull=True),
                name="unq_email_record_to_email_object_id",
            ),
        ]
        indexes = [
            models.Index(
                fields=["email_type", "object_id", "customer"],
                name="email_rec_type_oid_customer_ix",
            ),
        ]

    def __str__(self):
        return f"{self.get_email_type_display()} - {self.customer} - {self.get_status_display()}"

    def get_to_email(self) -> List[str]:
        return [self.to_email]

    def get_from_email(self) -> str:
        from_email = self.data.get("from_email", None)
        if from_email:
            return from_email
        elif self.customer is not None:
            try:
                return self.customer.nofence_company.get_email_config(self.email_type).from_email_formatted
            except EmailConfig.DoesNotExist:
                logger.info(
                    f"EmailConfig for {self.email_type} don't exists. Defaulting from_<NAME_EMAIL>",
                )
            return f"{self.customer.nofence_company.name} <<EMAIL>>"
        return "<EMAIL>"

    def create_attachment(self, content: File, filename: str = None, mimetype: str = None) -> "EmailRecordAttachment":
        return self.attachments.create(
            attachment_file=content,
            filename=filename,
            mimetype=mimetype,
        )

    def get_attachments(self) -> List["EmailRecordAttachment"]:
        return self.attachments.order_by("id").all()


def get_email_record_attachment_file_storage():
    return storages["s3_email_record_attachments"]


class EmailRecordAttachment(BaseHistoryModel):
    email_record = models.ForeignKey(EmailRecord, CASCADE, related_name="attachments")

    filename = models.CharField(max_length=300)
    attachment_file = models.FileField(storage=get_email_record_attachment_file_storage, blank=False, null=False)
    mimetype = models.CharField(max_length=300, null=True, blank=True)

    def get_attachment_bytes(self) -> bytes:
        with self.attachment_file.open("rb") as f:
            return f.read()


class EmailRecordEvent(BaseModel):
    """Model used to record sendgrid events related to the email records"""

    email_record = models.ForeignKey(EmailRecord, CASCADE, related_name="events")

    event_type = models.CharField(max_length=300)
    event_id = models.CharField(max_length=300)
    event_time = models.DateTimeField()

    payload = models.JSONField(default=dict)

    class Meta:
        unique_together = [
            "email_record",
            "event_id",
        ]

    def __str__(self):
        return f"{self.event_type} - {self.event_time} - {self.email_record_id}"


class TermsAndConditions(BaseHistoryModelForModelTranslations):
    class Type(models.TextChoices):
        TERMS_AND_CONDITIONS = "TERMS_AND_CONDITIONS"
        WARRANTY = "WARRANTY"
        PRIVACY_POLICY = "PRIVACY_POLICY"

    name = models.CharField(max_length=100, blank=False, null=False)
    link = models.CharField(max_length=300)
    type = models.CharField(
        max_length=50,
        choices=Type.choices,
        blank=False,
        null=False,
        default=Type.TERMS_AND_CONDITIONS,
    )
    nofence_company = models.ForeignKey(NofenceCompany, CASCADE, blank=False, null=False)

    def __str__(self):
        return f"{self.name}"


class MyNofencePasswordReset(BaseModel):
    """
    A hack to make the pasword reset work for customers with mismatching emails in billy and my.nofence.


    If a matching email is not found in billy when a user requests a new password, but a match is found
    in my.nofence, we send the reset email to the my.nofence email.

    To keep track of this and reset the my.nofence password when the user clicks on the reset link in the
    email, we keep track of the token in the email and links this to the email that the user typed into
    the password reset form.

    As part of the password reset process, we clean up any old rows in this model.

    """

    token = models.CharField(max_length=300)
    email = own_fields.EmailField()
    user = models.ForeignKey(User, CASCADE)

    class Meta:
        unique_together = [
            "token",
            "user",
        ]

    def __str__(self):
        return f"{self.user} - {self.email}"


class PurchaseOrderNumber(BaseModel):
    purchase_order_number = models.CharField(max_length=50, null=False, blank=False)
    start_date = models.DateField(null=False, blank=False)
    end_date = models.DateField(null=False, blank=False)
    customer = models.OneToOneField(
        Customer,
        models.CASCADE,
        null=False,
        blank=False,
        related_name="purchase_order_number",
    )

    def __str__(self):
        return f"{self.purchase_order_number}"

    def clean(self):
        # Do not allow overlapping date ranges
        if self.start_date >= self.end_date:
            raise ValidationError({"end_date": _("End date must be after start date")})
        super(PurchaseOrderNumber, self).clean()


class NofenceLearningResource(BaseModel):
    title = models.CharField(max_length=300, null=False, blank=False)
    description = models.TextField(null=True, blank=True)
    thumbnail_url = models.URLField(null=True, blank=True)
    video_url = models.URLField(null=True, blank=True, validators=[validate_youtube_url])
    order = models.IntegerField(null=True, blank=True)

    def __str__(self):
        return f"{self.title}"

    def save(self):
        if self.video_url:
            self.thumbnail_url = get_youtube_thumbnail_url(self.video_url)
        super(NofenceLearningResource, self).save()


class GoodsReceipt(BaseHistoryModel):
    """Track receiving of goods.

    We use this to keep track of goods coming in to the warehouses and to track
    deviations.
    """

    class Status(models.TextChoices):
        DRAFT = "DRAFT"
        CONFIRMED = "CONFIRMED"

    class Type(models.TextChoices):
        INTERNAL_SHIPMENT = "INTERNAL_SHIPMENT"

    warehouse = models.ForeignKey(Warehouse, PROTECT, related_name="goods_receipts")
    status = models.CharField(max_length=300, choices=Status.choices)

    # Initially it was intended to try to share some of this code with the purchase receipts,
    # for now this did not happen, to this is really not in use
    type = models.CharField(max_length=300, choices=Type.choices, default=Type.INTERNAL_SHIPMENT)

    shipment = models.OneToOneField(Shipment, PROTECT, null=True, blank=False)

    def __str__(self):
        return f"{self.get_type_display()} - {self.warehouse} - {self.get_status_display()}"

    def is_confirmed(self):
        return self.status == GoodsReceipt.Status.CONFIRMED


class GoodsReceiptLine(BaseHistoryModel):
    goods_receipt = models.ForeignKey(GoodsReceipt, CASCADE, related_name="lines")

    sku = models.CharField(max_length=300)
    product = models.ForeignKey(Product, PROTECT, null=True, blank=True)

    expected_quantity = models.IntegerField()
    received_quantity = models.IntegerField()

    def __str__(self):
        return f"{self.sku} - {self.expected_quantity}"

    def get_deviation(self) -> int:
        return self.received_quantity - self.expected_quantity

    def inventory_exists(self):
        """Used to show a warning in the confirm page in the admin."""
        from .inventory import Inventory

        return Inventory.objects.filter(
            product__sku=self.sku,
            location=self.goods_receipt.warehouse.primary_location,
        ).exists()

    def get_unit_cost(self) -> Optional[Money]:
        shipment = self.goods_receipt.shipment
        if shipment is not None and shipment.is_intercompany_shipment():
            from core.models import InvoiceDocument

            inv: InvoiceDocument = shipment.invoice_document
            for line in inv.data.lines:
                if line.item == self.sku and line.quantity == self.expected_quantity:
                    logger.info(
                        f"GoodsReceiptLine: Found unit cost {line.price} on {inv} matching "
                        f"{self.sku=} and {self.expected_quantity=} on {shipment=}",
                    )
                    return line.price
        logger.info(
            "GoodsReceiptLine: Found no unit cost on invoice matching "
            f"{self.sku=} and {self.expected_quantity=} on {shipment=}",
        )


def get_region_choices():
    return list(US_REGIONS)


class FreightMatrix(BaseHistoryModel):
    collar_type = models.CharField(max_length=300, choices=PRODUCT_TYPE)
    quantity = models.PositiveIntegerField()
    country = CountryField(multiple=False, countries=AllCountries)
    region = models.CharField(
        max_length=300,
        null=True,
        blank=True,
        choices=get_region_choices(),
        help_text="Only in use in the US",
    )

    product = models.ForeignKey(Product, CASCADE, limit_choices_to={"category": Product.ProductCategory.FREIGHT})

    class Meta:
        constraints = [
            models.UniqueConstraint(
                fields=["country", "collar_type", "quantity", "region"],
                name="unq_freight_matrix_lookup_key",
            ),
        ]
        verbose_name_plural = "Freight Matrix"

    def __str__(self):
        return f"{self.get_collar_type_display()} - {self.quantity} - {self.country}"


class FreightWeightThreshold(BaseHistoryModel):
    """Weight thresholds used for calculating freight by weight

    The thresholds defined here are the lower weight limit that this freight product
    applies from. The freight applies from the specified weight and until the next
    threshold defined above.
    """

    country = CountryField(multiple=False, countries=AllCountries)
    region = models.CharField(
        max_length=300,
        null=True,
        blank=True,
        choices=get_region_choices(),
        help_text="Only in use in the US",
    )

    from_weight = models.PositiveIntegerField(
        help_text="Lower weight threshold in grams. This freight applies from this weight up until the "
        "next defined threshold.",
    )

    product = models.ForeignKey(Product, CASCADE, limit_choices_to={"category": Product.ProductCategory.FREIGHT})

    internal_cost = MoneyField(max_digits=14, decimal_places=2, null=True, blank=True)

    class Meta:
        constraints = [
            models.UniqueConstraint(
                fields=["country", "from_weight", "region"],
                name="unq_freight_weight_threshold",
            ),
        ]

    def __str__(self):
        return f"{self.country} - {self.from_weight}g"


class CollarBOMBlock(BaseHistoryModel):
    """collars matching rows will be blocked from the pickandpack app"""

    ems_provider = models.IntegerField(choices=EMS_PROVIDERS)
    product = models.CharField(max_length=2, choices=PRODUCT_TYPE, default=CATTLE)
    product_record = models.IntegerField()
    mec_revision = models.IntegerField()
    pcb_revision = models.IntegerField()

    def __str__(self):
        return f"{self.ems_provider}: {self.product}{self.product_record}.{self.mec_revision}.{self.pcb_revision}"

    class Meta:
        unique_together = [
            ("ems_provider", "product", "pcb_revision", "mec_revision", "product_record"),
        ]


class PublicSuffixList(BaseModel):
    """
    Used for email validation. (SW-2425)
    https://publicsuffix.org/list/
    Hubspot uses this list to validate their emails
    https://knowledge.hubspot.com/forms/forms-faq
    """

    pattern = models.CharField(max_length=300, db_index=True)

    # Normally the patterns in the list indicate a valid suffix.
    # But in cases of whildcards, "!" can be used to make exceptions
    # to wildcard matches.
    # https://github.com/publicsuffix/list/wiki/Format#format
    # In these cases we set is_valid to False.
    is_valid = models.BooleanField(default=True)

    def __str__(self):
        if not self.is_valid:
            return f"!{self.pattern}"
        return f"{self.pattern}"


def get_carousel_item_image_storage():
    return storages["s3_carousel_images"]


class CarouselItem(BaseHistoryModelForModelTranslations):
    """Items for the carousel on the store page"""

    class SpecialModalTypes(models.TextChoices):
        SUGGESTED_ORDER = "SUGGESTED_ORDER", "Suggested Order"
        HTML = "HTML", "HTML"

    class ReferralType(models.TextChoices):
        IS_CUSTOMER = "IS_CUSTOMER", "Is already a Customer"
        IS_NOT_CUSTOMER_WITH_REFERRAL_CODE = (
            "IS_NOT_CUSTOMER_WITH_REFERRAL_CODE",
            "Is not already a Customer but has a Referral Code",
        )  # noqa: E501
        IS_NOT_CUSTOMER_NO_REFERRAL_CODE = (
            "IS_NOT_CUSTOMER_NO_REFERRAL_CODE",
            "Is not already a Customer and has no Referral Code",
        )  # noqa: E501

    title = models.CharField(max_length=300)
    description = models.TextField(max_length=1000, null=True, blank=True)
    html = models.TextField(
        max_length=10000,
        null=True,
        blank=True,
        help_text=_(
            "HTML content to display in the carousel item. "
            "Amplitude tracking will be automatically added to <a> and <button> tags.",
        ),
    )
    button_text = models.TextField(max_length=100, null=True, blank=True)
    button_link = models.URLField()
    image = SVGAndImageField(
        storage=get_carousel_item_image_storage,
        help_text=_("File can be SVG, PNG, JPG, JPEG or GIF"),
        null=True,
        blank=True,
    )
    background_color = models.CharField(max_length=7, default="#000000")
    text_color = models.CharField(max_length=7, default="#ffffff")
    button_background_color = models.CharField(max_length=7, default="#ffffff")
    button_text_color = models.CharField(max_length=7, default="#000000")
    markets = models.ManyToManyField(NofenceCompany, blank=True)
    countries = CountryField(blank=True, help_text=_("You can use the country or market field"), multiple=True)
    ordering = models.IntegerField(default=0)
    referral_type = models.CharField(
        max_length=300,
        null=True,
        blank=True,
        choices=ReferralType.choices,
        help_text=_("Used to determine the type of referral modal to show"),
    )
    special_modal_type = models.CharField(max_length=300, null=True, blank=True, choices=SpecialModalTypes.choices)

    def __str__(self):
        return f"{self.title}-{self.countries}"

    @property
    def image_url(self):
        if self.image:
            return self.image.url
        return None

    @property
    def image_url_with_fallback(self):
        return self.image_url or static("default-product-image.svg")


class TaxCalculation(BaseHistoryModel):
    class Source(models.TextChoices):
        STRIPE = "STRIPE"
        BILLY = "BILLY"

    @dataclasses.dataclass
    class TaxBreakdown:
        tax_amount: Money
        taxable_amount: Money
        tax_percentage: Decimal
        tax_type: str
        taxability_reason: str
        country: str
        state: str

        # Whether the tax is for US sales tax or not.
        # Used to tweak the rendering in some templates
        is_for_us: bool

        def tax_type_display(self) -> str:
            if self.tax_type:
                return self.tax_type.replace("_", " ").title()
            return "Sales Tax"

        def taxability_reason_display(self) -> str:
            return self.taxability_reason.replace("_", " ").title()

    basket = models.ForeignKey("Basket", CASCADE, null=True, blank=True)
    sales_order = models.ForeignKey(SalesOrder, CASCADE, null=True, blank=True)
    invoice = models.ForeignKey(Invoice, CASCADE, null=True, blank=True)

    source = models.CharField(max_length=300, default=Source.STRIPE, choices=Source.choices)
    request = models.JSONField(default=dict)
    result = models.JSONField(default=dict)

    user = models.ForeignKey(User, CASCADE, null=True, blank=True)
    customer = models.ForeignKey(Customer, CASCADE, null=True, blank=True)

    amount_total = MoneyField(max_digits=14, decimal_places=2)
    tax_amount_exclusive = MoneyField(max_digits=14, decimal_places=2)

    def __str__(self):
        return f"{self.source} - {self.tax_amount_exclusive}"

    def get_breakdown(self) -> List[TaxBreakdown]:
        items = []
        if self.is_billy_source():
            items.append(
                TaxCalculation.TaxBreakdown(
                    tax_amount=self.tax_amount_exclusive,
                    taxable_amount=Money(
                        self.result["total_price_excl_vat"]["amount"],
                        self.result["total_price_excl_vat"]["currency"],
                    ),
                    taxability_reason="VAT",
                    tax_percentage=Decimal(self.result["vat_percentage"]),
                    tax_type="VAT",
                    state="",
                    country=str(self.customer.country),
                    is_for_us=False,
                ),
            )

        else:
            currency = get_currency(self.result["currency"].upper())
            for data in self.result["tax_breakdown"]:
                details = data["tax_rate_details"]
                items.append(
                    TaxCalculation.TaxBreakdown(
                        tax_amount=Money(Decimal(data["amount"]) / currency.sub_unit, currency),
                        taxable_amount=Money(Decimal(data["taxable_amount"]) / currency.sub_unit, currency),
                        taxability_reason=data["taxability_reason"],
                        tax_percentage=Decimal(details["percentage_decimal"]),
                        tax_type=details["tax_type"],
                        state=details["state"],
                        country=details["country"],
                        is_for_us=True,
                    ),
                )
        return items

    def get_filtered_breakdown(self) -> List[TaxBreakdown]:
        # We sometimes get shipping as an additional breakdown item, with taxability reason product_exempt,
        # this probably depends on the recipient address. We just hide this for now to not get an additional
        # tax line in the overview.
        return [
            it for it in self.get_breakdown() if it.taxability_reason != "product_exempt" or it.tax_amount.amount > 0
        ]

    def get_tax_calculation_id(self) -> str:
        if self.is_billy_source():
            return f"billy-{self.id}"
        return self.result["id"]

    def is_expired(self) -> bool:
        if self.is_billy_source():
            return False

        expires_at = datetime.fromtimestamp(
            self.result["expires_at"],
            UTC,
        )
        # The calculations seems to be valid for 3 months, which should be plenty of time
        # for the customer to make the payment.
        # Remove one day to add some buffer to make sure we don't init a card payment just
        # as the calculation expires.
        return (expires_at - timedelta(days=1)) < timezone.now()

    def is_billy_source(self):
        return self.source == TaxCalculation.Source.BILLY

    def is_stripe_source(self):
        return self.source == TaxCalculation.Source.STRIPE

    def get_total_tax_rate(self):
        if self.is_billy_source():
            return Decimal(self.result["vat_percentage"])

        # For us we pick the first sales tax we have in the breakdown.
        # This could in theory be wrong if we have multiple different
        # tax rates in the same calulation.
        # This is only used in the proposales for now.

        for breakdown in self.get_filtered_breakdown():
            return breakdown.tax_percentage

        return Decimal("0")


class CollarTroubleshooting(BaseModel):
    session_id = models.CharField(max_length=100)
    serial_no = models.IntegerField()
    user_no = models.IntegerField()
    customer_id = models.IntegerField()
    firmware_version = models.IntegerField()
    internal_errors = models.CharField(max_length=8)
    connects_to_server = models.BooleanField(null=True)
    health_checks = models.JSONField(default=dict, blank=True, null=True)
    troubleshoot_page = models.CharField(max_length=255)

    def __str__(self):
        return self.serial_no


class HTMLContent(BaseHistoryModelForModelTranslations):
    class ContentType(models.TextChoices):
        QQ_BANNER = "QQ_BANNER"
        REFERRAL_BANNER = "REFERRAL_BANNER"
        OTHER = "OTHER"
        REFERRAL_RESTRICTED_BANNER = "REFERRAL_RESTRICTED_BANNER"
        REFERRAL_RESTRICTED_BANNER_REFERRED = "REFERRAL_RESTRICTED_BANNER_REFERRED"
        DELAYED_DELIVERY_POPUP = "DELAYED_DELIVERY_POPUP"

    type = models.CharField(max_length=300, choices=ContentType.choices, default=ContentType.OTHER)
    name = models.CharField(max_length=32, blank=True, null=True)
    content = HTMLField()
    countries = CountryField(blank=True, help_text=_("Used to enable content for a specific country"), multiple=True)

    def __str__(self):
        return f"{self.name} - {self.type}"

    @staticmethod
    def get_content(type: str, country):
        html = (
            HTMLContent.objects.filter(
                type=type,
            )
            .filter(
                Q(countries__contains=country) | Q(countries=[]),
            )
            .first()
        )
        return html.content if html else None


class AppAlert(BaseHistoryModel):
    class Type(models.TextChoices):
        SOLAR_STORM = "SOLAR_STORM"
        NETWORK_ISSUE = "NETWORK_ISSUE"

    type = models.CharField(unique=True, max_length=100, null=False, blank=False, choices=Type.choices)
    countries = CountryField(blank=True, multiple=True, help_text="Will only show info for selected countires")
    active = models.BooleanField(default=False, help_text="Needs to be checked for the info to show in the app")

    def __str__(self):
        return self.type


class VATNumberConfig(BaseHistoryModel):
    class Option(models.TextChoices):
        ALLOW_DUPLICATES = "ALLOW_DUPLICATES"

    vat_number = models.CharField(max_length=300)
    option = models.CharField(max_length=300, choices=Option.choices)

    class Meta:
        constraints = [
            models.UniqueConstraint(
                functions.Upper("vat_number"),
                "option",
                name="unq_vat_number_option",
                violation_error_message="The same option already exists for this VAT number",
            ),
        ]

    def __str__(self):
        return f"{self.vat_number} - {self.get_option_display()}"

    @classmethod
    def is_duplicates_allowed(cls: Self, vat_number: str) -> bool:
        return cls.objects.filter(
            vat_number__iexact=vat_number,
            option=cls.Option.ALLOW_DUPLICATES,
        ).exists()


class ShipmentRoutingRule(BaseHistoryModel):
    filter = models.TextField(
        help_text=(
            "When a sales order matches this filter rule. The shipment for this order will be routed to "
            "the specified shipment warehouse"
        )
    )

    shipment_warehouse = models.ForeignKey(
        Warehouse, CASCADE, help_text=("The warehouse that the shipment will be routed to")
    )

    is_active = models.BooleanField(default=False, help_text=("The rule will only be used when is is set to active."))

    hit_count = models.PositiveIntegerField(
        default=0,
        help_text=("The number of time a shipment has been routed using this rule"),
    )

    sort_order = models.PositiveIntegerField(default=0)

    class Meta:
        ordering = ["sort_order"]

    def __str__(self):
        return f"Rule({self.id})"

    def clean(self):
        if self.filter:
            from core import shipment_routing

            try:
                ast = DjangoQLParser().parse(self.filter)
                schema_instance = shipment_routing.SalesOrderRoutingQLSchema(SalesOrder)
                schema_instance.validate(ast)
            except Exception as e:
                raise ValidationError({"filter": f"Invalid filter rule: {e}"})


class ShipmentRoutingEvalLog(BaseHistoryModel):
    class Result(models.TextChoices):
        HIT = "HIT"
        MISS = "MISS"
        NOT_REACHED = "NOT_REACHED"
        NOT_ACTIVE = "NOT_ACTIVE"

    shipment_routing_rule = models.ForeignKey(
        ShipmentRoutingRule,
        SET_NULL,
        null=True,
    )
    sales_order = models.ForeignKey(
        SalesOrder,
        SET_NULL,
        null=True,
    )
    shipment_warehouse = models.ForeignKey(
        Warehouse,
        SET_NULL,
        null=True,
    )

    filter = models.TextField(help_text="The filter from the rule at the time of evauation")

    result = models.CharField(max_length=300, choices=Result.choices)
    eval_index = models.PositiveIntegerField(
        default=0,
        help_text=(
            "The position in the rule list this evaluation happened at. E.g the first rule will be 0, the next 1",
        ),
    )

    def __str__(self):
        return f"{self.eval_index}"
