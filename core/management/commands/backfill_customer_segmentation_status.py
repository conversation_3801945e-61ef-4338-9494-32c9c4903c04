import logging
from django.core.management.base import BaseCommand
from django.db import transaction
from django.db.models import Q

from core.models import Customer, QuickQuote
from core.models.quickquote import SegmentationStatus
from core.hubspot import HubSpotClient
from core.integrations.hubspot_integration.datamapping import (
    map_hubspot_segmentation_status_to_billy_segmentation_status,
)
from core.default_api_clients import create_hubspot_client

logger = logging.getLogger(__name__)


class Command(BaseCommand):
    help = "Backfill customer segmentation_status from HubSpot, fallback to QuickQuote logic"

    def add_arguments(self, parser):
        parser.add_argument(
            "--customer-id",
            type=int,
            help="Process only specific customer ID",
        )
        parser.add_argument(
            "--batch-size",
            type=int,
            default=100,
            help="Number of customers to process in each batch (default: 100)",
        )
        parser.add_argument(
            "--dry-run",
            action="store_true",
            help="Show what would be updated without making changes",
        )
        parser.add_argument(
            "--force",
            action="store_true",
            help="Update even if segmentation_status is already set",
        )

    def handle(self, *args, **options):
        customer_id = options.get("customer_id")
        batch_size = options.get("batch_size", 100)
        dry_run = options.get("dry_run", False)
        force = options.get("force", False)

        if dry_run:
            self.stdout.write(self.style.WARNING("DRY RUN MODE - No changes will be made"))

        # Get HubSpot client
        try:
            hubspot_client = create_hubspot_client()
        except Exception as e:
            self.stdout.write(self.style.ERROR(f"Failed to create HubSpot client: {e}"))
            return

        # Build queryset
        queryset = Customer.objects.all()

        if customer_id:
            queryset = queryset.filter(id=customer_id)
        elif not force:
            # Only process customers without segmentation_status
            queryset = queryset.filter(Q(segmentation_status__isnull=True) | Q(segmentation_status=""))

        customers = queryset.order_by("id")
        total_customers = customers.count()

        self.stdout.write(f"Found {total_customers} customers to process")

        processed = 0
        updated_from_hubspot = 0
        updated_from_quickquote = 0
        errors = 0

        # Process customers in batches
        for batch_start in range(0, total_customers, batch_size):
            batch_end = min(batch_start + batch_size, total_customers)
            self.stdout.write(f"Processing batch {batch_start + 1}-{batch_end} of {total_customers}")

            batch_customers = list(customers[batch_start:batch_end])

            # Process this batch
            batch_results = self._process_batch(batch_customers, hubspot_client, dry_run)

            for customer, result in batch_results.items():
                processed += 1
                if result:
                    source, segmentation_status = result
                    if source == "hubspot":
                        updated_from_hubspot += 1
                    elif source == "quickquote":
                        updated_from_quickquote += 1

                    if not dry_run:
                        self.stdout.write(
                            f"Updated customer {customer.id} with segmentation_status: {segmentation_status} (from {source})"
                        )
                    else:
                        self.stdout.write(
                            f"Would update customer {customer.id} with segmentation_status: {segmentation_status} (from {source})"
                        )

        self.stdout.write(
            self.style.SUCCESS(
                f"Processed {processed} customers, updated {updated_from_hubspot + updated_from_quickquote} total "
                f"({updated_from_hubspot} from HubSpot, {updated_from_quickquote} from QuickQuote), "
                f"errors {errors}"
            )
        )

    def _process_batch(self, customers: list, hubspot_client: HubSpotClient, dry_run: bool) -> dict:
        """Process a batch of customers using HubSpot batch API first, then QuickQuote fallback"""
        results = {}

        # First, try to get segmentation from HubSpot for customers with hubspot_id
        customers_with_hubspot_id = [c for c in customers if c.hubspot_id]
        customers_without_hubspot_id = [c for c in customers if not c.hubspot_id]

        # Process customers with HubSpot ID using batch API
        if customers_with_hubspot_id:
            results.update(
                self._process_customers_with_hubspot_batch(customers_with_hubspot_id, hubspot_client, dry_run)
            )

        # Process customers without HubSpot ID (QuickQuote only)
        for customer in customers_without_hubspot_id:
            try:
                quickquote_result = self._get_segmentation_from_quickquote(customer)
                if quickquote_result:
                    if not dry_run:
                        with transaction.atomic():
                            customer.segmentation_status = quickquote_result
                            customer._change_reason = "Backfilled from QuickQuote"
                            customer.save(update_fields=["segmentation_status", "updated_at"])
                    results[customer] = ("quickquote", quickquote_result)
                else:
                    results[customer] = None

            except Exception as e:
                logger.error(f"Error processing customer {customer.id}: {e}")
                self.stdout.write(self.style.ERROR(f"Error processing customer {customer.id}: {e}"))
                results[customer] = None

        return results

    def _process_customers_with_hubspot_batch(
        self, customers: list, hubspot_client: HubSpotClient, dry_run: bool
    ) -> dict:
        """Process customers with HubSpot IDs using batch API"""
        results = {}

        # Group customers by HubSpot ID for batch processing
        hubspot_ids = [c.hubspot_id for c in customers]
        customer_by_hubspot_id = {c.hubspot_id: c for c in customers}

        # Process in batches of 100 (HubSpot batch API limit)
        batch_size = 100
        for i in range(0, len(hubspot_ids), batch_size):
            batch_hubspot_ids = hubspot_ids[i : i + batch_size]
            batch_customers = [customer_by_hubspot_id[hid] for hid in batch_hubspot_ids]

            try:
                # Get companies in batch
                companies = hubspot_client.get_companies_batch(batch_hubspot_ids)
                company_by_id = {company.id: company for company in companies}

                # Process each customer in the batch
                for customer in batch_customers:
                    try:
                        company = company_by_id.get(customer.hubspot_id)

                        if company:
                            # Try to get segmentation from HubSpot
                            hubspot_result = self._get_segmentation_from_hubspot_company(company)
                            if hubspot_result:
                                if not dry_run:
                                    with transaction.atomic():
                                        customer.segmentation_status = hubspot_result
                                        customer._change_reason = "Backfilled from HubSpot"
                                        customer.save(update_fields=["segmentation_status", "updated_at"])
                                results[customer] = ("hubspot", hubspot_result)
                                continue

                        # If HubSpot fails, try QuickQuote
                        quickquote_result = self._get_segmentation_from_quickquote(customer)
                        if quickquote_result:
                            if not dry_run:
                                with transaction.atomic():
                                    customer.segmentation_status = quickquote_result
                                    customer._change_reason = "Backfilled from QuickQuote (HubSpot fallback)"
                                    customer.save(update_fields=["segmentation_status", "updated_at"])
                            results[customer] = ("quickquote", quickquote_result)
                            continue

                        # Neither worked
                        results[customer] = None

                    except Exception as e:
                        logger.error(f"Error processing customer {customer.id}: {e}")
                        self.stdout.write(self.style.ERROR(f"Error processing customer {customer.id}: {e}"))
                        results[customer] = None

            except Exception as e:
                logger.error(f"Error in batch HubSpot request: {e}")
                self.stdout.write(self.style.ERROR(f"Error in batch HubSpot request: {e}"))

                # Fall back to individual processing for this batch
                for customer in batch_customers:
                    try:
                        hubspot_result = self._get_segmentation_from_hubspot(customer, hubspot_client)
                        if hubspot_result:
                            if not dry_run:
                                with transaction.atomic():
                                    customer.segmentation_status = hubspot_result
                                    customer._change_reason = "Backfilled from HubSpot"
                                    customer.save(update_fields=["segmentation_status", "updated_at"])
                            results[customer] = ("hubspot", hubspot_result)
                            continue

                        # If HubSpot fails, try QuickQuote
                        quickquote_result = self._get_segmentation_from_quickquote(customer)
                        if quickquote_result:
                            if not dry_run:
                                with transaction.atomic():
                                    customer.segmentation_status = quickquote_result
                                    customer._change_reason = "Backfilled from QuickQuote (HubSpot fallback)"
                                    customer.save(update_fields=["segmentation_status", "updated_at"])
                            results[customer] = ("quickquote", quickquote_result)
                            continue

                        # Neither worked
                        results[customer] = None

                    except Exception as e:
                        logger.error(f"Error processing customer {customer.id}: {e}")
                        self.stdout.write(self.style.ERROR(f"Error processing customer {customer.id}: {e}"))
                        results[customer] = None

        return results

    def _get_segmentation_from_hubspot(self, customer: Customer, hubspot_client: HubSpotClient) -> str:
        """Get segmentation status from HubSpot company_segment property"""
        try:
            # Get company data from HubSpot
            company = hubspot_client.get_company(customer.hubspot_id)
            if not company or not company.properties:
                return None

            return self._get_segmentation_from_hubspot_company(company)

        except Exception as e:
            logger.error(f"Error fetching HubSpot data for customer {customer.id}: {e}")
            return None

    def _get_segmentation_from_hubspot_company(self, company) -> str:
        """Get segmentation status from HubSpot company object"""
        try:
            # Get segmentation status from HubSpot
            hubspot_segmentation = getattr(company.properties, "company_segment", None)
            if not hubspot_segmentation:
                return None

            # Map HubSpot segmentation to Billy segmentation
            billy_segmentation = map_hubspot_segmentation_status_to_billy_segmentation_status(hubspot_segmentation)

            return billy_segmentation

        except Exception as e:
            logger.error(f"Error processing HubSpot company data: {e}")
            return None

    def _get_segmentation_from_quickquote(self, customer: Customer) -> str:
        """Get segmentation status from latest QuickQuote using the segmentation logic"""
        try:
            # Get latest completed QuickQuote for this customer
            latest_qq = (
                QuickQuote.objects.filter(
                    email__iexact=customer.email,
                    status=QuickQuote.Status.COMPLETED,
                )
                .order_by("-created_at")
                .first()
            )

            if not latest_qq:
                return None

            # Get segmentation status from QuickQuote using the property logic
            segmentation_status = latest_qq.segmentation_status

            # Only return if it's not UNKNOWN
            if segmentation_status and segmentation_status != SegmentationStatus.UNKNOWN:
                return segmentation_status

            return None

        except Exception as e:
            logger.error(f"Error processing QuickQuote data for customer {customer.id}: {e}")
            return None
