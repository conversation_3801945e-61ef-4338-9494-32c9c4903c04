import logging
import json
import time
from typing import List, Dict, Any

from django.core.management import BaseCommand
from django.db import transaction
from core.hubspot import HubSpotClient
from core.models import Customer

from core.integrations.hubspot_integration.datamapping import (
    map_hubspot_segmentation_status_to_billy_segmentation_status,
)
from core.default_api_clients import create_hubspot_client

logger = logging.getLogger(__name__)


class Command(BaseCommand):
    """
    Backfill billy customer's segmentation_status with value from hubspot company_segment.
    Falls back to QuickQuote.segmentation_status if hubspot company_segment is not available.
    """

    hubspot: HubSpotClient

    def add_arguments(self, parser):
        parser.add_argument("--dry-run", action="store_true", help="Run without making changes")
        parser.add_argument("--pause-after-n", nargs="?", type=int, help="Pause script every n processed customer")
        parser.add_argument(
            "--sleep-time",
            default=0.2,
            type=float,
            help="Sleep time between processing each customer",
        )
        parser.add_argument(
            "--overwrite",
            action="store_true",
            help="Overwrite existing segmentation_status values (default: only fill empty values)",
        )
        parser.add_argument(
            "--batch-size",
            default=100,
            type=int,
            help="Number of companies to fetch from HubSpot in each batch",
        )

    def handle(self, dry_run, pause_after_n, sleep_time, overwrite, batch_size, *args, **options):
        if dry_run:
            logger.warn("Running in dry run mode. No real changes will be made to the customers.")
        if pause_after_n:
            logger.warn(f"Pausing every {pause_after_n} customer.")
        if overwrite:
            logger.warn("Overwriting existing segmentation_status values.")

        self.hubspot = create_hubspot_client()

        # Filter customers based on whether we want to overwrite existing values
        if overwrite:
            customers = Customer.objects.filter(hubspot_id__isnull=False).all()
        else:
            customers = (
                Customer.objects.filter(hubspot_id__isnull=False, segmentation_status__isnull=True)
                .all()
            )

        logger.info(f"Found {len(customers)} customers to process")

        results = {"updated": [], "failed": [], "skipped": [], "fallback_used": []}

        # Process customers in batches
        for batch_start in range(0, len(customers), batch_size):
            batch_end = min(batch_start + batch_size, len(customers))
            customer_batch = customers[batch_start:batch_end]

            logger.info(
                f"Processing batch {batch_start // batch_size + 1}: customers {batch_start + 1} to {batch_end}"
            )

            # Get HubSpot company IDs for this batch
            hubspot_ids = [customer.hubspot_id for customer in customer_batch]

            # Fetch companies from HubSpot in batch
            try:
                companies_data = self._fetch_companies_batch(hubspot_ids)
            except Exception:
                logger.exception("Failed to fetch companies batch from HubSpot")
                results["failed"].extend([(customer.id, customer.hubspot_id) for customer in customer_batch])
                continue

            # Process each customer in the batch
            for index, customer in enumerate(customer_batch, batch_start + 1):
                try:
                    self._process_customer(customer, companies_data, results, dry_run)

                    if pause_after_n and index % pause_after_n == 0:
                        logger.info(f"Checked {pause_after_n} customers")
                        input("Press enter to continue script...")

                except Exception:
                    logger.exception(f"Failed to process customer {customer.id}")
                    results["failed"].append((customer.id, customer.hubspot_id))

            time.sleep(sleep_time)

        logger.info(f"Completed processing {len(customers)} customers")
        logger.info(
            f"Updated: {len(results['updated'])}, Failed: {len(results['failed'])}, Skipped: {len(results['skipped'])}, Fallback used: {len(results['fallback_used'])}"
        )

        return json.dumps(results)

    def _fetch_companies_batch(self, hubspot_ids: List[str]) -> Dict[str, Any]:
        """Fetch companies from HubSpot using individual requests"""
        companies_data = {}
        for hubspot_id in hubspot_ids:
            try:
                company = self.hubspot.get_company(hubspot_id)
                companies_data[hubspot_id] = company
            except Exception:
                logger.exception(f"Failed to get hubspot company {hubspot_id}")
        return companies_data

    def _process_customer(
        self, customer: Customer, companies_data: Dict[str, Any], results: Dict[str, List], dry_run: bool
    ):
        """Process a single customer"""
        company = companies_data.get(customer.hubspot_id)

        if not company:
            results["failed"].append((customer.id, customer.hubspot_id))
            return

        with transaction.atomic():
            customer.refresh_from_db()
            old_status = customer.segmentation_status

            # Try to get segmentation status from HubSpot first
            new_status = map_hubspot_segmentation_status_to_billy_segmentation_status(
                company.properties.company_segment,
            )

            fallback_used = False

            # If HubSpot doesn't have company_segment, fall back to QuickQuote
            if new_status is None and customer.quick_quote:
                new_status = customer.quick_quote.segmentation_status
                fallback_used = True
                logger.info(f"Using QuickQuote segmentation_status for customer {customer.id}")

            if old_status != new_status and new_status is not None:
                if not dry_run:
                    customer.segmentation_status = new_status
                    customer._change_reason = "Management command backfill segmentation_status from hubspot"
                    customer.save(update_fields=["segmentation_status", "updated_at"])

                update_info = (customer.id, customer.hubspot_id, str(old_status), str(new_status))
                if fallback_used:
                    results["fallback_used"].append(update_info)
                else:
                    results["updated"].append(update_info)

                logger.info(
                    f"Updated {customer} segmentation_status: {old_status} -> {new_status}"
                    + (" (fallback)" if fallback_used else "")
                )
            else:
                results["skipped"].append((customer.id, customer.hubspot_id))
