# Generated by Django 4.2.19 on 2025-07-08 08:43

from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ('core', '0839_auto_20250701_1014'),
    ]

    operations = [
        migrations.AddField(
            model_name='customer',
            name='segmentation_status',
            field=models.CharField(blank=True, choices=[('BEEF_CATTLE_EXTENSIVE', 'Beef Cattle Extensive'), ('HABITAT_MANAGEMENT', 'Habitat Management'), ('AGROFORESTRY', 'Agroforestry'), ('DAIRY_NON_MILKING', 'Dairy Non Milking'), ('COMMERCIAL_SHEEP_GOAT', 'Commercial Sheep Goat'), ('NON_TARGET_DAIRY_MILKING', 'Non Target Dairy Milking'), ('UNKNOWN', 'Unknown')], max_length=50, null=True),
        ),
        migrations.AddField(
            model_name='historicalcustomer',
            name='segmentation_status',
            field=models.<PERSON><PERSON><PERSON><PERSON>(blank=True, choices=[('BEEF_CATTLE_EXTENSIVE', 'Beef Cattle Extensive'), ('HABITAT_MANAGEMENT', 'Habitat Management'), ('AGROFORESTRY', 'Agroforestry'), ('DAIRY_NON_MILKING', 'Dairy Non Milking'), ('COMMERCIAL_SHEEP_GOAT', 'Commercial Sheep Goat'), ('NON_TARGET_DAIRY_MILKING', 'Non Target Dairy Milking'), ('UNKNOWN', 'Unknown')], max_length=50, null=True),
        ),
    ]
