import logging

from django.utils import timezone
from hubspot.crm.companies.exceptions import ApiException as CompanyApiException

from core.models import (
    Customer,
    User,
)

from core.integrations.base import SyncHandler
from core.integrations.signals import notify_billy_customer_changed
from core.hubspot import HubSpotCompanySchema
from core.models.quickquote import QuickQuote

from . import utils
from .data import HubSpotCompanyData
from .datamapping import (
    map_hubspot_initiated_contact_to_customer_initiated_contact,
    map_hubspot_segmentation_status_to_billy_segmentation_status,
)


logger = logging.getLogger(__name__)


class HubSpotToBillyCompanySync(SyncHandler):
    """
    We no longer sync most of the data from HubSpot companies to billy, but we still have a few
    properties that we monitor and that have special meaning and trigger some action
    when changing.
    """

    def __init__(self, hubspot: HubSpotCompanyData):
        self.hubspot = hubspot

    def is_applicable(self):
        self.customer = self._get_existing_customer()

        if not self.customer:
            logger.info(
                f"No billy customer found for hubspot company {self.hubspot.company_id}",
            )
            return False

        return True

    def run(self):
        logger.info(
            f"Syncing billy customer {self.customer.id} with ",
            extra={"company": HubSpotCompanySchema().dump(self.hubspot.company)},
        )

        customer = self.customer

        # Call this before the other updates to make sure the first_contact_initiated_at is set correctly
        self._update_first_contact_initiated_at(customer, self.hubspot)

        # We still save a few properties from hubspot
        self._update_customer_properties(customer, self.hubspot)

        # Qualification
        if self._is_new_qualification(customer, self.hubspot):
            logger.info(f"New qualification for {customer=}")
            customer.send_account_activation_flow_email()
        elif self._is_unqualified(self.hubspot):
            logger.info(f"{customer=} is not qualified")
            customer.update_customer_status(Customer.Status.UNQUALIFIED_CUSTOMER)
            if not customer.unqualified_by_sales and self._is_unqualified_by_sales(customer):
                customer.unqualified_by_sales = True
                customer.save(update_fields=["unqualified_by_sales", "updated_at"])
        elif self._is_qualification_unknown(self.hubspot) and not self._is_customer_unreachable(customer):
            # If the customer has been set as unreachable, we leave the customer in the unreachable state.

            logger.info(f"{customer=} qualification status is unknown")
            # A customer may complete a new quick quote after first being rejected in an earlier quick quote.
            # In this case we try to reset the status back to prospect.
            customer.update_customer_status(Customer.Status.PROSPECT)

            if customer.unqualified_by_sales:
                logger.info(f"{customer=} resetting unqualified by sales status")
                customer.unqualified_by_sales = False
                customer.save(update_fields=["unqualified_by_sales", "updated_at"])

        # We notify event if there was no changes in case the customer has not been synced to visma
        notify_billy_customer_changed(sender=self.__class__, customer_id=self.customer.id)

        return self.customer.id

    def _update_customer_properties(self, customer: Customer, hubspot: HubSpotCompanyData):
        """We still sync some properties from hubspot"""

        changes = []

        properties = {
            "confirmed_no_interest": hubspot.company.properties.confirmed_no_interest == "true",
            "customer_gdpr": hubspot.company.properties.customer_gdpr,
        }

        changes.extend(utils.update_changed_properties(customer, properties.keys(), properties))

        properties = {
            "initiated_contact": map_hubspot_initiated_contact_to_customer_initiated_contact(
                hubspot.company.properties.initiated_contact,
            ),
            "segmentation_status": map_hubspot_segmentation_status_to_billy_segmentation_status(
                hubspot.company.properties.company_segment,
            ),
        }

        changes.extend(utils.update_changed_properties_if_not_empty(customer, properties.keys(), properties))

        if changes:
            logger.info(
                f"Updating billy customer {self.customer.id} with data from hubspot {self.hubspot.company.id}",
                extra={
                    "changes": [f"{attr}:{old_value}->{new_value}" for attr, old_value, new_value in changes],
                },
            )
            self.customer._change_reason = "Billy hubspot integration customer update"
            self.customer.save(update_fields=[attr for attr, _, _ in changes] + ["updated_at"])

    def _update_first_contact_initiated_at(self, customer: Customer, hubspot: HubSpotCompanyData):
        if (
            customer.initiated_contact is None
            and hubspot.company.properties.initiated_contact == "1st. attempt - failed"
        ):
            customer.first_contact_initiated_at = timezone.now()
            customer.save(update_fields=["first_contact_initiated_at", "updated_at"])

    def _is_new_qualification(self, customer: Customer, hubspot: HubSpotCompanyData):
        valid_statuses = [
            User.ActivationStatus.NOT_ACTIVATED,
            User.ActivationStatus.APP_USER,
        ]

        if customer.user.activation_status in valid_statuses:
            # Segment must be set
            segment = hubspot.company.properties.company_segment
            # associated quick quote must not be auto qualified,
            # because we already send the activation email during QQ completion
            qq_auto_qualified = customer.quick_quote and customer.quick_quote.auto_qualified
            return segment and hubspot.company.properties.qualification == "Qualified" and not qq_auto_qualified

        return False

    def _is_unqualified(self, hubspot: HubSpotCompanyData):
        return hubspot.company.properties.qualification == "Not Qualified"

    def _is_unqualified_by_sales(self, customer: Customer):
        quick_quote = customer.quick_quote
        if quick_quote:
            return (
                quick_quote.get_qualification_status_based_on_business_type
                != QuickQuote.QualificationStatus.UNQUALIFIED
            )
        return True

    def _is_qualification_unknown(self, hubspot: HubSpotCompanyData):
        return hubspot.company.properties.qualification in ["Qualification Unknown", "Likely Qualified"]

    def _is_customer_unreachable(self, customer: Customer):
        return customer.customer_status == Customer.Status.PROSPECT_UNREACHABLE

    def _get_existing_customer(self) -> Customer | None:
        try:
            self.hubspot.company
        except CompanyApiException as e:
            if e.status == 404:
                return None
            else:
                raise e

        return Customer.objects.filter(hubspot_id=self.hubspot.company.id).first()
