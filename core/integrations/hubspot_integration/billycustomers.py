import logging
import dataclasses
import re

from hubspot.crm.companies import ApiException as CompanyApiException
from hubspot.crm.contacts import ApiException as ContactApiException

from core.hubspot import (
    HubSpotClient,
    HubSpotCompany,
    HubSpotCompanySchema,
    HubSpotContact,
    HubSpotContactSchema,
)
from core.models import (
    Customer,
    CustomerCompanyAddress,
    CustomerInvoiceAddress,
    CustomerDeliveryAddress,
    QuickQuote,
)

from core.integrations.base import SyncHandler

from . import datamapping


logger = logging.getLogger(__name__)


class BillyCustomerToHubSpotCompany(SyncHandler):
    def __init__(self, billy_customer: Customer, hubspot_client: HubSpotClient):
        self.billy_customer = billy_customer
        self.hubspot_client = hubspot_client

    def is_applicable(self):
        return True

    def run(self):
        logger.info(f"Syncing billy customer {self.billy_customer} to hubspot {self.billy_customer.hubspot_id=}")

        company_props = self.build_company()

        if not self.billy_customer.hubspot_id:
            company = self.hubspot_client.create_company(**self.dump_company_props(company_props))
            self.billy_customer.hubspot_id = company.id
            self.billy_customer._change_reason = "Billy created company in hubspot"
            self.billy_customer.save(update_fields=["hubspot_id", "updated_at"])
        else:
            self.hubspot_client.update_company_properties(
                self.billy_customer.hubspot_id,
                **self.dump_company_props(company_props),
            )

        logger.info(f"Billy customer {self.billy_customer} synced to company {self.billy_customer.hubspot_id}")

        return self.billy_customer.hubspot_id

    def dump_company_props(self, properties: HubSpotCompany.Properties) -> dict:
        return HubSpotCompanySchema().dump({"properties": properties})["properties"]

    def build_company(self) -> HubSpotCompany.Properties:
        properties = HubSpotCompany.Properties(
            name=self.billy_customer.name,
            country_select=str(self.billy_customer.country),
            business_registration_number=self.billy_customer.business_reg_no,
            eori_number=self.billy_customer.eori_number,
            flat_rate_scheme=self.billy_customer.flat_rate_scheme,
            main_contact_person_email=self.billy_customer.email,
            invoice_contact_person_email=self.billy_customer.invoice_contact_email,
            delivery_contact_person_email=self.billy_customer.delivery_contact_email,
            visma_customer_class=datamapping.map_customer_type_to_company_type(self.billy_customer.customer_type),
            phone=self.billy_customer.phone_number_formatted,
            company_status=datamapping.map_billy_customer_status_to_company_status(
                self.billy_customer.customer_status,
            ),
        )

        self.set_company_address_props(properties)
        self.set_delivery_address_props(properties)
        self.set_invoice_address_props(properties)

        return properties

    def set_company_address_props(self, properties: HubSpotCompany.Properties):
        address: CustomerCompanyAddress = self.billy_customer.company_address_value
        if address is not None:
            properties.address = address.address1
            properties.address2 = address.address2
            properties.zip = address.zip_code
            properties.city = address.city
            properties.state = address.region

    def set_delivery_address_props(self, properties: HubSpotCompany.Properties):
        address: CustomerDeliveryAddress = self.billy_customer.delivery_address_value
        if address is not None:
            properties.delivery_street_address = address.address1
            properties.delivery_street_address_2 = address.address2
            properties.delivery_zip_code = address.zip_code
            properties.delivery_city = address.city
            properties.delivery_state = address.region

    def set_invoice_address_props(self, properties: HubSpotCompany.Properties):
        address: CustomerInvoiceAddress = self.billy_customer.invoice_address_value
        if address is not None:
            properties.post_address = address.address1
            properties.post_street_address_2 = address.address2
            properties.post_zipcode = address.zip_code
            properties.post_city = address.city
            properties.post_state = address.region


class BillyCustomerPrimaryContactToHubSpotContact(SyncHandler):
    def __init__(self, billy_customer: Customer, hubspot_client: HubSpotClient):
        self.billy_customer = billy_customer
        self.hubspot_client = hubspot_client

    def is_applicable(self):
        if not self.billy_customer.hubspot_id:
            logger.info(f"Billy customer {self.billy_customer} is missing hubspot_id. Skipping primary contact sync.")
            return False

        return True

    def run(self):
        logger.info(
            f"Syncing billy customer {self.billy_customer} to hubspot primary contact of company "
            f"{self.billy_customer.hubspot_id}",
        )

        contact_props = self.build_primary_contact()

        primary_contact = self.hubspot_client.get_contact_by_email(self.billy_customer.email)

        if not primary_contact:
            logger.info(
                f"No existing contact found for {self.billy_customer.email}. Creating new contact",
                extra={
                    "props": dataclasses.asdict(contact_props),
                },
            )
            try:
                primary_contact = self.hubspot_client.create_contact(**dataclasses.asdict(contact_props))
            except ContactApiException as e:
                existing_contact_id = self._find_existing_contact_id(e)
                if not existing_contact_id:
                    logger.info(
                        f"Failed to created contact for {self.billy_customer.email}.",
                        extra={
                            "props": dataclasses.asdict(contact_props),
                        },
                    )
                    raise e

                logger.info(
                    f"Found existing hubspot contact in 409 response: {existing_contact_id}. Updating properties.",
                    extra={
                        "updated_props": dataclasses.asdict(contact_props),
                    },
                )
                primary_contact = self.hubspot_client.update_contact_properties_with_response(
                    existing_contact_id,
                    **dataclasses.asdict(contact_props),
                )

        else:
            logger.info(
                f"Existing hubspot contact found {primary_contact.id}. Updating properties.",
                extra={
                    "existing_contact": HubSpotContactSchema().dump(primary_contact),
                    "updated_props": dataclasses.asdict(contact_props),
                },
            )
            primary_contact = self.hubspot_client.update_contact_properties_with_response(
                primary_contact.id,
                **dataclasses.asdict(contact_props),
            )

        logger.info(
            f"Billy customer {self.billy_customer} synced to hubspot contact {primary_contact.id}",
            extra={
                "updated_contact": HubSpotContactSchema().dump(primary_contact),
            },
        )

        return primary_contact.id

    def build_primary_contact(self) -> HubSpotContact.Properties:
        properties = HubSpotContact.Properties(
            firstname=self.billy_customer.user.first_name,
            lastname=self.billy_customer.user.last_name,
            email=self.billy_customer.email,
            phone=self.billy_customer.phone_number_formatted,
            mobilephone=self.billy_customer.phone_number_mobile_formatted,
            hs_whatsapp_phone_number=self.billy_customer.phone_number_mobile_formatted,
            sms_consent=self.billy_customer.sms_consent,
            associatedcompanyid=self.billy_customer.hubspot_id,
            company=self.billy_customer.name,
            country_select=str(self.billy_customer.country),
        )

        return properties

    def _find_existing_contact_id(self, exc: ContactApiException) -> str | None:
        """Find the existing id in a conflict response.

        If the sync is re-triggered quickly after initially creating the contact,
        the hubspot api may not return the contact on the get by email lookup, as there
        is a delay before the api returns the new object.

        But the unique check on the email seems to be instant, and the error response
        returns the existing contact id. So we find the existing id, we can run an
        update on the existing contact.
        """
        if exc.status == 409 and exc.body and "message" in exc.body:
            if "Contact already exists" in exc.body:
                m = re.search(r"Existing ID: (\d+)", exc.body)
                if m:
                    return m.group(1)


class BillyLatestQuickQuoteToHubSpot(SyncHandler):
    def __init__(self, billy_customer: Customer, hubspot_primary_contact_id: int, hubspot_client: HubSpotClient):
        self.billy_customer = billy_customer
        self.hubspot_primary_contact_id = hubspot_primary_contact_id
        self.hubspot_client = hubspot_client

    def is_applicable(self):
        if not self.billy_customer.hubspot_id:
            logger.info(
                f"Billy customer {self.billy_customer} is missing hubspot_id. Skipping latest quick quote sync.",
            )
            return False

        self.quick_quote = (
            QuickQuote.objects.filter(
                email__iexact=self.billy_customer.email,
                status=QuickQuote.Status.COMPLETED,
            )
            .order_by("-created_at")
            .first()
        )

        if not self.quick_quote:
            logger.info(f"Quick quote not found for {self.billy_customer}.Skipping quick quote sync")
            return False

        return True

    def run(self):
        logger.info(
            f"Syncing billy quick quote {self.quick_quote.id} - {self.quick_quote} to hubspot for "
            f"{self.billy_customer}. {self.billy_customer.hubspot_id=}, {self.hubspot_primary_contact_id=}.",
        )

        # These properties are only updated when not already set in hubspot
        company_properties = dict(
            company_source=self.get_source(self.quick_quote.source),
            company_segment=self.get_hubspot_segment(self.quick_quote.segmentation_status),
            qualification=self.get_hubspot_qualification(
                self.quick_quote.qualification_status,
                self.quick_quote.auto_qualified,
            ),
            animal_type=self.get_hubspot_animal_type(
                self.quick_quote.animal.animal if self.quick_quote.get_animal() else None,
            ),
        )

        farming_purpose = self.quick_quote.get_farming_purpose()
        animal = self.quick_quote.get_animal()

        # These properties are always set to the values from the latest quick quote
        latest_quick_quote_properties = dict(
            # New properties matching the values in the quick quote card views
            quick_quote_business_type=self.quick_quote.business_type or "",
            quick_quote_qualification_status=self.quick_quote.qualification_status or "",
            quick_quote_animal=(animal.animal.title() if animal else "") or "",
            quick_quote_farming_purpose=(farming_purpose.farming_purpose if farming_purpose else "") or "",
            quick_quote_number_of_animals=self.quick_quote.number_of_animals or -1,
            quick_quote_grazing_method=self.quick_quote.grazing_method or "",
            quick_quote_pasture_types=";".join(
                it.pasture_type for it in self.quick_quote.pasture_types.order_by("pasture_type").all()
            ),
            quick_quote_mobile_coverage=self.quick_quote.mobile_coverage or "",
            quick_quote_country=str(self.quick_quote.country) or "",
            # Not formatting the timezone like in the QuickQuote card here, as the offset values are changing
            # depending on time of year.
            quick_quote_timezone=self.quick_quote.browser_timezone or "",
            quick_quote_auto_qualify_cohort=self.quick_quote.auto_qualify_cohort,
        )

        self.hubspot_client.update_company_properties(
            self.billy_customer.hubspot_id,
            # We always set the quick quote created property
            quick_quote_created=self.quick_quote.completed_at,
            **latest_quick_quote_properties,
            **self.filter_company_properties(company_properties),
        )

        contact_properties = self.filter_contact_properties(
            dict(
                hs_legal_basis="Legitimate interest – prospect/lead",
            ),
        )

        if contact_properties:
            self.hubspot_client.update_contact_properties(
                self.hubspot_primary_contact_id,
                **contact_properties,
            )

        self.hubspot_client.set_communication_preferences_for_contact(self.billy_customer.email)

        self.quick_quote.hubspot_company_id = self.billy_customer.hubspot_id
        self.quick_quote.hubspot_contact_id = self.hubspot_primary_contact_id
        self.quick_quote.save(update_fields=["hubspot_company_id", "hubspot_contact_id", "updated_at"])

        logger.info(
            f"Done Syncing billy quick quote {self.quick_quote} to hubspot for "
            f"{self.billy_customer}. {self.billy_customer.hubspot_id=}, {self.hubspot_primary_contact_id=}.",
        )

        return self.billy_customer.hubspot_id

    @staticmethod
    def get_source(source: QuickQuote.Source) -> str:
        match source:
            case QuickQuote.Source.REQUEST_ACCOUNT:
                return "2nd hand"
            case _:
                return "QQ"

    @staticmethod
    def get_hubspot_segment(segmentation_status: QuickQuote.SegmentationStatus) -> str | None:
        SEGMENTATION_MAPPING = {
            "BEEF_CATTLE_EXTENSIVE": "Beef Cattle Extensive",
            "HABITAT_MANAGEMENT": "Habitat Management",
            "AGROFORESTRY": "Agroforestry",
            "DAIRY_NON_MILKING": "Dairy non-milking",
            "COMMERCIAL_SHEEP_GOAT": "Commercial Sheep/Goat",
            "NON_TARGET_DAIRY_MILKING": "Dairy Milking",
            "UNKNOWN": "",
        }
        value = SEGMENTATION_MAPPING.get(
            segmentation_status,
            None,
        )
        logger.info(f"Mapping {segmentation_status=} to {value=}")
        return value

    @staticmethod
    def get_hubspot_qualification(
        qualification_status: QuickQuote.QualificationStatus,
        auto_qualified: bool = False,
    ) -> str | None:
        if auto_qualified:
            return "Qualified"

        QUALIFICATION_STATUS_MAPPING = {
            "LIKELY_QUALIFIED": "Likely Qualified",
            "QUALIFICATION_UNKNOWN": "Qualification Unknown",
            "UNQUALIFIED": "Not Qualified",
        }
        value = QUALIFICATION_STATUS_MAPPING.get(
            qualification_status,
            None,
        )
        logger.info(f"Mapping {qualification_status} to {value=}")
        return value

    @staticmethod
    def get_hubspot_animal_type(animal: str) -> str | None:
        ANIMAL_TYPE_MAPPING = {
            "CATTLE": "Cattle",
            "SHEEP": "Sheep",
            "GOAT": "Goat",
        }
        value = ANIMAL_TYPE_MAPPING.get(
            animal,
            None,
        )
        logger.info(f"Mapping {animal} to {value=}")
        return value

    def filter_company_properties(self, properties: dict) -> dict:
        if self.is_all_updates_allowed():
            return properties
        try:
            logger.info(f"Fetching hubspot company {self.billy_customer.hubspot_id} to compare properties")
            company = self.hubspot_client.get_company(self.billy_customer.hubspot_id)
        except CompanyApiException as e:
            if e.status == 404:
                # If we get 404 it must be a newly created company that has not become available in the api yet,
                # if that is the case we assume that it is safe to update all properties.
                # If the company actually does not exist the update call will fail later.
                logger.info(f"Company {self.billy_customer.hubspot_id} not found. Updating all properties.")
                return properties
            raise
        logger.info("Only updating empty company properties")
        return {k: v for k, v in properties.items() if not getattr(company.properties, k)}

    def filter_contact_properties(self, properties: dict) -> dict:
        if self.is_all_updates_allowed():
            return properties
        try:
            logger.info(f"Fetching hubspot contact {self.hubspot_primary_contact_id} to compare properties")
            contact = self.hubspot_client.get_contact(self.hubspot_primary_contact_id)
        except ContactApiException as e:
            if e.status == 404:
                # If we get 404 it must be a newly created contact that has not become available in the api yet,
                # if that is the case we assume that it is safe to update all properties.
                # If the contact actually does not exist the update call will fail later.
                logger.info(f"Contact {self.hubspot_primary_contact_id} not found. Updating all properties.")
                return properties
            raise
        logger.info("Only updating empty contact properties")
        return {k: v for k, v in properties.items() if not getattr(contact.properties, k)}

    def is_all_updates_allowed(self):
        # If the status is different we only update changed properties, this is the same behavior as the previous
        # quick quote sync
        return self.billy_customer.customer_status in [Customer.Status.PROSPECT, Customer.Status.UNQUALIFIED_CUSTOMER]
