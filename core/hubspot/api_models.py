"""
Data classes that directly matches the data returned from the HubSpot apis
"""

import datetime

from typing import List, Optional

from marshmallow import fields
from marshmallow_dataclass import NewType
from marshmallow.decorators import post_load, pre_load
from dataclasses import dataclass, field
from decimal import Decimal


class HubSpotBooleanField(fields.Bool):
    """Boolean field that handles blank values from hubspot"""

    def _deserialize(self, value, attr, obj, **kwargs):
        if value is None or value == "":
            return False
        return super()._deserialize(value, attr, obj, **kwargs)


class HubSpotDateTimeField(fields.DateTime):
    """Existing datetime object from hubspot"""

    def _deserialize(self, value, attr, obj, **kwargs):
        return value


HubSpotBoolean = NewType(
    "HubSpotBoolean",
    bool,
    field=HubSpotBooleanField,
)

HubSpotDateTime = NewType(
    "HubSpotDateTime",
    datetime.datetime,
    field=HubSpotDateTimeField,
)


@dataclass
class HubSpotContact:
    id: str
    archived: bool

    @dataclass
    class Properties:
        firstname: str = None
        lastname: str = None
        email: str = None
        phone: str = None
        mobilephone: str = None
        hs_whatsapp_phone_number: str = None
        sms_consent: bool = None
        address: str = None
        company: str = None
        country_select: str = None
        zip: str = None
        jobtitle: str = None
        associatedcompanyid: Optional[str] = None
        hs_legal_basis: Optional[str] = None

    properties: Properties


@dataclass
class HubSpotCompany:
    id: str
    archived: bool

    @dataclass
    class Properties:
        delivery_city: str = None
        delivery_street_address: str = None
        delivery_zip_code: str = None
        company_status: str = None
        company_segment: str = None
        email: str = None
        phone: str = None
        post_address: str = None
        post_city: str = None
        post_street_address_2: str = None
        post_zipcode: str = None
        visma_id: str = None
        visma_prepaid_id: str = None
        visma_customer_class: str = None
        visma_primary_billing_email: str = None
        main_contact_person_email: str = None
        name: str = None
        address: str = None
        address2: str = None
        city: str = None
        country_select: str = None
        zip: str = None
        business_registration_number: str = None
        country: str = None
        description: str = None
        delivery_street_address_2: str = None
        priceplan_sheep_and_goat: str = None
        priceplan_cattle: str = None
        uk_county_select: str = None
        ireland___county_select: str = None
        uk___northern_ireland___county_select: str = None
        uk___scotland___county_select: str = None
        uk___wales___county_select: str = None
        county_select: str = None
        xero_id: str = None
        my_nofence_userid: str = None
        hubspot_owner_id: str = None
        delivery_contact_person_email: str = None
        invoice_contact_person_email: str = None
        company_segment: Optional[str] = None
        qualification: Optional[str] = None
        animal_type: Optional[str] = None
        quick_quote_created: Optional[datetime.datetime] = None
        proposal_created_date: Optional[datetime.datetime] = None
        confirmed_no_interest: str = None
        customer_gdpr: str = None
        state: Optional[str] = None
        post_state: Optional[str] = None
        delivery_state: Optional[str] = None
        company_source: Optional[str] = None
        eori_number: str = None
        flat_rate_scheme: HubSpotBoolean = field(default_factory=lambda: False, metadata=dict(allow_none=True))
        initiated_contact: Optional[str] = None

    properties: Properties


@dataclass
class HubSpotDeal:
    id: str
    archived: bool

    @dataclass
    class Properties:
        dealstage: str
        pipeline: str
        createdate: datetime.datetime = None
        dealname: str = None
        deal_currency_code: str = None
        estimated_delivery_date: datetime.date = None
        deal_source: str = None
        description: str = None
        visma_id: str = None
        visma_order_status: str = None
        hubspot_owner_id: str = None

        delivery_street_address: str = None
        delivery_street_address_2: str = None
        delivery_zip_code: str = None
        delivery_city: str = None
        delivery_country_select: str = None
        delivery_contact_person_email: str = None

        invoice_street_address: str = None
        invoice_street_address_2: str = None
        invoice_zip_code: str = None
        invoice_city: str = None
        invoice_country_select: str = None
        invoice_contact_person_email: str = None

        purchase_order: str = None

        disable_automatic_add_of_subscription: HubSpotBoolean = field(
            default_factory=lambda: False,
            metadata=dict(allow_none=True),
        )
        disable_annual_fee_sku_validation: HubSpotBoolean = field(
            default_factory=lambda: False,
            metadata=dict(allow_none=True),
        )

        billy_sales_order_id: str = None
        amount: str = None
        closed_lost_reason_2_0: str = None
        ship_date: str = None

        animal_type: Optional[str] = None

        first_customer_order_: Optional[bool] = None
        customer_country: Optional[str] = None
        discount_code: Optional[str] = None

        @pre_load
        def clean_estimated_delivery_date(self, data, **kwargs):
            # Hubspot can sometimes have the estimated_delivery_date as an empty string instead of None,
            # which will cause the marshmallow to fail to load the data
            if "estimated_delivery_date" in data and data["estimated_delivery_date"] == "":
                data["estimated_delivery_date"] = None
            return data

        @post_load
        def _post_load(self, data, **kwargs):
            # Just in case the values have None values in hubspot be set the default value to False
            # Marchmallow will load the None values as None
            _ensure_default_value(data, "disable_automatic_add_of_subscription", False)
            _ensure_default_value(data, "disable_annual_fee_sku_validation", False)
            return data

    properties: Properties


@dataclass
class HubSpotLineItem:
    id: str
    archived: bool

    @dataclass
    class Properties:
        price: str
        quantity: str
        hs_sku: str = None
        hs_line_item_currency_code: str = None
        description: str = None
        discount: str = None
        hs_discount_percentage: str = None
        visma_id: str = None
        name: str = None
        billy_sales_order_line_id: str = None

    properties: Properties


@dataclass
class HubSpotProduct:
    id: str
    archived: bool

    @dataclass
    class Properties:
        name: str = None
        price: Decimal = None
        quantity: int = None
        tax: float = None
        description: str = None
        hs_sku: str = None
        discount: str = None
        hs_createdate: datetime.datetime = None
        hs_lastmodifieddate: datetime.datetime = None
        hs_unique_creation_key: str = None
        hs_price_aud: str = None
        hs_price_cad: str = None
        hs_price_chf: str = None
        hs_price_eur: str = None
        hs_price_gbp: str = None
        hs_price_nok: str = None
        hs_price_nzd: str = None
        hs_price_usd: str = None
        hs_recurring_billing_period: str = None
        hs_url: str = None

    properties: Properties


@dataclass
class HubSpotOwner:
    # The owners api does not use the same properties structure as the other apis
    id: str = None
    email: str = None
    first_name: str = None
    last_name: str = None


@dataclass
class HubSpotNote:
    id: str
    archived: bool

    @dataclass
    class Properties:
        hs_body_preview: str = None
        hs_body_preview_is_truncated: bool = None
        hs_created_by: int = None
        hs_createdate: datetime.datetime = None
        hs_engagement_source: str = None
        hs_note_body: str = None
        hs_object_id: int = None
        hs_product_name: str = None

    properties: Properties


@dataclass
class HubSpotTask:
    id: str
    archived: bool

    @dataclass
    class Properties:
        hs_body_preview: str = None
        hs_body_preview_is_truncated: bool = None
        hs_created_by: int = None
        hs_createdate: datetime.datetime = None
        hs_lastmodifieddate: datetime.datetime = None
        hs_engagement_source: str = None
        hs_num_associated_tickets: int = None
        hs_note_body: str = None
        hs_object_id: int = None
        hs_product_name: str = None
        hs_gdpr_deleted: bool = None
        hs_scheduled_tasks: str = None
        hs_task_body: str = None
        hs_task_completion_date: str = None
        hs_task_contact_timezone: str = None
        hs_task_for_object_type: str = None
        hs_task_priority: str = None
        hs_task_probability_to_complete: str = None
        hs_task_status: str = None
        hs_task_subject: str = None
        hs_task_type: str = None
        hs_user_ids_of_all_owners: str = None
        hubspot_owner_id: str = None
        hubspot_team_id: str = None
        hs_body_preview_html: str = None

    properties: Properties

    # Used in the initial load of all tasks
    associations: dict = None


@dataclass
class HubSpotTicket:
    id: str
    archived: bool

    @dataclass
    class Properties:
        animal_type: str = None
        animal_welfare: str = None
        app: str = None
        c2: str = None
        c2_1: str = None
        c2_2: str = None
        c2_b2_1_20ah: str = None
        c2_b2_1_battery_20ah: str = None
        c2chgr_ps1_bd2: str = None
        c_ns2_neckstrap_gn_32cm: str = None
        calf_pilot_2022: str = None
        career_job_postings: str = None
        closed_date: str = None
        company_status_at_ticket_create_date: str = None
        created_by: str = None
        createdate: datetime.datetime = None
        customer_administration: str = None
        department: str = None
        escalation: str = None
        finance: str = None
        hs_createdate: str = None
        hs_custom_inbox: str = None
        hs_last_email_activity: str = None
        hs_last_email_date: str = None
        hs_last_message_received_at: str = None
        hs_last_message_sent_at: str = None
        hs_lastactivitydate: str = None
        hs_lastcontacted: str = None
        hs_lastmodifieddate: datetime.datetime = None
        hs_latest_message_seen_by_agent_ids: str = None
        hs_nextactivitydate: str = None
        hs_num_associated_companies: str = None
        hs_num_times_contacted: str = None
        hs_object_id: str = None
        hs_pipeline: str = None
        hs_pipeline_stage: str = None
        hs_read_only: str = None
        hs_resolution: str = None
        hs_ticket_category: str = None
        hs_ticket_id: str = None
        hs_ticket_priority: str = None
        hs_time_to_close_sla_at: str = None
        hs_time_to_close_sla_status: str = None
        hs_time_to_first_response_sla_at: str = None
        hs_time_to_first_response_sla_status: str = None
        hs_unique_creation_key: str = None
        hs_updated_by_user_id: str = None
        incident: str = None
        information: str = None
        last_engagement_date: str = None
        last_reply_date: str = None
        logistics: str = None
        n19battery: str = None
        n20battery_sheep: str = None
        nps_follow_up_answer: str = None
        nps_follow_up_question_version: int = None
        nps_score: str = None
        onboarding: str = None
        product___app: str = None
        return_order_number: str = None
        sales: str = None
        serial_numbers: str = None
        sg1: str = None
        sg2: str = None
        sg2_09: str = None
        sg2_1: str = None
        sg2_2: str = None
        sg2chgr_ps1_bd2: str = None
        sg_ns2_neckstrap_gn_16cm: str = None
        shelter_beacon_sb1: str = None
        si_jira_issue_assignee: str = None
        si_jira_issue_id: str = None
        si_jira_issue_key: str = None
        si_jira_issue_link: str = None
        si_jira_issue_priority: str = None
        si_jira_issue_reporter: str = None
        si_jira_issue_status: str = None
        si_jira_issue_summary: str = None
        si_jira_sync_notes: str = None
        sub_category: str = None
        sub_owner: str = None
        ticket_category_and_label_counter: str = None
        ticket_category_counter: str = None
        time_from_first_contact_to_closed: str = None
        time_to_close: str = None
        time_to_first_agent_reply: str = None
        subject: str = None
        content: str = None
        source_type: str = None
        source_ref: str = None
        tags: str = None
        hs_sales_email_last_replied: str = None
        hubspot_owner_id: str = None
        notes_last_contacted: str = None
        notes_last_updated: str = None
        notes_next_activity_date: str = None
        num_contacted_notes: str = None
        num_notes: str = None

    properties: Properties

    # Used in the initial load of all tickets
    associations: dict = None


@dataclass
class HubSpotEmail:
    id: str
    archived: bool

    @dataclass
    class Properties:
        hs_body_preview: str = None
        hs_body_preview_is_truncated: bool = None
        hs_created_by: int = None
        hs_createdate: datetime.datetime = None
        hs_engagement_source: str = None
        hs_note_body: str = None
        hs_object_id: int = None
        hs_product_name: str = None
        hs_email_bcc_email: str = None
        hs_email_bcc_firstname: str = None
        hs_email_bcc_lastname: str = None
        hs_email_bcc_raw: str = None
        hs_email_bounce_error_detail_message: str = None
        hs_email_cc_email: str = None
        hs_email_cc_firstname: str = None
        hs_email_cc_lastname: str = None
        hs_email_cc_raw: str = None
        hs_email_error_message: str = None
        hs_email_from_email: str = None
        hs_email_from_firstname: str = None
        hs_email_from_lastname: str = None
        hs_email_from_raw: str = None
        hs_email_logged_from: str = None
        # hs_email_html: str = None
        hs_email_message_id: str = None
        hs_email_sender_email: str = None
        hs_email_sender_firstname: str = None
        hs_email_sender_firstname: str = None
        hs_email_sender_firstname: str = None
        hs_email_sent_via: str = None
        hs_email_status: str = None
        hs_email_subject: str = None
        # hs_email_text: str = None
        hs_email_thread_id: str = None
        hs_email_to_email: str = None
        hs_email_to_firstname: str = None
        hs_email_to_firstname: str = None
        hs_email_to_raw: str = None
        hs_email_tracker_key: str = None
        hs_engagement_source: str = None

    properties: Properties


@dataclass
class HubSpotPipelineStage:
    id: str
    archived: bool
    label: str
    display_order: int
    created_at: HubSpotDateTime
    updated_at: HubSpotDateTime
    metadata: dict


@dataclass
class HubSpotPipeline:
    id: str
    archived: bool
    label: str
    display_order: int
    created_at: HubSpotDateTime
    updated_at: HubSpotDateTime
    stages: List[HubSpotPipelineStage]


def _ensure_default_value(data, property, default_value):
    """Sets the property key in data to the default value if missing or None"""
    if property not in data or data[property] is None:
        data[property] = default_value
