import logging
import dataclasses
import functools
import datetime

from typing import List, Optional

from hubspot import HubSpot
from hubspot.crm.deals import (
    SimplePublicObjectInput as DealInputObject,
    PublicObjectSearchRequest as DealSearchRequest,
)
from hubspot.crm.line_items import (
    SimplePublicObjectInput as LineItemInputObject,
    PublicObjectSearchRequest as LineItemSearchRequest,
)
from hubspot.crm.contacts import (
    PublicObjectSearchRequest as ContactSearchRequest,
)
from hubspot.crm.tickets import PublicObjectSearchRequest as TicketSearchRequest
from hubspot.crm.tickets import SimplePublicObjectInput as TicketInputObject
from hubspot.crm.objects.tasks import PublicObjectSearchRequest as TaskSearchRequest

from hubspot.marketing.transactional import PublicSingleSendEmail, PublicSingleSendRequestEgg
from hubspot.crm.companies import SimplePublicObjectInput as CompanyInputObject
from hubspot.crm.contacts import SimplePublicObjectInput as ContactInputObject
from urllib3.util import Retry
from django.core.cache import cache

from .api_models import (
    HubSpotCompany,
    HubSpotContact,
    HubSpotDeal,
    HubSpotLineItem,
    HubSpotOwner,
    HubSpotPipeline,
    HubSpotProduct,
    HubSpotTicket,
    HubSpotTask,
    HubSpotEmail,
    HubSpotNote,
)
from .api_schema import (
    HubSpotCompanySchema,
    HubSpotContactSchema,
    HubSpotDealSchema,
    HubSpotLineItemSchema,
    HubSpotOwnerSchema,
    HubSpotProductSchema,
    HubSpotTicketSchema,
    HubSpotEmailSchema,
    HubSpotNoteSchema,
    HubSpotTaskSchema,
    HubSpotPipelineSchema,
)

logger = logging.getLogger(__name__)


def access_token(f):
    """Decorator for the HubSpotClient that handles refresh of the access_token.

    It catches 401 errors and tries to refresh the access token, before
    retrying the method original method call.
    """

    @functools.wraps(f)
    def _handle_auth(self: "HubSpotClient", *args, **kwargs):
        try:
            return f(self, *args, **kwargs)
        except Exception as e:
            # We need to check the exception name because the hubspot
            # library uses different exeption with the same name for
            # every object type (deal, contact, company, etc.)
            if e.__class__.__name__ == "ApiException":
                if getattr(e, "status") == 401:
                    # Refresh token and try again
                    self.refresh_access_token()
                    return f(self, *args, **kwargs)
            raise e

    return _handle_auth


class HubSpotClient:
    def __init__(
        self,
        client_id=None,
        client_secret=None,
        refresh_token=None,
        redirect_uri=None,
        client_obj: HubSpot = None,
    ):
        self.redirect_uri = redirect_uri
        self.client_id = client_id
        self.client_secret = client_secret
        self.refresh_token = refresh_token

        retry = Retry(
            total=10,
            backoff_factor=0.3,
            status_forcelist=(500, 502, 503, 504, 429, 413),
        )

        self.client = client_obj or HubSpot(retry=retry)
        # if access_token is set from client_obj, we don't need to load it from cache.
        if not self.client.access_token:
            self.client.access_token = self._load_access_token()

    @access_token
    def get_deal(self, deal_id: str) -> HubSpotDeal:
        result = self.client.crm.deals.basic_api.get_by_id(
            deal_id=deal_id,
            properties=self._properties_for_model(HubSpotDeal.Properties),
        )
        return HubSpotDealSchema().load(result.to_dict())

    @access_token
    def create_deal(self, properties: HubSpotDeal.Properties) -> HubSpotDeal:
        logger.info(f"Creating new deal with properties: {properties}")
        result = self.client.crm.deals.basic_api.create(
            DealInputObject(properties=dataclasses.asdict(properties)),
        )
        return HubSpotDealSchema().load(result.to_dict())

    @access_token
    def create_deal_with_association(
        self,
        properties: HubSpotDeal.Properties,
        to_company_id: str,
        to_contact_id: str,
        **kwargs,
    ) -> HubSpotDeal:
        deal = self.create_deal(properties)
        self.client.crm.deals.associations_api.create(
            deal.id,
            to_object_type="company",
            association_type="deal_to_company",
            to_object_id=to_company_id,
            **kwargs,
        )
        self.client.crm.deals.associations_api.create(
            deal.id,
            to_object_type="contact",
            association_type="deal_to_contact",
            to_object_id=to_contact_id,
            **kwargs,
        )
        return deal

    @access_token
    def assosciate_company_to_contact(
        self,
        company_id: str,
        to_contact_id: str,
        **kwargs,
    ):
        self.client.crm.companies.associations_api.create(
            company_id,
            to_object_type="contact",
            association_type="company_to_contact",
            to_object_id=to_contact_id,
            **kwargs,
        )

    @access_token
    def get_contact(self, contact_id: str) -> HubSpotContact:
        result = self.client.crm.contacts.basic_api.get_by_id(
            contact_id=contact_id,
            properties=self._properties_for_model(HubSpotContact.Properties),
        )
        return HubSpotContactSchema().load(result.to_dict())

    @access_token
    def get_contact_by_email(self, email: str) -> Optional[HubSpotContact]:
        result = self.client.crm.contacts.search_api.do_search(
            ContactSearchRequest(
                # The email can be defined as either the primary email or as an additional email, we need to check both.
                filter_groups=[
                    {
                        "filters": [
                            {"propertyName": "email", "value": email, "operator": "EQ"},
                        ],
                    },
                    {
                        "filters": [
                            {"propertyName": "hs_additional_emails", "value": email, "operator": "EQ"},
                        ],
                    },
                ],
                properties=self._properties_for_model(HubSpotContact.Properties),
            ),
        )
        return HubSpotContactSchema().load(result.to_dict()["results"][0]) if result.to_dict()["total"] != 0 else None

    @access_token
    def get_company(self, company_id: str) -> HubSpotCompany:
        result = self.client.crm.companies.basic_api.get_by_id(
            company_id=company_id,
            properties=self._properties_for_model(HubSpotCompany.Properties),
        )
        return HubSpotCompanySchema().load(result.to_dict())

    @access_token
    def get_companies_batch(self, company_ids: List[str]) -> List[HubSpotCompany]:
        """
        Get multiple companies by their IDs in a single batch request.

        Args:
            company_ids: List of company IDs to retrieve

        Returns:
            List of HubSpotCompany objects
        """
        from hubspot.crm.companies import BatchInputSimplePublicObjectId

        # Create batch input with the company IDs and properties
        batch_input = BatchInputSimplePublicObjectId(
            inputs=[{"id": company_id} for company_id in company_ids], properties=["company_segment"]
        )

        # Make the batch API call
        result = self.client.crm.companies.batch_api.read(batch_read_input_simple_public_object_id=batch_input)

        # Extract the results and convert to our schema
        companies_data = result.to_dict()
        if "results" in companies_data:
            return HubSpotCompanySchema(many=True).load(companies_data["results"])
        else:
            return []

    @access_token
    def get_line_item(self, line_item_id: str) -> HubSpotLineItem:
        result = self.client.crm.line_items.basic_api.get_by_id(
            line_item_id=line_item_id,
            properties=self._properties_for_model(HubSpotLineItem.Properties),
            # associations=["deal"], ##  breaks the test I can't be arsed to fix them
        )
        return HubSpotLineItemSchema().load(result.to_dict())

    @access_token
    def archive_line_item(self, line_item_id: str):
        return self.client.crm.line_items.basic_api.archive(line_item_id=line_item_id)

    @access_token
    def get_product(self, product_id: str) -> HubSpotProduct:
        result = self.client.crm.products.basic_api.get_by_id(
            product_id=product_id,
            properties=self._properties_for_model(HubSpotProduct.Properties),
        )
        return HubSpotProductSchema().load(result.to_dict())

    @access_token
    def get_all_products(self) -> List[HubSpotProduct]:
        result = self.client.crm.products.get_all(
            properties=self._properties_for_model(HubSpotProduct.Properties),
        )

        return HubSpotProductSchema(many=True).load(map(lambda it: it.to_dict(), result))

    @access_token
    def get_companies_for_ticket(self, ticket_id: str) -> List[HubSpotCompany]:
        items = []
        for company_id in self.get_company_ids_for_ticket(ticket_id):
            items.append(self.get_company(company_id))
        return items

    @access_token
    def get_company_ids_for_ticket(self, ticket_id: str) -> List[str]:
        items = []
        for assoc in self.client.crm.tickets.associations_api.get_all(ticket_id, "company").results:
            items.append(assoc.id)
        return items

    @access_token
    def get_owner(self, owner_id: str) -> HubSpotOwner:
        result = self.client.crm.owners.owners_api.get_by_id(
            owner_id=owner_id,
        )
        return HubSpotOwnerSchema().load(result.to_dict())

    def find_tickets_by_time(
        self,
        time_property: str,
        time_threshold: datetime.datetime,
        limit: int = 100,
        max_items: int = None,
    ) -> List[HubSpotTicket]:
        """Returns tickets where the time property changed on or after the time threshold"""
        count = 0
        after = None
        while max_items is None or count < max_items:
            items, paging = self._find_tickets_by_time(
                sorts=[time_property],
                limit=limit,
                after=after,
                filter_groups=[
                    {
                        "filters": [
                            {
                                "propertyName": time_property,
                                "value": int(time_threshold.timestamp()) * 1000,
                                "operator": "GTE",
                            },
                        ],
                    },
                ],
                properties=self._properties_for_model(HubSpotTicket.Properties),
            )

            yield from items

            count += len(items)

            if paging is None:
                break
            after = paging.next.after

    @access_token
    def _find_tickets_by_time(self, *args, **kwargs):
        """
        This extra method is needed because the access_token decorator does not work with generator
        functions.
        """
        result = self.client.crm.tickets.search_api.do_search(
            TicketSearchRequest(
                *args,
                **kwargs,
            ),
        )

        return HubSpotTicketSchema(many=True).load(map(lambda it: it.to_dict(), result.results)), result.paging

    @access_token
    def load_all_ticket(self, limit: int = 100, max_items: int = None) -> List[HubSpotTicket]:
        """Returns tickets where the time property changed on or after the time threshold"""
        count = 0
        after = None
        while max_items is None or count < max_items:
            items, paging = self._load_all_tickets(
                limit=limit,
                after=after,
                associations=["company"],
                properties=self._properties_for_model(HubSpotTicket.Properties),
            )

            yield from items

            count += len(items)

            if paging is None:
                break

            after = paging.next.after

    @access_token
    def _load_all_tickets(self, *args, **kwargs):
        """
        This extra method is needed because the access_token decorator does not work with generator
        functions.
        """
        result = self.client.crm.tickets.basic_api.get_page(
            *args,
            **kwargs,
        )

        return HubSpotTicketSchema(many=True).load(map(lambda it: it.to_dict(), result.results)), result.paging

    @access_token
    def load_all_tasks(self, limit: int = 100, max_items: int = None) -> List[HubSpotTask]:
        count = 0
        after = None
        while max_items is None or count < max_items:
            items, paging = self._load_all_tasks(
                limit=limit,
                after=after,
                associations=["ticket"],
                properties=self._properties_for_model(HubSpotTask.Properties),
            )

            yield from items

            count += len(items)

            if paging is None:
                break

            after = paging.next.after

    @access_token
    def _load_all_tasks(self, *args, **kwargs):
        """
        This extra method is needed because the access_token decorator does not work with generator
        functions.
        """
        result = self.client.crm.objects.tasks.basic_api.get_page(
            *args,
            **kwargs,
        )

        return HubSpotTaskSchema(many=True).load(map(lambda it: it.to_dict(), result.results)), result.paging

    @access_token
    def find_tasks_by_time(
        self,
        time_property: str,
        time_threshold: datetime.datetime,
        limit: int = 100,
        max_items: int = None,
    ) -> List[HubSpotTask]:
        count = 0
        after = None
        while max_items is None or count < max_items:
            items, paging = self._find_task_by_time(
                sorts=["hs_createdate"],
                limit=limit,
                after=after,
                filter_groups=[
                    {
                        "filters": [
                            {
                                "propertyName": time_property,
                                "value": int(time_threshold.timestamp()) * 1000,
                                "operator": "GTE",
                            },
                        ],
                    },
                ],
                properties=self._properties_for_model(HubSpotTask.Properties),
            )

            yield from items

            count += len(items)

            if paging is None:
                break

            after = paging.next.after

    @access_token
    def _find_task_by_time(self, *args, **kwargs):
        """
        This extra method is needed because the access_token decorator does not work with generator
        functions.
        """
        result = self.client.crm.objects.tasks.search_api.do_search(
            TaskSearchRequest(
                *args,
                **kwargs,
            ),
        )

        return HubSpotTaskSchema(many=True).load(map(lambda it: it.to_dict(), result.results)), result.paging

    @access_token
    def create_ticket(self, properties: HubSpotTicket.Properties) -> HubSpotTicket:
        logger.info(f"Creating ticket with properties {properties}")
        resp = self.client.crm.tickets.basic_api.create(TicketInputObject(properties=dataclasses.asdict(properties)))
        return HubSpotTicketSchema().load(resp.to_dict())

    @access_token
    def create_ticket_with_association(
        self,
        properties: HubSpotTicket.Properties,
        to_company_id: str,
        contact_email: str,
    ) -> HubSpotTicket:
        ticket = self.create_ticket(properties)
        self.client.crm.tickets.associations_api.create(
            ticket.id,
            to_object_type="company",
            association_type="ticket_to_company",
            to_object_id=to_company_id,
        )
        contact = self.get_contact_by_email(contact_email)
        if not contact:
            contact = self.create_contact(email=contact_email, associatedcompanyid=to_company_id)
        if contact:
            self.client.crm.tickets.associations_api.create(
                ticket.id,
                to_object_type="contact",
                association_type="ticket_to_contact",
                to_object_id=contact.id,
            )
        return ticket

    @access_token
    def get_ticket(self, ticket_id: str) -> HubSpotTicket:
        result = self.client.crm.tickets.basic_api.get_by_id(
            ticket_id=ticket_id,
            associations=["company"],
            properties=self._properties_for_model(HubSpotTicket.Properties),
        )
        return HubSpotTicketSchema().load(result.to_dict())

    @access_token
    def get_communication_preferences(self, email: str):
        results = self.client.communication_preferences.status_api.get_email_status(email_address=email)
        return results.subscription_statuses

    @access_token
    def get_email(self, email_id: str) -> HubSpotEmail:
        result = self.client.crm.objects.emails.basic_api.get_by_id(
            email_id=email_id,
            properties=self._properties_for_model(HubSpotEmail.Properties),
        )
        return HubSpotEmailSchema().load(result.to_dict())

    @access_token
    def get_note(self, note_id: str) -> HubSpotNote:
        result = self.client.crm.objects.notes.basic_api.get_by_id(
            note_id=note_id,
            properties=self._properties_for_model(HubSpotNote.Properties),
        )
        return HubSpotNoteSchema().load(result.to_dict())

    @access_token
    def get_task(self, task_id: str) -> HubSpotTask:
        result = self.client.crm.objects.tasks.basic_api.get_by_id(
            task_id=task_id,
            properties=self._properties_for_model(HubSpotTask.Properties),
        )
        return HubSpotTaskSchema().load(result.to_dict())

    @access_token
    def get_tasks_for_ticket(self, ticket: HubSpotTicket) -> List[HubSpotTask]:
        items = []
        for assoc in self.client.crm.tickets.associations_api.get_all(ticket.id, "task").results:
            items.append(self.get_task(assoc.id))
        return items

    @access_token
    def get_notes_for_ticket(self, ticket: HubSpotTicket) -> List[HubSpotNote]:
        items = []
        for assoc in self.client.crm.tickets.associations_api.get_all(ticket.id, "note").results:
            items.append(self.get_note(assoc.id))
        return items

    @access_token
    def get_emails_for_ticket(self, ticket: HubSpotTicket) -> List[HubSpotEmail]:
        items = []
        for assoc in self.client.crm.tickets.associations_api.get_all(ticket.id, "email").results:
            items.append(self.get_email(assoc.id))
        return items

    @access_token
    def get_contacts_for_deal(self, deal: HubSpotDeal) -> List[HubSpotContact]:
        items = []
        for assoc in self.client.crm.deals.associations_api.get_all(deal.id, "contact").results:
            items.append(self.get_contact(assoc.id))
        return items

    @access_token
    def get_contacts_for_company(self, company: HubSpotCompany) -> List[HubSpotContact]:
        items = []
        for assoc in self.client.crm.companies.associations_api.get_all(company.id, "contact").results:
            items.append(self.get_contact(assoc.id))
        return items

    @access_token
    def get_deals_for_company_id(self, company_id: str) -> List[HubSpotDeal]:
        items = []
        for assoc in self.client.crm.companies.associations_api.get_all(company_id, "deal").results:
            items.append(self.get_deal(assoc.id))
        return items

    @access_token
    def get_tickets_for_company(self, company: HubSpotCompany) -> List[HubSpotTicket]:
        items = []
        for assoc in self.client.crm.companies.associations_api.get_all(company.id, "ticket").results:
            items.append(self.get_ticket(assoc.id))
        return items

    @access_token
    def get_ticket_ids_for_task(self, task_id: str) -> List[str]:
        items = []
        for assoc in self.client.crm.objects.tasks.associations_api.get_all(task_id, "ticket").results:
            items.append(assoc.id)
        return items

    @access_token
    def get_ticket_ids_for_company(self, company_id: str) -> List[str]:
        """Optimization to fetch only ids when we don't need the full objects"""
        items = []
        for assoc in self.client.crm.companies.associations_api.get_all(company_id, "ticket").results:
            items.append(assoc.id)
        return items

    @access_token
    def get_companies_for_deal(self, deal: HubSpotDeal) -> List[HubSpotCompany]:
        items = []
        for assoc in self.client.crm.deals.associations_api.get_all(deal.id, "company").results:
            items.append(self.get_company(assoc.id))
        return items

    @access_token
    def get_company_ids_for_deal(self, deal_id: str) -> List[str]:
        items = []
        for assoc in self.client.crm.deals.associations_api.get_all(deal_id, "company").results:
            items.append(assoc.id)
        return items

    @access_token
    def get_contact_ids_for_deal(self, deal_id: str) -> List[str]:
        items = []
        for assoc in self.client.crm.deals.associations_api.get_all(deal_id, "contact").results:
            items.append(assoc.id)
        return items

    @access_token
    def get_line_items_for_deal(self, deal: HubSpotDeal) -> List[HubSpotLineItem]:
        items = []
        for assoc in self.client.crm.deals.associations_api.get_all(deal.id, "line_item").results:
            items.append(self.get_line_item(assoc.id))
        return items

    @access_token
    def update_deal_visma_properties(self, deal_id: str, visma_id: str, visma_order_status: str):
        self.update_deal_properties(deal_id, visma_id=visma_id, visma_order_status=visma_order_status)

    @access_token
    def update_deal_visma_order_status(self, deal_id: str, visma_order_status: str):
        self.update_deal_properties(deal_id, visma_order_status=visma_order_status)

    @access_token
    def update_deal_ship_date(self, deal_id: str, ship_date: datetime.date):
        self.update_deal_properties(deal_id, ship_date=ship_date.isoformat())

    @access_token
    def update_deal_has_been_delivered(self, deal_id: str, order_status: str, ship_date: datetime.date):
        self.update_deal_properties(deal_id, visma_order_status=order_status, ship_date=ship_date.isoformat())

    @access_token
    def update_deal_stage(self, deal_id: str, deal_stage: str):
        self.update_deal_properties(deal_id, dealstage=deal_stage)

    @access_token
    def update_deal_properties(self, deal_id: str, **properties):
        logger.info(f"Updating deal {deal_id} with properties {properties}", extra={"properties": properties})
        return self.client.crm.deals.basic_api.update(deal_id, DealInputObject(properties=properties))

    @access_token
    def update_line_item_properties(self, line_id: str, **properties):
        logger.info(f"Updating line_item {line_id} with properties {properties}", extra={"properties": properties})
        return self.client.crm.line_items.basic_api.update(line_id, LineItemInputObject(properties=properties))

    @access_token
    def create_line_item_properties(self, deal_id: str, **properties):
        logger.info(
            f"Creating line_item for deal {deal_id} with properties {properties}",
            extra={"properties": properties},
        )
        line_item = self.client.crm.line_items.basic_api.create(LineItemInputObject(properties=properties))

        self.client.crm.line_items.associations_api.create(
            line_item_id=line_item.id,
            to_object_type="deal",
            to_object_id=deal_id,
            association_type="line_item_to_deal",
        )

        return line_item

    @access_token
    def update_deal(self, deal_id: str, deal_props: HubSpotDeal.Properties) -> HubSpotDeal:
        response = self.update_deal_properties(deal_id, **dataclasses.asdict(deal_props))
        return HubSpotDealSchema().load(response.to_dict())

    @access_token
    def create_deal_line_item(self, deal_id: str, line_item_props: HubSpotLineItem.Properties) -> HubSpotLineItem:
        response = self.create_line_item_properties(deal_id, **dataclasses.asdict(line_item_props))
        return HubSpotLineItemSchema().load(response.to_dict())

    @access_token
    def update_line_item(self, line_item_id: str, line_item_props: HubSpotLineItem.Properties) -> HubSpotLineItem:
        response = self.update_line_item_properties(line_item_id, **dataclasses.asdict(line_item_props))
        return HubSpotLineItemSchema().load(response.to_dict())

    @access_token
    def link_deal_to_company(self, deal_id: str, company_id: str):
        self.client.crm.deals.associations_api.create(
            deal_id=deal_id,
            to_object_type="company",
            to_object_id=company_id,
            association_type="deal_to_company",
        )

    @access_token
    def link_deal_to_contact(self, deal_id: str, contact_id: str):
        self.client.crm.deals.associations_api.create(
            deal_id=deal_id,
            to_object_type="contact",
            to_object_id=contact_id,
            association_type="deal_to_contact",
        )

    @access_token
    def unlink_deal_from_company(self, deal_id: str, company_id: str):
        self.client.crm.deals.associations_api.archive(
            deal_id=deal_id,
            to_object_type="company",
            to_object_id=company_id,
            association_type="deal_to_company",
        )

    @access_token
    def unlink_deal_from_contact(self, deal_id: str, contact_id: str):
        self.client.crm.deals.associations_api.archive(
            deal_id=deal_id,
            to_object_type="contact",
            to_object_id=contact_id,
            association_type="deal_to_contact",
        )

    @access_token
    def update_company_visma_id(self, company_id: str, visma_id: str):
        self.update_company_properties(company_id, visma_id=visma_id)

    @access_token
    def update_company_visma_prepaid_id(self, company_id: str, visma_prepaid_id: str):
        self.update_company_properties(company_id, visma_prepaid_id=visma_prepaid_id)

    @access_token
    def update_company_xero_id(self, company_id: str, xero_id: str):
        self.update_company_properties(company_id, xero_id=xero_id)

    @access_token
    def update_company_status(self, company_id: str, status: str):
        self.update_company_properties(company_id, company_status=status)

    @access_token
    def update_company_and_primary_contact_email(self, company_id: str, new_email: str, old_email: str):
        logger.info(
            f"Updating email for company {company_id} and primary contact from old email {old_email} to new "
            f"email {new_email}",
        )
        company = self.get_company(company_id)
        self.update_company_properties(
            company_id,
            main_contact_person_email=new_email,
            invoice_contact_person_email=(
                new_email
                if company.properties.invoice_contact_person_email == old_email
                else company.properties.invoice_contact_person_email
            ),
            delivery_contact_person_email=(
                new_email
                if company.properties.delivery_contact_person_email == old_email
                else company.properties.delivery_contact_person_email
            ),
        )
        contacts = self.get_contacts_for_company(company)
        contact_found = False
        for contact in contacts:
            if contact.properties.email and old_email:
                if contact.properties.email.lower() == old_email.lower():
                    contact_found = True
                    self.update_contact_properties(contact_id=contact.id, email=new_email)
        if not contact_found:
            self.create_contact(email=new_email, associatedcompanyid=company_id)

    @access_token
    def update_properties_for_primary_contact_by_company(self, company_id: str, **properties) -> Optional[str]:
        logger.info(f"Updating properties for primary contact for company {company_id}")
        company = self.get_company(company_id)
        contacts = self.get_contacts_for_company(company)
        contact_found = False
        for contact in contacts:
            if contact.properties.email and company.properties.main_contact_person_email:
                if contact.properties.email.lower() == company.properties.main_contact_person_email.lower():
                    contact_found = True
                    self.update_contact_properties(contact_id=contact.id, **properties)
                    return contact.id
        if not contact_found:
            logger.error(f"Could not find primary contact for company {company_id}")

    @access_token
    def update_company_properties(self, company_id: str, **properties):
        logger.info(f"Updating company {company_id} with properties {properties}", extra={"properties": properties})
        self.client.crm.companies.basic_api.update(company_id, CompanyInputObject(properties=properties))

    @access_token
    def update_company_my_nofence_user_id(self, company_id: str, user_no: str):
        logger.info(f"Updating company {company_id} with my_nofence_userid {user_no}")
        self.client.crm.companies.basic_api.update(
            company_id,
            CompanyInputObject(
                properties={
                    "my_nofence_userid": user_no,
                },
            ),
        )

    @access_token
    def update_contact_properties(self, contact_id: str, **properties):
        logger.info(f"Updating contact {contact_id} with properties {properties}", extra={"properties": properties})
        self.client.crm.contacts.basic_api.update(contact_id, ContactInputObject(properties=properties))

    @access_token
    def update_contact_properties_with_response(self, contact_id: str, **properties) -> HubSpotContact:
        logger.info(f"Updating contact {contact_id} with properties {properties}", extra={"properties": properties})
        resp = self.client.crm.contacts.basic_api.update(contact_id, ContactInputObject(properties=properties))
        return HubSpotContactSchema().load(resp.to_dict())

    @access_token
    def create_contact(self, **properties) -> HubSpotContact:
        logger.info(f"Creating contact with properties {properties}", extra={"properties": properties})
        resp = self.client.crm.contacts.basic_api.create(ContactInputObject(properties=properties))
        return HubSpotContactSchema().load(resp.to_dict())

    @access_token
    def set_communication_preferences_for_contact(self, email: str):
        logger.info(f"Setting communication preferences for contact with email {email}", extra={"email": email})
        try:
            self.client.communication_preferences.status_api.subscribe(
                public_update_subscription_status_request={
                    "emailAddress": email,
                    "subscriptionId": "7472995",
                    "legalBasis": "LEGITIMATE_INTEREST_PQL",
                    "legalBasisExplanation": "This customer has agreed through quick quote",
                },
            )
        except Exception as e:
            logger.warning(e)
        try:
            self.client.communication_preferences.status_api.subscribe(
                public_update_subscription_status_request={
                    "emailAddress": email,
                    "subscriptionId": "9181246",
                    "legalBasis": "LEGITIMATE_INTEREST_PQL",
                    "legalBasisExplanation": "This customer has agreed through quick quote",
                },
            )
        except Exception as e:
            logger.warning(e)
        try:
            self.client.communication_preferences.status_api.subscribe(
                public_update_subscription_status_request={
                    "emailAddress": email,
                    "subscriptionId": "8355683",
                    "legalBasis": "LEGITIMATE_INTEREST_PQL",
                    "legalBasisExplanation": "This customer has agreed through quick quote",
                },
            )
        except Exception as e:
            logger.warning(e)
        try:
            self.client.communication_preferences.status_api.subscribe(
                public_update_subscription_status_request={
                    "emailAddress": email,
                    "subscriptionId": "8667257",
                    "legalBasis": "LEGITIMATE_INTEREST_PQL",
                    "legalBasisExplanation": "This customer has agreed through quick quote",
                },
            )
        except Exception as e:
            logger.warning(e)

    @access_token
    def create_company(self, **properties) -> HubSpotCompany:
        logger.info(f"Creating company with properties {properties}", extra={"properties": properties})
        resp = self.client.crm.companies.basic_api.create(CompanyInputObject(properties=properties))
        return HubSpotCompanySchema().load(resp.to_dict())

    @access_token
    def create_property(self, object_type, **properties):
        logger.info(f"Creating property for {object_type}", extra={"properties": properties})
        self.client.crm.properties.core_api.create(
            object_type=object_type,
            property_create=properties,
        )

    @access_token
    def find_deals_with_visma_id(self, visma_id: str) -> List[HubSpotDeal]:
        result = self.client.crm.deals.search_api.do_search(
            DealSearchRequest(
                filter_groups=[
                    {
                        "filters": [
                            {"propertyName": "visma_id", "value": visma_id, "operator": "EQ"},
                        ],
                    },
                ],
                properties=self._properties_for_model(HubSpotDeal.Properties),
            ),
        )
        return HubSpotDealSchema(many=True).load(map(lambda it: it.to_dict(), result.results))

    @access_token
    def find_deals_with_billy_sales_order_id(self, sales_order_id: int) -> List[HubSpotDeal]:
        result = self.client.crm.deals.search_api.do_search(
            DealSearchRequest(
                filter_groups=[
                    {
                        "filters": [
                            {"propertyName": "billy_sales_order_id", "value": str(sales_order_id), "operator": "EQ"},
                        ],
                    },
                ],
                properties=self._properties_for_model(HubSpotDeal.Properties),
            ),
        )
        return HubSpotDealSchema(many=True).load(map(lambda it: it.to_dict(), result.results))

    @access_token
    def find_deal_line_items_with_billy_sales_order_line_ids(
        self,
        sales_order_line_ids: List[int],
    ) -> List[HubSpotLineItem]:
        result = self.client.crm.line_items.search_api.do_search(
            LineItemSearchRequest(
                filter_groups=[
                    {
                        "filters": [
                            {
                                "propertyName": "billy_sales_order_line_id",
                                "values": list(map(str, sales_order_line_ids)),
                                "operator": "IN",
                            },
                        ],
                    },
                ],
                properties=self._properties_for_model(HubSpotLineItem.Properties),
            ),
        )
        return HubSpotLineItemSchema(many=True).load(map(lambda it: it.to_dict(), result.results))

    @access_token
    def find_deal_line_items_not_linked_to_sales_order_line_ids(
        self,
        deal_id: str,
        sales_order_line_ids: List[int],
    ) -> List[HubSpotLineItem]:
        """Finds lines items not linked to any of the provided sales order line ids"""
        result = self.client.crm.line_items.search_api.do_search(
            LineItemSearchRequest(
                filter_groups=[
                    {
                        "filters": [
                            {
                                "propertyName": "billy_sales_order_line_id",
                                "values": list(map(str, sales_order_line_ids)),
                                "operator": "NOT_IN",
                            },
                            {"propertyName": "associations.deal", "value": str(deal_id), "operator": "EQ"},
                        ],
                    },
                ],
                properties=self._properties_for_model(HubSpotLineItem.Properties),
            ),
        )
        return HubSpotLineItemSchema(many=True).load(map(lambda it: it.to_dict(), result.results))

    @access_token
    def find_contacts_by_emails(self, emails: List[int]) -> List[HubSpotContact]:
        result = self.client.crm.contacts.search_api.do_search(
            ContactSearchRequest(
                filter_groups=[
                    {
                        "filters": [
                            {
                                "propertyName": "email",
                                "values": list(map(str, emails)),
                                "operator": "IN",
                            },
                        ],
                    },
                ],
                properties=self._properties_for_model(HubSpotContact.Properties),
            ),
        )
        return HubSpotContactSchema(many=True).load(map(lambda it: it.to_dict(), result.results))

    @access_token
    def find_crm_cards(self, app_id):
        result = self.client.crm.extensions.cards.cards_api.get_all(app_id)
        return result

    @access_token
    def create_crm_card(self, app_id, card_definition):
        return self.client.crm.extensions.cards.cards_api.create(app_id, card_create_request=card_definition)

    @access_token
    def update_crm_card(self, app_id, card_id, card_definition):
        return self.client.crm.extensions.cards.cards_api.update(app_id, card_id, card_patch_request=card_definition)

    @access_token
    def send_email(self, email_id: str, recipient: str):
        logger.info(f"Sending email {email_id} to recipient {recipient}")
        return self.client.marketing.transactional.single_send_api.send_email(
            public_single_send_request_egg=PublicSingleSendRequestEgg(
                email_id=email_id,
                message=PublicSingleSendEmail(to=recipient),
            ),
        )

    @access_token
    def get_pipelines_for_type(self, object_type: str) -> List[HubSpotPipeline]:
        result = self.client.crm.pipelines.pipelines_api.get_all(object_type)
        return HubSpotPipelineSchema(many=True).load(map(lambda it: it.to_dict(), result.results))

    def _properties_for_model(self, model: dataclasses.dataclass):
        """Returns all field names defined in the model data class"""
        return [field.name for field in dataclasses.fields(model)]

    def _get_properties_for_object_type(self, hubspot_object_type):
        """Useful for dev/debug to get all available properties from hubspot"""
        return self.client.crm.properties.core_api.get_all(hubspot_object_type).results

    def _get_property_names_for_object_type(self, hubspot_object_type):
        """Useful for dev/debug to get all available properties from hubspot"""
        return [p.name for p in self._get_properties_for_object_type(hubspot_object_type)]

    def refresh_access_token(self):
        response = self.client.auth.oauth.tokens_api.create_token(
            grant_type="refresh_token",
            redirect_uri=self.redirect_uri,
            client_id=self.client_id,
            client_secret=self.client_secret,
            refresh_token=self.refresh_token,
        )
        self.client.access_token = response.access_token
        self._save_access_token(response)

    def _load_access_token(self):
        return cache.get("hubspot_access_token", None)

    def _save_access_token(self, response):
        cache.set("hubspot_access_token", response.access_token, response.expires_in)
