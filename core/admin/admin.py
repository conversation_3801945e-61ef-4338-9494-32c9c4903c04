import csv
import logging
import pprint

import mongoengine
import codecs
import textwrap
import json
import itertools
import operator
import re

from urllib.parse import urlparse, parse_qs
from typing import Optional, List
from collections import defaultdict, Counter
from datetime import date, datetime, timedelta
from functools import cache, reduce
from abc import abstractmethod
import difflib
import pygments
from pygments import lexers
from pygments.formatters import HtmlFormatter

from django.core.exceptions import ValidationError
from django_admin_inline_paginator.admin import TabularInlinePaginated
from simple_history.utils import bulk_update_with_history
from django.contrib.admin.templatetags.admin_urls import add_preserved_filters

from djangoql.serializers import DjangoQLSchemaSerializer

from django.utils import timezone
from admin_auto_filters.filters import AutocompleteFilter, AutocompleteFilterFactory
from django.template.defaultfilters import pluralize
from django.forms.models import model_to_dict
from django import forms
from django.contrib import admin, messages
from django.shortcuts import get_object_or_404
from django.contrib.admin.helpers import ACTION_CHECKBOX_NAME

from django_celery_beat.models import (
    ClockedSchedule,
    CrontabSchedule,
    IntervalSchedule,
    PeriodicTask,
    SolarSchedule,
)

from unfold.decorators import action as unfold_action
from unfold.widgets import (
    UnfoldAdminSelectWidget,
    UnfoldAdminTextInputWidget,
)
from unfold.contrib.forms.widgets import ArrayWidget

from unfold.forms import AdminPasswordChangeForm, UserChangeForm, UserCreationForm

from explorer.models import Query, DatabaseConnection
from explorer.admin import QueryAdmin as ExplorerQueryAdmin

from push_notifications.models import APNSDevice, GCMDevice, WebPushDevice, WNSDevice
from push_notifications.admin import DeviceAdmin as PushDeviceAdmin
from push_notifications.admin import GCMDeviceAdmin as PushGCMDeviceAdmin
from push_notifications.admin import WebPushDeviceAdmin as PushWebPushDeviceAdmin


from core.admin.unfold_admin import unfold_admin_site
from django.contrib.admin import EmptyFieldListFilter, SimpleListFilter
from advanced_filters.admin import AdminAdvancedFiltersMixin, AdvancedFilter, AdvancedFilterAdmin
from django.contrib.auth.models import Group
from django.contrib.auth.admin import UserAdmin, GroupAdmin
from django.db import transaction, IntegrityError
from django.db.models import (
    Count,
    Sum,
    Q,
    Exists,
    OuterRef,
    Subquery,
    F,
    Value,
    CharField,
    TextField,
    IntegerField,
)
from django.db.models.query import QuerySet
from django.db.models.functions import Coalesce, Cast, Concat, Trunc
from django.core.exceptions import PermissionDenied
from django.http import HttpResponse, HttpResponseRedirect, StreamingHttpResponse, HttpRequest
from django.shortcuts import redirect, render
from django.urls import path, reverse
from django.utils.http import urlencode
from django.utils.html import format_html
from django.utils.formats import date_format
from django.utils.safestring import mark_safe
from django.utils.translation import gettext_lazy as _
from django.contrib.admin.options import IS_POPUP_VAR
from mongoengine.queryset.visitor import Q as DQ
from django.template.defaultfilters import truncatechars
from simple_history.models import HistoricalRecords
from djangoql.admin import DjangoQLSearchMixin
from django.contrib.admin.views.main import ChangeList

from nested_inline.admin import NestedStackedInline, NestedModelAdmin

from unfold.contrib.filters.admin import (
    RangeDateFilter,
    RangeNumericFilter,
)

from django_celery_beat.admin import ClockedScheduleAdmin as BaseClockedScheduleAdmin
from django_celery_beat.admin import CrontabScheduleAdmin as BaseCrontabScheduleAdmin
from django_celery_beat.admin import PeriodicTaskAdmin as BasePeriodicTaskAdmin
from django_celery_beat.admin import PeriodicTaskForm, TaskSelectWidget

from django_celery_results.admin import TaskResult, TaskResultAdmin as BaseTaskResultAdmin
from django_celery_results.admin import GroupResult, GroupResultAdmin as BaseGroupResultAdmin

from simple_history.admin import SimpleHistoryAdmin as SimpleHistoryAdminBase

from modeltranslation.admin import TranslationAdmin, TranslationTabularInline

from django.conf import settings

from core.admin.actions.add_customer_to_questionnaire import (
    CustomerAssignQuestionnaireForm,
    CustomerResetQuestionnaireForm,
)
from core.admin.admin_base import TabularInlineBase, StackedInlineBase
from core.admin.shop.admin_shop import ShopCategoryAdmin
from core.models.addresses import (
    FinancingPartnerCompanyAddress,
    FinancingPartnerInvoiceAddress,
    FinancingPartnerDeliveryAddress,
)
from core.models.choices import CustomerStatus
from core.models.financing_partners import FinancingPartner
from core.models.models import CarouselItem
from core.models.questionnaires import (
    AnswerChoice,
    Question,
    Questionnaire,
    QuestionnaireDismissed,
    QuestionnaireTextAnswer,
    QuestionnaireChoiceAnswer,
)
from core.models.referrals import CampaignReferral, CustomerReferral
from core.models.shop import ReferralLine, ShopCategory, RecallItemRequest
from core.salesorders import validation as salesorders_validation
from .multi_select_field_list_filter import MultiSelectFieldListFilter

from ..csv_export import CsvEchoWriter
from core import products, data_export, shipment_routing, signals, signal_notifiers

from core.tasks import generate_inventory_cost_table, retry_direct_debit_payment
from core.integrations import nofence_integration

from ..forms import (
    CollarDebugExportActionForm,
    CreateCustomerForm,
    StartDataExportActionForm,
    CreateAppUserForm,
    MassEmailForm,
    SelectMassEmailForm,
    UploadCSVMassEmailForm,
    CollarTagForm,
    SetCollarTagForm,
    RemoveCollarTagForm,
    TestEmailRecordForm,
    ReturnAndReplacementForm,
    SetCollarEnvironment,
    SelectFakeDoorLinkForm,
)
from .forms import (
    ConfirmShipmentReceiptForm,
    get_adjust_inventory_components_form,
    AssembleInventoryForm,
    get_inventory_change_form,
    ProductPriceForm,
    TestNotificationMessageForm,
    ChangeNofenceCompanyForm,
    get_set_communication_plan_form,
    ConfirmCancelShipmentForm,
)
from . import mongo_forms
from .purchaseorders import (
    ConfirmPurchaseReceiptForm,
    PurchaseReceiptInissionAddForm,
    PurchaseReceiptInissionRefreshForm,
)
from ..models import (
    CATTLE,
    Collar,
    CollarBOMBlock,
    CollarUsage,
    CollarUsageSummary,
    Customer,
    UserAccountAccess,
    Invoice,
    InvoiceLine,
    MessageDocument,
    PricePlan,
    PricePlanPrice,
    SHEEP_GOAT,
    SalesOrder,
    SalesOrderLine,
    SalesOrderSource,
    SalesOrderStatus,
    Shipment,
    ShipmentLine,
    SubscriptionChange,
    Warehouse,
    KeyValue,
    Inventory,
    PlannedInventoryAdjustment,
    User,
    NotificationLog,
    NotificationMessage,
    CustomerCompanyAddress,
    CustomerDeliveryAddress,
    CustomerInvoiceAddress,
    SalesOrderDeliveryAddress,
    SalesOrderInvoiceAddress,
    NofenceCompanyAddress,
    WarehouseLocation,
    InventoryJournal,
    Product,
    InvoiceDocument,
    NOFENCE_UK,
    MoneyField,
    PaymentReminder,
    CustomerEmailQueue,
    PurchaseBlockExclude,
    AppUser,
    PushToken,
    PushNotificationSettings,
    JiraReturnIssue,
    NofenceCompany,
    SupportTicket,
    HubSpotPipelineModel,
    HubSpotPipelineStageModel,
    XeroAuthConnection,
    SalesOrderRecord,
    HubSpotTaskModel,
    InventoryAdjustmentType,
    EmailConfig,
    MassEmail,
    MassEmailRecord,
    CollarTag,
    CollarTagFromMongo,
    ShipmentPickedUnit,
    CollarUsageMonthlyProduct,
    CollarUsageMonthly,
    InvoicedUsage,
    EmailRecord,
    EmailRecordEvent,
    ReturnAndReplacement,
    CollarReplacement,
    ReplacementDeliveryAddress,
    CollarWarranty,
    TicketBAI,
    ReturnAndReplacementExtraItem,
    TermsAndConditions,
    InvoiceDocumentFile,
    ReportExport,
    PurchaseOrderNumber,
    SyncStatus,
    FakeDoorLink,
    CustomerEmail,
    DebtCollection,
    DirectDebitMandate,
    DirectDebitEvent,
    DirectDebitPayment,
    DirectDebitPayout,
    XeroItem,
    PushNotification,
    NofenceLearningResource,
    Supplier,
    SupplierProduct,
    SupplierAddress,
    SupplierBillingAddress,
    WarehouseAddress,
    PurchaseOrder,
    PurchaseOrderLine,
    PurchaseOrderDeliveryAddress,
    GoodsReceipt,
    GoodsReceiptLine,
    PurchaseReceipt,
    PurchaseReceiptFile,
    PurchaseReceiptLine,
    PurchaseReceiptCollarLink,
    PurchaseOrderFile,
    INISSION,
    CompoundProductLink,
    PurchaseInvoice,
    PurchaseInvoiceLine,
    ProductDisplayName,
    UserDocument,
    PushNotificationType,
    ProductPrice,
    FreightMatrix,
    FreightWeightThreshold,
    Money,
    Basket,
    BasketLine,
    BasketVoucherLine,
    Voucher,
    CustomerVoucher,
    Discount,
    QuickQuote,
    QuickQuoteAnimal,
    QuickQuoteFarmingPurpose,
    QuickQuotePasture,
    SalesOrderAccountingSystem,
    EmailRecordAttachment,
    PostPaymentLimit,
    CustomerStatusEvent,
    DefaultReplacementProduct,
    CollarOwner,
    TaxDefinition,
    TaxCategory,
    TaxCode,
    CustomerAccountingSystem,
    TaxCalculation,
    CardPayment,
    CardPaymentEvent,
    StripeProductTaxCode,
    HTMLContent,
    DataExport,
    InvoiceDocumentPayment,
    RivertyCase,
    RivertyCasePayment,
    NOFENCE_NO,
    AppAlert,
    RivertyFileUpload,
    VATNumberConfig,
    StripePaymentMethod,
    VismaPaymentSync,
    XeroPaymentSync,
    ShipmentRoutingRule,
    ShipmentRoutingEvalLog,
)
from ..integrations.hubspot_integration import tasks as hubspot_tasks
from ..integrations.visma_integration import tasks as visma_tasks
from ..collar_assignment import (
    update_collars_owner_and_user,
    update_collars_environment,
    CollarAssignOwnerUserForm,
)
from .. import app_notifications
from ..app_notifications import (
    SelectNotificationMessageForm,
    queue_notifications,
    retry_failed_message_log_entry,
    retry_failed_notifications_from_log,
)
from core.pagination import LargeTablePaginatorPG

from core.default_api_clients import (
    create_nofence_client,
    create_nofence2_client,
)
from ..services import payment_reminders
from .. import csv_export
from ..services.usage_summarizer import UsageSummarizer

from ..default_services import (
    create_inventory_service,
    create_email_service,
    create_price_level_notifier,
    create_price_cap_notifier,
    create_quick_quote_service,
    create_usage_biller_flexible_monthy,
    create_collar_blocker,
    create_goods_receipt_service,
    create_purchase_receipt_service,
    create_direct_debit_payout_notifier,
    create_sales_order_service,
    create_shipment_service,
    create_riverty_service,
    create_invoicer_service,
    create_stripe_card_payment_service,
    create_usage_summarizer,
    create_iotvalue_service,
)
from core.services.goods_receipt_service import GoodsReceiptException
from core.services.purchase_receipt_service import PurchaseReceiptException
from core.services.inventory_service import InventoryAdjustmentError
from core.app_notifications import PushNotificationHelper
from core.services.riverty_dept_collection import RivertyServiceError

from .admin_base import UnfoldModelAdmin, UnfoldSortableMixin as SortableAdminMixin, ModelFormBase

from .admin_widgets import (
    UnfoldSelect2MultipleAdminWidget,
)

from .config_manager_admin import (  # noqa: F401
    ConfigManagerAdmin,
    ConfigOptionAdmin,
)

from . import admin_actions

logger = logging.getLogger(__name__)

admin.site.site_title = "Billy - Nofence Admin"
admin.site.site_header = "Billy - Nofence Admin"

admin.FieldListFilter.register(
    lambda f: (isinstance(f, (CharField, TextField, IntegerField)) or (hasattr(f, "remote_field") and f.remote_field)),
    MultiSelectFieldListFilter,
    take_priority=True,
)


class UnfoldTaskSelectWidget(UnfoldAdminSelectWidget, TaskSelectWidget):
    pass


class TranslationUnfoldModelAdmin(TranslationAdmin, UnfoldModelAdmin):
    pass


class UnfoldPeriodicTaskForm(PeriodicTaskForm):
    def __init__(self, *args, **kwargs):
        super().__init__(*args, **kwargs)
        self.fields["task"].widget = UnfoldAdminTextInputWidget()
        self.fields["regtask"].widget = UnfoldTaskSelectWidget()


@admin.register(PeriodicTask, site=unfold_admin_site)
class PeriodicTaskAdmin(BasePeriodicTaskAdmin, UnfoldModelAdmin):
    form = UnfoldPeriodicTaskForm


@admin.register(IntervalSchedule, site=unfold_admin_site)
class IntervalScheduleAdmin(UnfoldModelAdmin):
    pass


@admin.register(CrontabSchedule, site=unfold_admin_site)
class CrontabScheduleAdmin(BaseCrontabScheduleAdmin, UnfoldModelAdmin):
    pass


@admin.register(SolarSchedule, site=unfold_admin_site)
class SolarScheduleAdmin(UnfoldModelAdmin):
    pass


@admin.register(ClockedSchedule, site=unfold_admin_site)
class ClockedScheduleAdmin(BaseClockedScheduleAdmin, UnfoldModelAdmin):
    pass


@admin.register(Query, site=unfold_admin_site)
class QueryAdmin(ExplorerQueryAdmin, UnfoldModelAdmin):
    pass


@admin.register(DatabaseConnection, site=unfold_admin_site)
class DatabaseConnectionAdmin(UnfoldModelAdmin):
    pass


@admin.register(APNSDevice, site=unfold_admin_site)
class APNSDeviceAdmin(PushDeviceAdmin, UnfoldModelAdmin):
    pass


@admin.register(GCMDevice, site=unfold_admin_site)
class GCMDeviceAdmin(PushGCMDeviceAdmin, UnfoldModelAdmin):
    pass


@admin.register(WNSDevice, site=unfold_admin_site)
class DeviceAdmin(PushDeviceAdmin, UnfoldModelAdmin):
    pass


@admin.register(WebPushDevice, site=unfold_admin_site)
class WebPushDeviceAdmin(PushWebPushDeviceAdmin, UnfoldModelAdmin):
    pass


# Custom Filters
class CustomerFilter(AutocompleteFilter):
    title = "Customer"
    field_name = "customer"


class AdminAutocompleteFilter(AutocompleteFilter):
    # Monkeypatching admin site url to be the base site url, because otherwise custom sites get errors.
    def get_autocomplete_url(self, request, model_admin):
        url = "%s:autocomplete"
        default_admin_site = "admin"
        return reverse(url % default_admin_site)


class OwnerFilter(AdminAutocompleteFilter):
    title = "Owner"
    field_name = "owner"


class UserFilter(AdminAutocompleteFilter):
    title = "User"
    field_name = "user"


class InvoiceFilter(AutocompleteFilter):
    title = "Invoice"
    field_name = "invoice"


class SalesOrderFilter(AutocompleteFilter):
    title = "SalesOrder"
    field_name = "sales_order"


class NofenceContactFilter(AutocompleteFilter):
    title = "Nofence Contact"
    field_name = "nofence_contact"


class CollarFilter(AutocompleteFilter):
    title = "Serial Number"
    field_name = "collar"


class EditInlineFilterHack(SimpleListFilter):
    """Used to add a query parameter to enable inline edits on the change list"""

    title = "Edit Inline"
    parameter_name = "_enable_edit_inline"

    def lookups(self, request, model_admin):
        return [("yes", "Yes")]

    def queryset(self, request, queryset):
        return queryset


class DynamicListEditableMixin:
    """Make some columns editable in the changelist.

    If the user has super user permission, a "Edit inline" filter
    is added to the top of the filter list, which when enabled will
    allow the columns in the dynamic_list_editable to be edited inline.
    """

    dynamic_list_editable = []

    def get_list_filter(self, request):
        list_filter = super().get_list_filter(request)

        if self.dynamic_list_editable and request.user.is_superuser:
            list_filter = list(list_filter)
            list_filter.insert(0, EditInlineFilterHack)

        return list_filter

    @property
    def list_editable(self):
        if self.dynamic_list_editable:
            try:
                request = HistoricalRecords.context.request
                if request.user.is_superuser and request.GET.get("_enable_edit_inline", "no") == "yes":
                    return self.dynamic_list_editable
            except AttributeError:
                pass
        return []

    def get_list_display(self, *args, **kwargs):
        list_display = super().get_list_display(*args, **kwargs)
        if self.list_editable:
            missing = []
            for item in self.list_editable:
                if item not in list_display:
                    missing.append(item)
            if missing:
                list_display = [*list_display, *missing]
        return list_display


class TrackHistoryForActionsMixin:
    def response_action(self, request, queryset):
        action_name = request.POST.get("action")
        selected_ids = request.POST.getlist(ACTION_CHECKBOX_NAME)

        if not action_name:
            return super().response_action(request, queryset)

        if selected_ids:
            queryset = queryset.filter(pk__in=selected_ids)

        request._admin_action_name = action_name
        response = super().response_action(request, queryset)

        for obj in queryset:
            obj.save(update_fields=[])

        return response


class SimpleHistoryDiffMixing:
    """Add this to a SimpleHistoryAdmin class to get a diff view on the history page"""

    history_list_display = []

    # This field can be set on the model admin to enable unified diff for certain columns.
    # This will show a diff of the changed value, instead of the full old and new value.
    # Useful for big strings, json documents etc.
    # history_unified_diff = [...]

    def get_history_list_display(self, request):
        history_list_display = self.history_list_display[:]
        unified_diff = getattr(self, "history_unified_diff", [])
        if unified_diff:
            history_list_display = [*map(self._get_unified_diff_column, unified_diff), *history_list_display]
        return history_list_display

    def _get_unified_diff_column(self, column):
        # Add a prefix to the column name, to prevent the full column value to be displayed if there is no diff
        return f"{column}_diff"

    def set_history_delta_changes(self, request, historical_records, foreign_keys_are_objs=True, **kwargs):
        super().set_history_delta_changes(request, historical_records, foreign_keys_are_objs, **kwargs)
        if hasattr(self, "history_unified_diff"):
            previous = None
            for current in historical_records:
                # Set the default value on all entries, in case there is no diff
                for diff_column in getattr(self, "history_unified_diff", []):
                    setattr(current, self._get_unified_diff_column(diff_column), "")

                # Calculate and set diff
                if previous is not None:
                    for diff_column, diff_value in self._unified_diff_changes(previous, current):
                        setattr(previous, self._get_unified_diff_column(diff_column), diff_value)

                previous = current

    def _unified_diff_changes(self, previous_record, current_record):
        """
        NOTE: In simple history version 3.6.0 a "changes" column was added to the history view, this
              is basically the same as we used to do in this method.
              I think the unified diff rendering we have here is better for changes to large text fields,
              like JSON blobs etc. So keeping the unified diff part of this implementation for now.
        """
        unified_diff = getattr(self, "history_unified_diff", [])
        changes = {}
        if previous_record:
            delta = previous_record.diff_against(current_record)
            for change in delta.changes:
                if change.field in unified_diff:
                    old_body = pprint.pformat(change.old, width=120).splitlines(True)
                    new_body = pprint.pformat(change.new, width=120).splitlines(True)
                    diff = "".join(difflib.unified_diff(old_body, new_body, n=1))
                    diff = pygments.highlight(
                        diff,
                        lexers.get_lexer_by_name("diff"),
                        HtmlFormatter(style="friendly", noclasses=True),
                    )
                    changes[change.field] = mark_safe(diff)
        for column in unified_diff:
            yield column, changes.get(column, "")


class InlineActionsMixin:
    """Add actions to the top of the change form view.

    The extra submit buttons are rendered by the admin/change_form_object_tools.html
    template if set.

    See ShipmentAdmin for example.

    NOTE: Should be used with care. The user may this that the current change form
          will be saved when clicking the action. This is not the case, so any unsaved
          changes in the form will be lost when the action is clicked.
    """

    def get_inline_actions(self, request, obj):
        """Should return a list action

        The actions muse defined on the model admin. Same as the regular actions.
        """
        return []

    def render_change_form(self, request, context, obj=None, **kwargs):
        if obj is not None:
            inline_actions = []
            actions = self.get_inline_actions(request, obj)
            for action in self._filter_actions_by_permissions(request, actions):
                action_method = getattr(self, action, None)
                if action_method is None:
                    continue
                action_url = reverse(f"admin:{self.model._meta.app_label}_{self.model._meta.model_name}_changelist")
                preserved_filters = self.get_preserved_filters(request)
                action_url = add_preserved_filters(
                    {"preserved_filters": preserved_filters, "opts": self.opts},
                    action_url,
                )
                inline_actions.append(
                    {
                        "action": action,
                        "title": getattr(action_method, "short_description", action),
                        "url": action_url,
                    },
                )
            context["inline_actions"] = inline_actions
        return super().render_change_form(request, context, obj=obj, **kwargs)

    def get_inline_action_context(self, request):
        return {
            "is_from_inline_action": "_from_inline_action" in request.POST,
        }

    def get_redirect_url_for_inline_action(self, request, obj_pk):
        """Can be used to redirect the user to either the change list or the change form
        depending on wether the action was triggered from an inline action or not."""

        if (request.method == "POST" and "_from_inline_action" not in request.POST) or not obj_pk:
            action_url = reverse(f"admin:{self.model._meta.app_label}_{self.model._meta.model_name}_changelist")
        else:
            action_url = reverse(
                f"admin:{self.model._meta.app_label}_{self.model._meta.model_name}_change",
                args=(obj_pk,),
            )

        preserved_filters = self.get_preserved_filters(request)
        redirect_url = add_preserved_filters(
            {"preserved_filters": preserved_filters, "opts": self.opts},
            action_url,
        )
        return redirect_url


# Create a new mixin that inherits from DjangoQLSearchMixin


class DisabledCheckboxDjangoQLSearchMixin(DjangoQLSearchMixin):
    djangoql_completion_enabled_by_default = False


class SimpleHistoryAdmin(
    TrackHistoryForActionsMixin,
    DisabledCheckboxDjangoQLSearchMixin,
    SimpleHistoryDiffMixing,
    SimpleHistoryAdminBase,
):
    """Add common setting to all simple history admin views"""

    history_list_display = [
        "impersonated_by",
        "history_admin_action",
        "history_request_path",
        "history_celery_parent_id",
        "history_celery_current_id",
        "correlation_id_link",
    ]

    history_list_per_page = 50

    def impersonated_by(self, obj):
        if obj.history_impersonator:
            url = reverse("admin:core_user_change", args=(obj.history_impersonator.pk,))
            return format_html("<a href='{}'>{}</a>", url, obj.history_impersonator)
        return "-"

    def correlation_id_link(self, obj):
        if obj.history_correlation_id:
            updated_at = obj.updated_at
            to_ts = int((updated_at + timedelta(minutes=5)).timestamp()) * 1000
            from_ts = int((updated_at - timedelta(days=1)).timestamp()) * 1000
            return format_html(
                '<a href="{}" class="inlineviewlink">DataDog</a>',
                "https://app.datadoghq.eu/logs?"
                f"query=%40correlation_id%3A{obj.history_correlation_id}"
                "&integration_short_name=&messageDisplay=inline&stream_sort=desc"
                f"&from_ts={from_ts}&to_ts={to_ts}&live=false",
            )
        return "-"

    correlation_id_link.short_description = "Correlation ID"

    def get_history_queryset(self, *args, **kwargs) -> QuerySet:
        qs = super().get_history_queryset(*args, **kwargs)
        # Join in the impersonator
        return qs.select_related("history_impersonator")


class CattleListFilter(SimpleListFilter):
    title = "Cattle Collars"
    parameter_name = "cattle_collars"

    def lookups(self, request, model_admin):
        return ("YES", "Yes"), ("NO", "No")

    def queryset(self, request, queryset):
        if self.value() == "YES":
            return queryset.filter(
                Exists(
                    Collar.objects.filter(
                        Q(owner=OuterRef("pk")) | Q(user=OuterRef("pk")),
                        product=CATTLE,
                    ),
                ),
            )
        if self.value() == "NO":
            return queryset.filter(
                ~Exists(
                    Collar.objects.filter(
                        Q(owner=OuterRef("pk")) | Q(user=OuterRef("pk")),
                        product=CATTLE,
                    ),
                ),
            )


class SheepGoatListFilter(SimpleListFilter):
    title = "Sheep/Goat Collars"
    parameter_name = "sheep_goat_collars"

    def lookups(self, request, model_admin):
        return ("YES", "Yes"), ("NO", "No")

    def queryset(self, request, queryset):
        if self.value() == "YES":
            return queryset.filter(
                Exists(
                    Collar.objects.filter(
                        Q(owner=OuterRef("pk")) | Q(user=OuterRef("pk")),
                        product=SHEEP_GOAT,
                    ),
                ),
            )
        if self.value() == "NO":
            return queryset.filter(
                ~Exists(
                    Collar.objects.filter(
                        Q(owner=OuterRef("pk")) | Q(user=OuterRef("pk")),
                        product=SHEEP_GOAT,
                    ),
                ),
            )


class OwnedCollarTypeFilter(SimpleListFilter):
    title = "Owned collar types"
    parameter_name = "collar_type"

    def lookups(self, request, model_admin):
        return (
            ("ONLY_NEW", "Only new collars"),
            ("ONLY_LEGACY", "Only legacy collars"),
            ("BOTH", "Both new and legacy"),
        )

    def queryset(self, request, queryset):
        new_models = ["SG2.5", "C2.5"]
        legacy_models = ["S1", "SG1", "SG1.2", "SG2", "SG2.09", "SG2.1", "SG2.2", "C1", "C2", "C2.1", "C2.2"]
        if self.value() == "ONLY_NEW":
            return queryset.filter(
                Exists(
                    Collar.objects.filter(
                        Q(owner=OuterRef("pk")) | Q(user=OuterRef("pk")),
                        model__in=new_models,
                    ),
                ),
                ~Exists(
                    Collar.objects.filter(
                        Q(owner=OuterRef("pk")) | Q(user=OuterRef("pk")),
                        model__in=legacy_models,
                    ),
                ),
            )
        elif self.value() == "ONLY_LEGACY":
            return queryset.filter(
                Exists(
                    Collar.objects.filter(
                        Q(owner=OuterRef("pk")) | Q(user=OuterRef("pk")),
                        model__in=legacy_models,
                    ),
                ),
                ~Exists(
                    Collar.objects.filter(
                        Q(owner=OuterRef("pk")) | Q(user=OuterRef("pk")),
                        model__in=new_models,
                    ),
                ),
            )
        elif self.value() == "BOTH":
            return queryset.filter(
                Exists(
                    Collar.objects.filter(
                        Q(owner=OuterRef("pk")) | Q(user=OuterRef("pk")),
                        model__in=legacy_models,
                    ),
                ),
                Exists(
                    Collar.objects.filter(
                        Q(owner=OuterRef("pk")) | Q(user=OuterRef("pk")),
                        model__in=new_models,
                    ),
                ),
            )


class CustomerWithOverdueInvoiceFilter(SimpleListFilter):
    title = "Overdue Invoices"
    parameter_name = "overdueinv"

    def lookups(self, request, model_admin):
        return ("YES", "Yes"), ("14DAYS", "14+ days"), ("NO", "No")

    def queryset(self, request, queryset):
        if self.value() == "YES":
            return queryset.filter(
                Exists(
                    InvoiceDocument.objects.is_overdue().filter(
                        customer=OuterRef("pk"),
                    ),
                ),
            )
        elif self.value() == "14DAYS":
            return queryset.filter(
                Exists(
                    InvoiceDocument.objects.is_overdue().filter(
                        due_date__lt=date.today() - timedelta(days=14),
                        customer=OuterRef("pk"),
                    ),
                ),
            )
        elif self.value() == "NO":
            return queryset.filter(
                Exists(
                    InvoiceDocument.objects.is_overdue().filter(
                        customer=OuterRef("pk"),
                    ),
                    negated=True,
                ),
            )


class CustomerWithOpenInvoiceFilter(SimpleListFilter):
    title = "Open Invoices"
    parameter_name = "openinv"

    def lookups(self, request, model_admin):
        return ("YES", "Yes"), ("NO", "No")

    def queryset(self, request, queryset):
        if self.value() == "YES":
            return queryset.filter(
                Exists(
                    InvoiceDocument.objects.is_unpaid().filter(
                        customer=OuterRef("pk"),
                    ),
                ),
            )
        elif self.value() == "NO":
            return queryset.filter(
                Exists(
                    InvoiceDocument.objects.is_unpaid().filter(
                        customer=OuterRef("pk"),
                    ),
                    negated=True,
                ),
            )


class CustomerOverdueBalanceFilter(SimpleListFilter):
    title = "Overdue Balance"
    parameter_name = "overduebalance"

    def lookups(self, request, model_admin):
        # Does not consider currencies
        return (
            ("500", ">500"),
            ("1000", ">1000"),
            ("5000", ">5000"),
            ("10000", ">10000"),
            ("25000", ">25000"),
            ("50000", ">50000"),
            ("100000", ">100000"),
        )

    def queryset(self, request, queryset):
        if self.value():
            return queryset.filter(
                Exists(
                    InvoiceDocument.objects.is_overdue()
                    .filter(
                        customer=OuterRef("pk"),
                    )
                    .values("customer_id")
                    .annotate(total=Sum("balance"))
                    .filter(total__gte=float(self.value())),
                ),
            )


class CustomerWithCollarsHavingModemUpgradeStatus(SimpleListFilter):
    title = "Collars with modem FW status"
    parameter_name = "modemfw_status"

    def lookups(self, request, model_admin):
        return [
            (r["modem_upgrade_status"], r["modem_upgrade_status"])
            for r in Collar.objects.values("modem_upgrade_status").distinct()
            if r["modem_upgrade_status"]
        ]

    def queryset(self, request, queryset):
        if self.value():
            return queryset.filter(
                Exists(
                    Collar.objects.filter(
                        owner=OuterRef("pk"),
                        modem_upgrade_status=self.value(),
                    ),
                ),
            )


class CustomerWithCollarsModemUpgradeStatusChanged(SimpleListFilter):
    title = "Modem FW status changed"
    parameter_name = "modemfw_status_changed"

    def lookups(self, request, model_admin):
        return [
            (">3600", "< 1 hour ago"),
            (f">{3600 * 25}", "< 1 day ago"),
            ("<3600", "> 1 hours ago"),
            (f"<{3600 * 3}", "> 3 hours ago"),
            (f"<{3600 * 5}", "> 6 hours ago"),
            (f"<{3600 * 25}", "> 1 day ago"),
        ]

    def queryset(self, request, queryset):
        if self.value():
            filter = {}

            # Also filter on status from CustomerWithCollarsHavingModemUpgradeStatus if used, as
            # that is probably what the user expects, to only find collars that have been stuck
            # in a particular status for a certain time.
            status_value = request.GET.get("modemfw_status", None)
            if status_value is not None:
                filter["modem_upgrade_status"] = status_value

            cmp = self.value()[0]
            seconds = int(self.value()[1:])
            if cmp == ">":
                filter["modem_upgrade_status_created_at__gt"] = timezone.now() - timedelta(seconds=seconds)
            elif cmp == "<":
                filter["modem_upgrade_status_created_at__lt"] = timezone.now() - timedelta(seconds=seconds)
            return queryset.filter(
                Exists(Collar.objects.filter(owner=OuterRef("pk"), **filter)),
            )


class CollarsWithModemUpgradeStatusChanged(SimpleListFilter):
    title = "Modem FW status changed"
    parameter_name = "modemfw_status_changed"

    def lookups(self, request, model_admin):
        return [
            (">3600", "< 1 hour ago"),
            (f">{3600 * 25}", "< 1 day ago"),
            ("<3600", "> 1 hours ago"),
            (f"<{3600 * 3}", "> 3 hours ago"),
            (f"<{3600 * 5}", "> 6 hours ago"),
            (f"<{3600 * 25}", "> 1 day ago"),
        ]

    def queryset(self, request, queryset):
        if self.value():
            filter = {}
            cmp = self.value()[0]
            seconds = int(self.value()[1:])
            if cmp == ">":
                filter["modem_upgrade_status_created_at__gt"] = timezone.now() - timedelta(seconds=seconds)
            elif cmp == "<":
                filter["modem_upgrade_status_created_at__lt"] = timezone.now() - timedelta(seconds=seconds)
            return queryset.filter(**filter)


class CustomerWithCollarModemUpgradeRestarts(RangeNumericFilter):
    def __init__(self, field, request, params, model, model_admin, field_path):
        # Override the field_path as the django admin failed with
        # Customer has no field named 'modem_upgrade_status_upgrade_restarts' when trying specify the
        # field on the CustomerAdmin
        super().__init__(
            field,
            request,
            params,
            model,
            model_admin,
            "owned_collars__modem_upgrade_status_upgrade_restarts",
        )
        self.title = "Modem FW upgrade restarts"

    def queryset(self, request, queryset):
        return super().queryset(request, queryset).distinct()


class CustomerWithCollarModemDownloadRestarts(RangeNumericFilter):
    def __init__(self, field, request, params, model, model_admin, field_path):
        # Override the field_path as the django admin failed with
        # Customer has no field named 'modem_upgrade_status_download_restarts' when trying specify the
        # field on the CustomerAdmin
        super().__init__(
            field,
            request,
            params,
            model,
            model_admin,
            "owned_collars__modem_upgrade_status_download_restarts",
        )
        self.title = "Modem FW download restarts"

    def queryset(self, request, queryset):
        return super().queryset(request, queryset).distinct()


class CustomerWithDirectDebitFilter(SimpleListFilter):
    title = "Direct Debit Active"
    parameter_name = "directdebit"

    def lookups(self, request, model_admin):
        return ("yes", "Yes"), ("no", "No")

    def queryset(self, request, queryset):
        if self.value() == "yes":
            return queryset.filter(
                Exists(
                    DirectDebitMandate.objects.filter(
                        customer=OuterRef("pk"),
                        status=DirectDebitMandate.Status.ACTIVE,
                    ),
                ),
            )
        elif self.value() == "no":
            return queryset.filter(
                ~Exists(
                    DirectDebitMandate.objects.filter(
                        customer=OuterRef("pk"),
                        status=DirectDebitMandate.Status.ACTIVE,
                    ),
                ),
            )


class PricePlanPrefListFilter(SimpleListFilter):
    title = "Selected Price Plan"
    parameter_name = "price_plan_selected"

    def lookups(self, request, model_admin):
        return ("yes", "Yes"), ("no", "No")

    def queryset(self, request, queryset):
        if self.value() == "yes":
            return queryset.exclude(
                sg_price_plan_preference__isnull=True,
                c_price_plan_preference__isnull=True,
            )

        if self.value() == "no":
            return queryset.filter(
                sg_price_plan_preference__isnull=True,
                c_price_plan_preference__isnull=True,
            )


class BaseSkuProductTypeFilter(SimpleListFilter):
    title = "Product Type"

    def lookups(self, request, model_admin):
        return [
            ("SKUS_CATTLE", _("Cattle")),
            ("SKUS_SHEEP_GOAT", _("Sheep/Goat")),
            ("SKUS_COLLARS", _("Collars")),
            ("SKUS_SUBSCRIPTION", _("Subscriptions")),
        ]

    def queryset(self, request, queryset):
        if self.value():
            skus = getattr(products, self.value(), {}).keys()
            return queryset.filter(**{self.parameter_name: skus})
        else:
            return queryset


class InventoryProductTypeFilter(BaseSkuProductTypeFilter):
    parameter_name = "product__sku__in"


class InventoryJournalTypeProductTypeFilter(BaseSkuProductTypeFilter):
    parameter_name = "inventory__product__sku__in"


class SalesOrderProductTypeFilter(SimpleListFilter):
    title = "Product Type"
    parameter_name = "sku__in"

    def lookups(self, request, model_admin):
        return [
            ("SKUS_CATTLE", _("Cattle")),
            ("SKUS_SHEEP_GOAT", _("Sheep/Goat")),
            ("SKUS_COLLARS", _("Collars")),
            ("SKUS_SUBSCRIPTION", _("Subscriptions")),
        ]

    def queryset(self, request, queryset):
        if self.value():
            skus = getattr(products, self.value(), {}).keys()
            return queryset.filter(
                Exists(
                    SalesOrderLine.objects.filter(order=OuterRef("pk"), sku__in=skus),
                ),
            )
        else:
            return queryset


class SalesOrderInventoryReservationFilter(SimpleListFilter):
    title = "Inventory Reservation"
    parameter_name = "inventory_reservation"

    def lookups(self, request, model_admin):
        return [
            ("yes", _("Yes")),
        ]

    def queryset(self, request, queryset):
        if self.value() == "yes":
            return queryset.filter(
                Exists(
                    SalesOrderLine.objects.filter(
                        order=OuterRef("pk"),
                        inventory_reserved=True,
                    ),
                ),
            )
        else:
            return queryset


def custom_titled_filter(title):
    class Wrapper(admin.FieldListFilter):
        def __new__(cls, *args, **kwargs):
            instance = admin.FieldListFilter.create(*args, **kwargs)
            instance.title = title
            return instance

    return Wrapper


def custom_titled_empty_filter(title):
    class CustomEmptyFieldListFilter(EmptyFieldListFilter):
        def __init__(self, *args, **kwargs):
            super().__init__(*args, **kwargs)
            self.title = title

    return CustomEmptyFieldListFilter


class ModelAdminBase(DisabledCheckboxDjangoQLSearchMixin, UnfoldModelAdmin):
    class Meta:
        abstract = True


class NestedModelAdminBase(NestedModelAdmin, UnfoldModelAdmin):
    class Meta:
        abstract = True


class ImmutableMixin:
    def has_change_permission(self, *args, **kwargs):
        return False

    def has_delete_permission(self, *args, **kwargs):
        return False

    def has_add_permission(self, *args, **kwargs):
        return False


class ImmutableMixinWithDelete:
    def has_change_permission(self, *args, **kwargs):
        return False

    def has_add_permission(self, *args, **kwargs):
        return False


class SendMassEmailAction:
    @abstractmethod
    def filter_customers_for_mass_email(self, queryset):
        pass

    @admin.action(
        description="Send Mass Email",
        permissions=["send_mass_email"],
    )
    def send_mass_email(self, request, queryset, *args, **kwargs):
        template = "admin/send_mass_email.html"
        filtered_queryset = self.filter_customers_for_mass_email(queryset)

        if "send_email" in request.POST:
            form = SelectMassEmailForm(request.POST)
            if form.is_valid():
                email_service = create_email_service()
                mass_email = form.cleaned_data["mass_email"]
                email_service.send_customer_mass_email(
                    mass_email=mass_email,
                    customers=filtered_queryset,
                )

                self.message_user(
                    request,
                    f"Enqueued mass email for sending to {filtered_queryset.count()} customers. ",
                    messages.SUCCESS,
                )

                return redirect(request.get_full_path())
        else:
            form = SelectMassEmailForm()

        opts = self.model._meta

        context = {
            **self.admin_site.each_context(request),
            "title": f"Send mass email to {filtered_queryset.count()} customers",
            "subtitle": "",
            "selected_objects": queryset,
            "form": form,
            "opts": opts,
            "app_label": opts.app_label,
        }

        return render(request, template, context=context)


class CollarUsageInline(TabularInlineBase):
    model = CollarUsage

    fields = ("date", "product", "customer")

    ordering = ["-date"]

    autocomplete_fields = ["customer"]

    def get_queryset(self, request):
        q = super().get_queryset(request)
        return q.select_related("collar", "customer__user")


class CollarUsageSummaryInline(TabularInlineBase):
    model = CollarUsageSummary

    fields = ("date", "product", "total_usage")

    ordering = ["-date"]


class PricePlanPricingInline(TabularInlineBase):
    model = PricePlanPrice
    fields = ("countries", "annual", "usage")
    extra = 0


class CustomerInline(TabularInlineBase):
    model = Customer
    fk_name = "user"
    show_change_link = True

    fields = ("name", "country", "visma_id", "business_reg_no", "nofence_company")
    autocomplete_fields = ["nofence_contact"]
    extra = 0

    def get_fields(self, request, obj=None):
        if obj is not None and not obj.is_staff:
            # In some special cases the when trying to disable the if_staff flag on a user and
            # the "Contact required when disabling invoicing unless staff" validation kicks in,
            # we can get a crash if the nofence_contact is not included in the inline form.
            # By including the field here we validation error will show instead of crashing.
            if obj.is_customer() and not obj.customer.automatic_invoice:
                return (
                    "name",
                    "country",
                    "visma_id",
                    "business_reg_no",
                    "automatic_invoice",
                    "nofence_contact",
                    "nofence_company",
                )
        return ("name", "country", "visma_id", "business_reg_no", "nofence_company")

    def has_change_permission(self, request, obj=None) -> bool:
        return request.user.has_perm("core.change_customer")


class PushNotificationSettingsInline(TabularInlineBase):
    model = PushNotificationSettings

    def has_delete_permission(self, request, obj) -> bool:
        return False


@admin.register(User, site=unfold_admin_site)
class UserAdmin(SimpleHistoryAdmin, UserAdmin, UnfoldModelAdmin):
    form = UserChangeForm
    add_form = UserCreationForm
    change_password_form = AdminPasswordChangeForm

    add_fieldsets = (
        (
            None,
            {
                "classes": ("wide",),
                "fields": ("email", "password1", "password2"),
            },
        ),
    )
    list_display = ("email", "name", "is_staff", "is_customer")
    list_filter = [
        "is_staff",
        "is_superuser",
        ("customer", admin.EmptyFieldListFilter),
        ("app_user", admin.EmptyFieldListFilter),
        "is_active",
        "groups",
    ]
    search_fields = ("name", "email")
    ordering = ("date_joined",)
    filter_horizontal = (
        "groups",
        "user_permissions",
    )
    readonly_fields = ("user_activation_link", "is_activated")
    inlines = [
        CustomerInline,
        PushNotificationSettingsInline,
    ]
    actions = [
        "send_activation_link",
    ]

    @admin.display(boolean=True)
    def is_customer(self, user: User):
        return user.is_customer()

    def get_fieldsets(self, request, obj: User = None):
        if obj is None:
            return self.add_fieldsets
        auth_fields = ["email", "password", "activation_status", "is_activated"]
        if can_manage_groups_only(request, obj):
            # Remove the password field to not display the password reset link and hash
            auth_fields = ["email", "activation_status", "is_activated"]

        if not obj.is_user_activation_done() and obj.activation_status not in [
            User.ActivationStatus.APP_USER,
            User.ActivationStatus.ACTIVATED_FROM_ACCOUNT_CREATION_FLOW,
        ]:
            # Show the activation link so support can get the activation link if necessary.
            auth_fields.append("user_activation_link")

        return (
            (None, {"fields": auth_fields}),
            (_("Personal info"), {"fields": ("name", "first_name", "last_name")}),
            (
                _("Permissions"),
                {
                    "fields": (
                        "is_active",
                        "is_staff",
                        "is_superuser",
                        "groups",
                        "user_permissions",
                        "warehouse_access",
                        "company_access",
                    ),
                },
            ),
            (
                _("Important dates"),
                {
                    "fields": (
                        "last_login",
                        "date_joined",
                        "first_login_date_after_onboarding_page",
                    ),
                },
            ),
        )

    def user_change_password(self, request, id, **kwargs) -> HttpResponse:
        user = self.get_object(request, id)
        if can_manage_groups_only(request, user):
            raise PermissionDenied()
        return super().user_change_password(request, id, **kwargs)

    def get_readonly_fields(self, request, obj=None):
        if can_manage_groups_only(request, obj):
            # Everything readonly except the groups
            return [field.name for field in User._meta.get_fields() if field.name not in ["groups"]] + [
                "user_activation_link",
                "is_activated",
            ]
        return super().get_readonly_fields(request, obj)

    def has_change_permission(self, request, obj=None) -> bool:
        return request.user.has_perm("core.change_user") or can_manage_groups_only(
            request,
            obj,
        )

    def get_search_results(self, request, queryset, search_term):
        return super().get_search_results(request, queryset, search_term)

    @admin.display(description="Activation Link")
    def user_activation_link(self, obj: User):
        if obj.is_customer():
            return obj.customer.get_create_account_url()
        return obj.get_user_activation_url()

    @admin.display(description="Is Activated", boolean=True)
    def is_activated(self, obj: User):
        return obj.is_user_activation_done()

    @admin.action(
        description="Send activation email",
        permissions=["send_activation_link"],
    )
    def send_activation_link(self, request, queryset):
        if queryset.count() > 10:
            self.message_user(request, "Too many users selected", messages.ERROR)
            return

        count, already_done, app_users, account_creation = 0, 0, 0, 0
        for user in queryset.all():
            user: User
            if not user.is_user_activation_done():
                if user.activation_status == User.ActivationStatus.APP_USER:
                    app_users += 1
                elif user.activation_status == User.ActivationStatus.ACTIVATED_FROM_ACCOUNT_CREATION_FLOW:
                    account_creation += 1
                else:
                    user.send_user_activation_email()
                    count += 1
            else:
                already_done += 1

        if count > 0:
            self.message_user(
                request,
                f"Sent activation email to {count} users",
                messages.SUCCESS,
            )
        if already_done > 0:
            self.message_user(
                request,
                f"Skipped sending activation email to {already_done} users, as they were already activated",
                messages.WARNING,
            )
        if app_users > 0:
            self.message_user(
                request,
                f"Skipped sending activation email to {app_users} users,"
                " as app users are managed by the customer. If the app user has been"
                " converted to a customer, you can use the 'send activation link' action on"
                " the customer.",
                messages.WARNING,
            )
        if account_creation > 0:
            self.message_user(
                request,
                f"Skipped sending activation email to {account_creation} users,"
                " as the user is using account creation flow. Use the 'send activation link' on"
                " the customer to resend the account creation activation email",
                messages.WARNING,
            )

    def has_send_activation_link_permission(self, request, obj=None):
        return request.user.has_perm("core.view_user")


@admin.register(UserAccountAccess, site=unfold_admin_site)
class UserAccountAccessAdmin(SimpleHistoryAdmin, UnfoldModelAdmin):
    list_display = ["user", "customer", "type", "is_accepted", "created_at"]
    search_fields = ["user__email", "user__name", "customer__user_no", "customer__user__email", "customer__user__name"]
    autocomplete_fields = ["user", "customer"]
    ordering = ["-created_at"]
    date_hierarchy = "created_at"
    list_filter = (
        UserFilter,
        CustomerFilter,
        "type",
        "is_accepted",
    )

    def get_search_results(self, request, queryset, search_term: str):
        if "autocomplete" in request.path:
            # Reduce number of queries on autocomplete lookups
            queryset = queryset.select_related("user", "customer")
        return super().get_search_results(request, queryset, search_term)


def can_manage_groups_only(request, obj=None):
    """Checks if the user has the special manage_groups permissions that allow a user without
    the normal edit user permission to edit a users group.

    This is done to allow e.g support to add users to groups.
    """
    if request.user.has_perm("core.change_user"):
        # The user can change the user, not limited to groups only
        return False
    if not request.user.has_perm("core.manage_groups"):
        return False
    # Allow edit of groups for non staff users
    # This will also not allow the user to edit him self, as a user
    # with access to the admin will also be staff.
    return obj is None or not (obj.is_staff or obj.is_superuser)


@admin.register(PricePlan, site=unfold_admin_site)
class PricePlanAdmin(ModelAdminBase, UnfoldModelAdmin):
    list_display = ("name", "product", "get_type_display", "sku", "usage_sku")
    exclude = ("countries",)
    search_fields = [
        "name",
        "sku",
    ]
    inlines = [PricePlanPricingInline]


@admin.register(PricePlanPrice, site=unfold_admin_site)
class PricePlanPriceAdmin(ModelAdminBase, SimpleHistoryAdmin, UnfoldModelAdmin):
    formfield_override_by_name = {
        "countries": {"widget": UnfoldSelect2MultipleAdminWidget},
    }

    list_display = ("price_plan", "annual", "usage")
    list_filter = ("price_plan__product", "price_plan__type", "price_plan")
    search_fields = ["price_plan"]


@admin.register(SubscriptionChange, site=unfold_admin_site)
class SubscriptionChangeAdmin(SimpleHistoryAdmin, UnfoldModelAdmin):
    list_display = (
        "created_at",
        "customer_link",
        "price_plan",
        "quantity_delta",
        "origin",
        "start_date",
        "end_date",
    )
    list_filter = (
        CustomerFilter,
        ("start_date", RangeDateFilter),
        ("end_date", RangeDateFilter),
        "price_plan",
        "origin",
        SalesOrderFilter,
        "customer__nofence_company",
        "customer__country",
    )
    search_fields = [
        "customer__visma_id",
        "customer__name",
        "customer__hubspot_id",
        "visma_document_id",
        "visma_sales_order_id",
    ]
    autocomplete_fields = ["customer", "invoice", "sales_order"]
    actions = ["export_subscription_changes"]

    fields = (
        "customer",
        ("start_date", "end_date"),
        ("price_plan", "quantity_delta"),
        "origin",
        "hubspot_id",
        "visma_document_id",
        "visma_sales_order_id",
        "sales_order",
        "invoice",
        "note",
    )

    def customer_link(self, obj):
        url = reverse("admin:core_customer_change", args=(obj.customer.pk,))
        return format_html("<a href='{}'>{}</a>", url, obj.customer)

    @admin.action(description="Download subscription changes")
    def export_subscription_changes(self, request, queryset):
        def row_generator(queryset):
            buffer = csv_export.CsvEchoWriter()
            writer = csv.writer(buffer)
            yield writer.writerow(
                [
                    "price_plan",
                    "customer",
                    "country",
                    "email",
                    "tlf",
                    "mob",
                    "company",
                    "quantity",
                    "origin",
                    "start",
                    "end",
                ],
            )

            for sub in queryset:
                yield writer.writerow(
                    [
                        sub.price_plan,
                        sub.customer,
                        sub.customer.country,
                        sub.customer.email,
                        sub.customer.phone_number,
                        sub.customer.phone_number_mobile,
                        sub.customer.nofence_company,
                        sub.quantity_delta,
                        sub.origin,
                        sub.start_date,
                        sub.end_date,
                    ],
                )

        return StreamingHttpResponse(
            row_generator(queryset),
            content_type="text/csv",
            headers={"Content-Disposition": 'attachment; filename="subscriptions.csv"'},
        )

    customer_link.admin_order_field = "customer"
    customer_link.short_description = "customer"


class SubscriptionChangeActiveForm(ModelFormBase):
    class Meta:
        model = SubscriptionChange
        fields = "__all__"

    def clean(self):
        cleaned_data = super().clean()
        start_date = cleaned_data.get("start_date")
        end_date = cleaned_data.get("end_date")
        today = date.today()

        if start_date and start_date > today:
            raise ValidationError("Start date must be today or earlier for active subscriptions.")
        if end_date and end_date < today:
            raise ValidationError("End date must be today or later for active subscriptions.")
        return cleaned_data


class SubscriptionChangeFutureForm(ModelFormBase):
    class Meta:
        model = SubscriptionChange
        fields = "__all__"

    def clean(self):
        cleaned_data = super().clean()
        start_date = cleaned_data.get("start_date")
        today = date.today()

        if start_date and start_date <= today:
            raise ValidationError("Start date must be in the future for future subscriptions.")
        return cleaned_data


class SubscriptionChangeActiveInline(TabularInlineBase):
    model = SubscriptionChange
    form = SubscriptionChangeActiveForm
    fields = ["start_date", "end_date", "price_plan", "origin", "quantity_delta"]
    ordering = ["-end_date", "-start_date"]
    verbose_name = "Subscription Change"
    verbose_name_plural = "Active Subscription Changes"
    autocomplete_fields = ["price_plan"]

    def get_queryset(self, request: HttpRequest) -> QuerySet[SubscriptionChange]:
        return (
            super()
            .get_queryset(request)
            .filter(
                start_date__lte=date.today(),
                end_date__gte=date.today(),
            )
            .select_related("price_plan", "customer")
        )


class SubscriptionChangeInactiveInline(TabularInlineBase):
    model = SubscriptionChange
    fields = ["start_date", "end_date", "price_plan", "origin", "quantity_delta"]
    ordering = ["-end_date", "-start_date"]
    max_num = 0
    classes = ["collapse"]
    verbose_name = "Subscription Change"
    verbose_name_plural = "Expired Subscription Changes"
    autocomplete_fields = ["price_plan"]

    def get_queryset(self, request: HttpRequest) -> QuerySet[SubscriptionChange]:
        return (
            super()
            .get_queryset(request)
            .filter(
                end_date__lt=date.today(),
            )
            .select_related("price_plan", "customer")
        )


class SubscriptionChangeFutureInline(TabularInlineBase):
    model = SubscriptionChange
    form = SubscriptionChangeFutureForm
    fields = ["start_date", "end_date", "price_plan", "origin", "quantity_delta"]
    ordering = ["-end_date", "-start_date"]
    verbose_name = "Subscription Change"
    verbose_name_plural = "Future Subscription Changes"
    autocomplete_fields = ["price_plan"]

    def get_queryset(self, request: HttpRequest) -> QuerySet[SubscriptionChange]:
        return (
            super()
            .get_queryset(request)
            .filter(
                start_date__gt=date.today(),
            )
            .select_related("price_plan", "customer")
        )


class InvoiceInline(TabularInlineBase):
    model = Invoice
    fields = [
        "id",
        "state",
        "created_at",
        "invoice_provider",
        "invoice_provider_id",
        "invoice_provider_ref",
        "sum_total",
    ]
    ordering = ["id"]
    show_change_link = True
    readonly_fields = ["sum_total", "created_at"]

    def get_queryset(self, request):
        return super().get_queryset(request).select_related("customer").prefetch_related("invoice_lines")


class SalesOrderInline(TabularInlineBase):
    model = SalesOrder
    fields = ["id", "status", "created_at", "source", "source_id", "sum_total"]
    ordering = ["-id"]
    show_change_link = True
    readonly_fields = ["sum_total", "created_at"]

    def has_delete_permission(self, request, obj=None):
        return False

    def get_queryset(self, request):
        return super().get_queryset(request).prefetch_related("lines")


class ShipmentInline(TabularInlineBase):
    model = Shipment
    fields = [
        "shipment_no",
        "status",
        "delivered_date",
        "transporter",
        "tracking_number",
    ]
    show_change_link = True
    ordering = ["-id"]

    def has_delete_permission(self, request, obj=None):
        return False


class CollarInline(TabularInlineBase):
    model = Collar
    fields = ["serial_no", "product"]
    show_change_link = True


class CustomerCompanyAddressInline(TabularInlineBase):
    model = CustomerCompanyAddress


class CustomerDeliveryAddressInline(TabularInlineBase):
    model = CustomerDeliveryAddress


class CustomerInvoiceAddressInline(TabularInlineBase):
    model = CustomerInvoiceAddress


class NofenceCompanyAddressInline(TabularInlineBase):
    model = NofenceCompanyAddress


class InvoiceDocumentInline(ImmutableMixin, TabularInlineBase):
    template = "admin/inlines/invoice-document.html"
    verbose_name = "Payment status"
    verbose_name_plural = "Payment status"
    model = InvoiceDocument
    fields = [
        "reference_number",
        "provider",
        "type",
        "status",
        "total",
        "balance",
        "due_date",
    ]
    readonly_fields = ["reference_number"]
    ordering = ["-id"]
    show_change_link = True

    media = forms.Media(css={"all": ["admin/css/invoice-documents-inline.css"]})


class SupportTicketInline(ImmutableMixin, TabularInlineBase):
    verbose_name = "Support Ticket"
    verbose_name_plural = "Support Tickets"
    model = SupportTicket
    fields = ["hubspot_id", "subject", "priority", "ticket_created_at", "hubspot_link"]
    readonly_fields = ["hubspot_link"]
    ordering = ["-ticket_created_at"]
    show_change_link = True


class CustomerEmailInline(TabularInlineBase):
    verbose_name = "Additional Email"
    verbose_name_plural = "Additional Emails"
    model = CustomerEmail
    fields = ["email_type", "email_address"]
    ordering = ["email_type", "email_address"]


class CustomerVoucherInline(TabularInlineBase):
    verbose_name = "Voucher"
    verbose_name_plural = "Vouchers"
    model = CustomerVoucher
    fields = ["voucher", "quantity", "status", "created_at"]
    readonly_fields = ["created_at"]
    ordering = ["-created_at"]
    autocomplete_fields = ["voucher"]


class CustomerFailedSyncListFilter(SimpleListFilter):
    title = "Failed Sync"
    parameter_name = "failed_sync"

    def lookups(self, request, model_admin):
        return (("YES", "Yes"),)

    def queryset(self, request, queryset):
        if self.value() == "YES":
            return queryset.filter(
                Exists(
                    SyncStatus.objects.filter(
                        Q(customer=OuterRef("pk"), status=SyncStatus.Status.FAILED),
                    ),
                ),
            )


class CustomerWithActiveVoucherFilter(SimpleListFilter):
    title = "Active Voucher"
    parameter_name = "active_voucher"

    def lookups(self, request, model_admin):
        return (("YES", "Yes"),)

    def queryset(self, request, queryset):
        if self.value() == "YES":
            return queryset.filter(
                Exists(
                    CustomerVoucher.objects.filter(
                        Q(customer=OuterRef("pk"), status=CustomerVoucher.Status.ACTIVE),
                    ),
                ),
            )


class CustomerPowerProfileFilter(SimpleListFilter):
    title = "Power profile"
    parameter_name = "profile"

    def lookups(self, request, model_admin):
        return (
            ("DEFAULT", "DEFAULT"),
            ("NIGHT_MODE", "NIGHT_MODE"),
            ("NO_LISTENING_SOCKET", "NO_LISTENING_SOCKET"),
        )

    def queryset(self, request, queryset):
        if self.value():
            ids = UserDocument.objects.filter(
                power_profile__profile=self.value(),
            ).values_list("user_no")
            return queryset.filter(user_no__in=ids)


class CustomerAccountingSystemInline(TabularInlineBase):
    model = CustomerAccountingSystem
    autocomplete_fields = ["nofence_company"]


@admin.register(Customer, site=unfold_admin_site)
class CustomerAdmin(
    InlineActionsMixin,
    SendMassEmailAction,
    SimpleHistoryAdmin,
    UnfoldModelAdmin,
):
    list_display = [
        "name",
        "primary_contact",
        "user",
        "user_no",
        "business_reg_no_short",
        "country_name",
        "nofence_company",
        "show_hubspot_url",
        "visma_prepaid_id",
        "customer_status",
        "impersonate_link",
    ]
    advanced_filter_fields = [
        "country",
        "created_at",
        "customer_status",
        "business_reg_no",
        "customer_type",
    ]
    list_filter = [
        "country",
        "customer_status",
        "nofence_company",
        CattleListFilter,
        SheepGoatListFilter,
        ("shipments__delivered_date", RangeDateFilter),
        "owned_collars__model",
        OwnedCollarTypeFilter,
        "user__is_active",
        "user__is_staff",
        "automatic_invoice",
        "billing_period_preference",
        ("customer_type", MultiSelectFieldListFilter),
        ("visma_id", EmptyFieldListFilter),
        ("visma_prepaid_id", EmptyFieldListFilter),
        ("business_reg_no", EmptyFieldListFilter),
        ("nofence_contact", EmptyFieldListFilter),
        PricePlanPrefListFilter,
        NofenceContactFilter,
        CustomerWithOpenInvoiceFilter,
        CustomerWithOverdueInvoiceFilter,
        CustomerOverdueBalanceFilter,
        "user__groups",
        CustomerWithCollarsHavingModemUpgradeStatus,
        CustomerWithCollarsModemUpgradeStatusChanged,
        # "id" is used as dummy value, because django did not allow a field that does not exists
        # on the customer admin to be used. The field value is overridden in the filter classes.
        ("id", CustomerWithCollarModemDownloadRestarts),
        ("id", CustomerWithCollarModemUpgradeRestarts),
        CustomerFailedSyncListFilter,
        "fake_door_links",
        "is_part_of_debt_collection_flow",
        CustomerWithDirectDebitFilter,
        "is_nofence_company_shipment_receiver",
        "customer_relationship_terminated",
        CustomerWithActiveVoucherFilter,
        CustomerPowerProfileFilter,
    ]
    ordering = ["-id"]
    search_fields = [
        "visma_id",
        "visma_prepaid_id",
        "name",
        "user__email",
        "user__name",
        "user_no",
        "hubspot_id",
        "xero_id",
        "business_reg_no",
    ]
    djangoql_completion_enabled_by_default = False
    autocomplete_fields = ["nofence_contact", "nofence_company"]
    date_hierarchy = "created_at"
    actions = [
        "download_overuse_csv",
        "download_customer_info",
        "send_app_notification_message",
        "trigger_resync_from_hubspot",
        "trigger_sync_to_hubspot",
        "generate_overuse_and_usage_invoices",
        "run_collar_summarizer",
        "start_data_export",
        "sync_subscription_to_hubspot",
        "export_invoice_info",
        "send_payment_reminders",
        "send_mass_email",
        "run_price_level_notification_check",
        "run_price_cap_notification_check",
        "invoice_flexible_usage",
        "create_mynofence_user",
        "toggle_data_export_permission",
        "add_fake_door_link",
        "edit_user_document",
        "enable_store_access",
        "disable_store_access",
        "enable_referrals",
        "resend_activation_link",
        "run_vies_check",
        "add_customers_to_questionnaire",
        "reset_customer_response_to_questionnaire",
        "change_nofence_company",
    ]

    actions_detail = [
        "login_as",
        "go_to_hubspot",
        "collars_owned",
        "trigger_resync_from_hubspot",
        "trigger_sync_to_hubspot",
        "find_email_records",
        "edit_user_document",
        "edit_billy_user",
    ]

    actions_row = [
        "find_email_records",
        "trigger_resync_from_hubspot",
        "trigger_sync_to_hubspot",
        "resend_activation_link",
        "create_stripe_customer_portal_session",
    ]

    fieldsets = (
        (
            None,
            {
                "fields": (
                    "is_active",
                    "user_no",
                    "name",
                    "user",
                    "country",
                    "nofence_company",
                    "customer_type",
                    "customer_status",
                    "has_store_access",
                    "store_access_disabled",
                    "referrals_enabled",
                    "phone_number",
                    "phone_number_formatted",
                    "phone_number_mobile",
                    "phone_number_mobile_formatted",
                    "sms_consent",
                    "invoice_contact_email",
                    "delivery_contact_email",
                    ("invoice_requester_first_name", "invoice_requester_last_name"),
                    "purchase_order_flow",
                    ("sync_status_link", "current_sync_status"),
                    "is_part_of_debt_collection_flow",
                    "is_allowed_to_buy_few_collars",
                    "can_use_direct_debit",
                    "customer_relationship_terminated",
                    "customer_gdpr",
                    "confirmed_no_interest",
                    "customer_status_events",
                    "initiated_contact",
                    "first_contact_initiated_at",
                ),
            },
        ),
        (
            "Links",
            {
                "fields": (
                    "collar_owner_list",
                    "impersonate_link2",
                ),
            },
        ),
        (
            "Reference IDs",
            {
                "fields": (
                    "business_reg_no",
                    "eori_number",
                    "flat_rate_scheme",
                    "visma_id",
                    "visma_prepaid_id",
                    ("hubspot_id", "show_hubspot_url"),
                    "xero_id",
                    "go_cardless_id",
                    "stripe_id",
                    "stripe_default_payment_method",
                ),
            },
        ),
        (
            "Billing",
            {
                "fields": (
                    "billing_period_preference",
                    "approved_for_post_payment",
                    "approved_for_post_payment_by",
                    "automatic_invoice",
                    "automatic_invoice_note",
                    "nofence_contact",
                ),
            },
        ),
        (
            _("Vies Data"),
            {
                "fields": ("vies_request_payload", "vies_response", "vies_valid"),
                "classes": ["collapse in"],
            },
        ),
    )

    custom_add_form = CreateCustomerForm
    fieldsets_add_form = (
        (None, {"fields": ["name", "email", "country"]}),
        (_("my.nofence user"), {"fields": ["create_my_nofence_user"]}),
        (
            None,
            {
                "fields": [],
                "description": _(
                    "<i style='background: var(--message-warning-bg)'> "
                    "If you are adding a real customers manually through the admin, make sure you add "
                    "the necessary link IDs to the external systems (hubspot_id, xero_id ect.) afterwards "
                    "so that the integration with these systems will work correctly.",
                ),
            },
        ),
    )

    inlines = [
        CustomerAccountingSystemInline,
        CustomerCompanyAddressInline,
        CustomerDeliveryAddressInline,
        CustomerInvoiceAddressInline,
        CustomerEmailInline,
        InvoiceDocumentInline,
        SubscriptionChangeActiveInline,
        SubscriptionChangeFutureInline,
        SubscriptionChangeInactiveInline,
        CustomerVoucherInline,
        InvoiceInline,
        SalesOrderInline,
        SupportTicketInline,
        # ShipmentInline,  # hiding this for now, it's only HAPRO shipments so will give a confusing view
    ]

    @cache
    def get_country_name(self, code: str) -> str:
        from django_countries import countries

        return countries.name(code)

    @admin.display(description="Country", ordering="country")
    def country_name(self, obj: Customer):
        return self.get_country_name(obj.country.code)

    @admin.display(description="Primary Contact")
    def primary_contact(self, obj: Customer):
        return obj.user.name

    def filter_customers_for_mass_email(self, queryset):
        return queryset.all()

    def get_actions_detail(self, request, object_id=None):
        actions = self.actions_detail.copy()
        customer: Customer = self.get_object(request, object_id)
        if not customer.hubspot_id:
            actions.remove("go_to_hubspot")
            actions.remove("trigger_resync_from_hubspot")
        if customer.get_billable_owned_collar_count() == 0:
            actions.remove("collars_owned")
        if not customer.user_no:
            actions.remove("edit_user_document")
        if not (customer.user.is_user_activation_done() and customer.user.is_active_with_password()):
            actions.remove("login_as")
        if not customer.emailrecord_set.exists():
            actions.remove("find_email_records")
        return self._filter_unfold_actions_by_permissions(
            request,
            [self.get_unfold_action(action) for action in actions],
            object_id,
        )

    def changeform_view(self, request, object_id=None, form_url="", extra_context=None):
        if object_id is not None:
            # Add some extra context used by the invoice-document inline
            obj = self.get_object(request, object_id)
            extra_context = extra_context or {}
            extra_context["payment_status_balance"] = obj.invoice_documents.total_balance_sums()
            extra_context["payment_status_overdue"] = obj.invoice_documents.is_overdue().total_balance_sums()
        return super().changeform_view(request, object_id, form_url, extra_context)

    def get_search_results(self, request, queryset, search_term):
        """
        try and detect a list of collar_nos and do a search on all of them
        so we can do batch operations
        """

        def is_visma_id(value: str) -> bool:
            # Currently the visma ids seems to be 5 characters
            return value.isnumeric() and len(value) in [5, 6]

        search_term_list = []
        if search_term.replace(" ", "").isnumeric():
            for word in search_term.split(" "):
                if word.strip().isnumeric():
                    search_term_list.append(int(word))

            # Extra check for visma ids to make sure searches for hubspot_ids will work
            if all(is_visma_id(str(word)) for word in search_term_list):
                queryset = Customer.objects.filter(
                    Q(visma_id__in=search_term_list) | Q(visma_prepaid_id__in=search_term_list),
                )
                return queryset, False

        return super(CustomerAdmin, self).get_search_results(
            request,
            queryset,
            search_term,
        )

    def get_form(self, request, obj=None, change=False, **kwargs):
        """
        We need a special form when creating a customer from the admin,
        because that the associated user model must also be created.
        """
        defaults = {}
        if not change:
            defaults["form"] = self.custom_add_form
        defaults.update(kwargs)
        return super().get_form(request, obj, change, **defaults)

    def save_model(self, request, obj, form, change):
        # We only need to save the model if when update an existing customer, because
        # the custom add form will save and commit the model. Nothing bad happens if
        # we save it twice, but we get an unnecessary entry in the history
        if change:
            # Adds the updated fields to a property on the model
            obj._update_fields = form.changed_data
            return super().save_model(request, obj, form, change)

    def get_fieldsets(self, request, obj=None):
        if obj is None:
            return self.fieldsets_add_form
        fieldsets = super().get_fieldsets(request, obj=obj)

        if request.user.is_superuser:
            return fieldsets + (
                (
                    "Internal Values",
                    {
                        "fields": (
                            "price_plan_level_notification_last_collar_count",
                            "price_cap_notification_last_reached_caps",
                            "is_nofence_company_shipment_receiver",
                            "can_use_card_payment",
                        ),
                    },
                ),
            )

        return fieldsets

    def get_readonly_fields(self, request, obj=None):
        if obj is None:
            # Hide readonly fields when creating a new user
            return [
                "customer_relationship_terminated",
            ]

        readonly_fields = [
            "show_hubspot_url",
            "is_active",
            "user",
            "collar_owner_list",
            "sync_status_link",
            "current_sync_status",
            "impersonate_link2",
            "customer_status_events",
            "vies_valid",
            "phone_number_formatted",
            "phone_number_mobile_formatted",
            "approved_for_post_payment_by",
        ]

        # Be strict on who gets to set this field
        if not request.user.is_superuser:
            readonly_fields += ["customer_relationship_terminated"]

        if not request.user.has_perm("core.set_order_post_payment"):
            readonly_fields += ["approved_for_post_payment"]

        # The nofence_company should normally not be editable by normal users, as changing the company
        # for exiting customer could potentially break stuff, but enable it for everyone temporary
        # for testing purposes.
        if not request.user.is_superuser:
            readonly_fields.append("nofence_company")

        return readonly_fields

    def get_inlines(self, request, obj=None):
        if obj is None:
            # Hide inlines when creating a new user
            return []
        return super().get_inlines(request, obj=obj)

    def response_add(self, request, obj, post_url_continue=None):
        # Taken from django.contrib.auth.admin.UserAdmin
        if "_addanother" not in request.POST and IS_POPUP_VAR not in request.POST:
            request.POST = request.POST.copy()
            request.POST["_continue"] = 1
        return super().response_add(request, obj, post_url_continue=post_url_continue)

    def show_hubspot_url(self, obj):
        if obj.hubspot_id:
            return format_html(
                "<a href='{url}'>{hubspot_id}</a>",
                hubspot_id=obj.hubspot_id,
                url=f"https://app.hubspot.com/contacts/{settings.HUBSPOT_PORTAL_ID}/company/{obj.hubspot_id}",
            )
        return "-"

    show_hubspot_url.short_description = "Hubspot"

    def business_reg_no_short(self, obj):
        return obj.business_reg_no

    business_reg_no_short.short_description = "Business No."

    def collar_owner_list(self, obj):
        url_owner = reverse("admin:core_collar_changelist") + "?" + urlencode({"owner__pk__exact": obj.id})
        return format_html(
            "<a href='{url_owner}'>Collars owned by customer</a>",
            url_owner=url_owner,
        )

    collar_owner_list.short_description = "Collars"

    def customer_status_events(self, obj: Customer):
        status_events: List[CustomerStatusEvent] = obj.status_events.order_by("status_changed_at")

        output = ["<table>"]
        output.append("<tr><th>Changed at</th><th>Status</th><th>Reason</th></tr>")
        for to_event in status_events:
            output.append(
                format_html(
                    "<tr><td>{}</td><td>{}</td><td>{}</td></tr>",
                    to_event.created_at.strftime("%Y-%m-%dT%H:%M:%S"),
                    to_event.get_status_display(),
                    to_event.change_reason if to_event.change_reason else "-",
                ),
            )
        output.append("</table>")
        return mark_safe("".join(output))

    @admin.display(description="Sync Status")
    def sync_status_link(self, obj):
        url = reverse("admin:core_syncstatus_changelist") + "?" + urlencode({"customer__pk__exact": obj.id})
        return format_html("<a href='{}'>Sync Status</a>", url)

    @admin.display(description="Current", boolean=True)
    def current_sync_status(self, obj):
        qs = SyncStatus.objects.filter(customer=obj)
        if qs.exists():
            return not qs.filter(status=SyncStatus.Status.FAILED).exists()
        return None

    @unfold_action(description="Edit billy user", attrs={"ext_link": True, "target": "_blank"})
    def edit_billy_user(self, request, object_id):
        if not request.user.has_perm("core.change_user") or not request.user.has_perm("core.manage_groups"):
            self.message_user(request, "You don't have permission to edit billy user", messages.ERROR)
            return redirect(self.get_redirect_url_for_inline_action(request, object_id))

        customer = Customer.objects.filter(id=object_id).first()

        if customer is not None and customer.user:
            return redirect(reverse("admin:core_user_change", args=[customer.user.id]))

        self.message_user(request, "User for customer not found", messages.ERROR)
        return redirect(self.get_redirect_url_for_inline_action(request, object_id))

    @unfold_action(
        description="Edit MongoDB User",
        permissions=["edit_user_doc"],
        attrs={"ext_link": True, "target": "_blank"},
    )
    def edit_user_document(self, request, queryset=None, object_id=None):
        if object_id:
            queryset = self.get_queryset(request).filter(id=object_id)

        if queryset.count() > 1:
            self.message_user(request, "Only one user can be edited at a time", messages.ERROR)
            return redirect(request.get_full_path())

        customer: Customer = queryset.first()

        user_doc: UserDocument = customer.get_user_document()

        if user_doc is None:
            self.message_user(request, "Found no MongoDB user linked to customer", messages.ERROR)
            return redirect(self.get_redirect_url_for_inline_action(request, customer.id))

        template = "admin/actions/edit_user_document.html"

        if "cancel" in request.POST:
            return redirect(self.get_redirect_url_for_inline_action(request, customer.id))
        elif "save_user" in request.POST:
            form = mongo_forms.get_user_document_form(request, user_doc)
            if form.is_valid():
                form.save()
                self.message_user(request, "Updated MongoDB user", messages.SUCCESS)
                return redirect(self.get_redirect_url_for_inline_action(request, customer.id))
        else:
            form = mongo_forms.get_user_document_form(request, user_doc, initial_form=True)

        opts = self.model._meta

        context = {
            **self.admin_site.each_context(request),
            "title": "Edit MongoDB User",
            "subtitle": "",
            "form": form,
            "opts": opts,
            "app_label": opts.app_label,
            "selected_objects": [customer],
            **self.get_inline_action_context(request),
        }

        return render(request, template, context=context)

    def has_edit_user_doc_permission(self, request, obj=None):
        return request.user.has_perm("core.change_customer")

    @unfold_action(description="Login As", attrs={"ext_link": True, "target": "_blank"})
    def login_as(self, request, queryset=None, object_id=None):
        customer = self.get_object(request, object_id)
        url = reverse("impersonate-auto-stop", args=[customer.user_id])
        return redirect(url)

    @unfold_action(description="Go to Hubspot", attrs={"ext_link": True, "target": "_blank"})
    def go_to_hubspot(self, request, queryset=None, object_id=None):
        customer = self.get_object(request, object_id)
        if customer.hubspot_id:
            url = f"https://app.hubspot.com/contacts/{settings.HUBSPOT_PORTAL_ID}/company/{customer.hubspot_id}"
            return redirect(url)
        else:
            self.message_user(request, "Customer has no Hubspot ID", messages.ERROR)
            return redirect(self.get_redirect_url_for_inline_action(request, object_id))

    @unfold_action(description="Collars owned", attrs={"ext_link": True, "target": "_blank"})
    def collars_owned(self, request, queryset=None, object_id=None):
        customer: Customer = self.get_object(request, object_id)
        url = reverse("admin:core_collar_changelist") + "?" + urlencode({"owner__pk__exact": customer.id})
        return redirect(url)

    @unfold_action(
        description="Stripe Customer Portal",
        attrs={"use_post_request": True, "target": "_blank"},
        permissions=["stripe_customer_portal"],
    )
    def create_stripe_customer_portal_session(self, request, queryset=None, object_id=None):
        customer: Customer = self.get_object(request, object_id)

        return_url = request.build_absolute_uri(reverse("admin:core_customer_changelist"))
        filters = request.GET.get("_changelist_filters", "")
        if filters:
            return_url += "?" + filters

        if not customer.is_card_payment_enabled_for_invoices():
            messages.add_message(request, messages.ERROR, "Card payments not enabled for customer")
            return redirect(return_url)

        link = create_stripe_card_payment_service().create_customer_portal_session_link_for_customer(
            customer,
            return_url=return_url,
        )
        return redirect(link)

    def has_stripe_customer_portal_permission(self, request, obj=None):
        return request.user.has_perm("core.change_stripepaymentmethod")

    @admin.action(description="Download customer information")
    def download_customer_info(self, request, queryset):
        class Echo:
            """An object that implements just the write method of the file-like interface."""

            def write(self, value):
                """Write the value by returning it, instead of storing in a buffer."""
                return value

        def row_generator(queryset):
            pseudo_buffer = Echo()
            writer = csv.writer(pseudo_buffer)

            yield writer.writerow(self.export_customer_headers())

            for customer in queryset:
                yield writer.writerow(self.export_customer(customer))

        return StreamingHttpResponse(
            row_generator(queryset),
            content_type="text/csv",
            headers={
                "Content-Disposition": 'attachment; filename="customer_information.csv"',
            },
        )

    def export_customer_headers(self):
        return [
            "visma_id",
            "visma_prepaid_id",
            "xero_id",
            "user_no",
            "hubspot_id",
            "status",
            "type",
            "app_created_at",
            "name",
            "contact",
            "email",
            "phone",
            "mobile",
            "company_address1",
            "company_address2",
            "company_zip_code",
            "company_city",
            "country",
            "region",
            "automatic_invoice",
            "nofence_company",
            "sg_collars",
            "c_collars",
        ]

    def export_customer(self, customer: Customer):
        sg_collars = customer.get_billable_owned_collar_count(SHEEP_GOAT)
        c_collars = customer.get_billable_owned_collar_count(CATTLE)

        company_address1 = None
        company_address2 = None
        company_zip_code = None
        company_region = None
        company_address_city = None
        if customer.company_address_value:
            company_address1 = customer.company_address.address1
            company_address2 = customer.company_address.address2
            company_zip_code = customer.company_address.zip_code
            company_region = customer.company_address.region
            company_address_city = customer.company_address.city

        return [
            customer.visma_id,
            customer.visma_prepaid_id,
            customer.xero_id,
            customer.user_no,
            customer.hubspot_id,
            customer.customer_status,
            customer.customer_type,
            customer.get_nofence_account_created_at(),
            customer.name,
            customer.user.name,
            customer.email,
            customer.phone_number,
            customer.phone_number_mobile,
            company_address1,
            company_address2,
            company_zip_code,
            company_address_city,
            customer.country,
            company_region,
            customer.automatic_invoice,
            customer.nofence_company,
            sg_collars,
            c_collars,
        ]

    def impersonate_link(self, customer: Customer):
        url = reverse("impersonate-auto-stop", args=[customer.user_id])
        return format_html('<a href="{}">Log in</a>', url)

    impersonate_link.short_description = "Log in as"
    impersonate_link.allow_tags = True

    def impersonate_link2(self, customer: Customer):
        url = reverse("impersonate-auto-stop", args=[customer.user_id])
        return format_html('<a href="{}">Log in to account pages</a>', url)

    impersonate_link2.short_description = "Log in as"
    impersonate_link2.allow_tags = True

    @admin.action(description="Run usage summarizer for customers collars")
    def run_collar_summarizer(self, request, queryset):
        summarizer = UsageSummarizer()

        for customer in queryset:
            summarizer.process_customer(customer)

        self.message_user(
            request,
            f"run usage summarizer for {len(queryset)} customers(s)",
            messages.SUCCESS,
        )

    @admin.action(description="Enable store access for testing")
    def enable_store_access(self, request, queryset):
        for customer in queryset:
            customer.has_store_access = True
            customer.save()

        self.message_user(
            request,
            f"Enabled store access for {len(queryset)} customers(s)",
            messages.SUCCESS,
        )

    @admin.action(description="Disable store access")
    def disable_store_access(self, request, queryset):
        for customer in queryset:
            customer.has_store_access = False
            customer.save()

        self.message_user(
            request,
            f"Disabled store access {len(queryset)} customers(s)",
            messages.SUCCESS,
        )

    def has_disable_store_access_permission(self, request, obj=None):
        return request.user.has_perm("core.change_customer")

    def has_enable_store_access_permission(self, request, obj=None):
        return request.user.has_perm("core.change_customer")

    @admin.action(description="Run Vies check")
    def run_vies_check(self, request, queryset):
        from core.services.vies_validation_service import VIESService

        vies_service = VIESService()
        failed_calls = 0
        for customer in queryset:
            try:
                vies_service.vies_check(customer)
            except Exception as e:
                failed_calls += 1
                logger.warn(f"VIES check failed with {e}")

        self.message_user(
            request,
            f"Ran Vies check for {len(queryset)} customers(s), failed calls: {failed_calls}",
            messages.SUCCESS,
        )

    def has_run_vies_check_permission(self, request, obj=None):
        return request.user.has_perm("core.change_customer")

    @unfold_action(
        description="Emails Records",
        permissions=["view_email_records"],
        attrs={"target": "_blank", "ext_link": True},
    )
    def find_email_records(self, request, object_id):
        return redirect(
            reverse("admin:core_emailrecord_changelist") + "?" + urlencode({"customer": object_id}),
        )

    def has_view_email_records_permission(self, request, obj=None):
        return request.user.has_perm("core.view_emailrecord")

    @unfold_action(
        description="Resend activation link",
        attrs={"use_post_request": True},
        permissions=["resend_activation_link"],
    )
    def resend_activation_link(self, request, queryset: QuerySet[Customer] = None, object_id=None):
        if object_id:
            queryset = self.get_queryset(request).filter(id=object_id)

        for customer in queryset:
            customer.send_account_activation_flow_email()

        self.message_user(
            request,
            f"Resent activation email to {len(queryset)} customers(s)",
            messages.SUCCESS,
        )

        if object_id:
            return redirect(self.get_redirect_url_for_inline_action(request, object_id))

    def has_resend_activation_link_permission(self, request, obj=None):
        return request.user.has_perm("core.change_customer")

    @admin.action(
        description="Toggle Data Export permission",
        permissions=["toggle_data_export"],
    )
    def toggle_data_export_permission(self, request, queryset):
        """Adds or removes customers from the Data Export group"""
        data_export_group = Group.objects.get_by_natural_key("Data Export")
        added, removed = 0, 0
        for customer in queryset:
            if customer.user.groups.filter(pk=data_export_group.pk).exists():
                customer.user.groups.remove(data_export_group)
                removed += 1
            else:
                customer.user.groups.add(data_export_group)
                added += 1

        if added:
            self.message_user(
                request,
                f"Gave {added} customer(s) data export permission",
                messages.SUCCESS,
            )
        if removed:
            self.message_user(
                request,
                f"Removed data export permission from {removed} customer(s)",
                messages.SUCCESS,
            )

    def has_toggle_data_export_permission(self, request, obj=None):
        return request.user.has_perm("core.change_customer")

    @unfold_action(description="Sync from HubSpot", attrs={"use_post_request": True})
    def trigger_resync_from_hubspot(self, request, queryset=None, object_id=None):
        if object_id:
            queryset = self.get_queryset(request).filter(id=object_id)

        missing_ids = []
        hubspot_ids = []
        for customer in queryset:
            if not customer.hubspot_id:
                missing_ids.append(customer.id)
            else:
                hubspot_ids.append((customer.id, customer.hubspot_id))

        for customer_id, hubspot_id in hubspot_ids:
            task = hubspot_tasks.sync_hubspot_company_to_billy.delay(
                hubspot_id,
                metadata={"trigger": "Manual trigger from admin"},
            )
            logger.info(
                f"Manually triggered hubspot company sync for customer {customer_id} in task {task.id}",
            )

        if missing_ids:
            self.message_user(
                request,
                f"The following {len(missing_ids)} customer(s) "
                "are missing hubspot_id and can't be re-synced "
                f"{', '.join(map(str, missing_ids))}",
                messages.WARNING,
            )

        if hubspot_ids:
            self.message_user(
                request,
                f"Hubspot sync triggered for {len(hubspot_ids)} customers(s)",
                messages.SUCCESS,
            )

        if object_id:
            return redirect(self.get_redirect_url_for_inline_action(request, obj_pk=object_id))

    @unfold_action(description="Sync to HubSpot", attrs={"use_post_request": True})
    def trigger_sync_to_hubspot(self, request, queryset=None, object_id=None):
        from core.integrations.hubspot_integration import receivers

        if object_id:
            queryset = self.get_queryset(request).filter(id=object_id)

        count = 0
        for customer in queryset:
            logger.info(f"Manually triggering sync of {customer=} to hubspot from admin")
            # Calling the receiver directly instead of sending the signal to not trigger any
            # uneccessary actions.
            receivers.on_billy_customer_data_changed(None, customer_id=customer.id)
            count += 1

        if count:
            self.message_user(
                request,
                f"Sync to hubspot triggered for {count} customers(s)",
                messages.SUCCESS,
            )

        if object_id:
            return redirect(self.get_redirect_url_for_inline_action(request, obj_pk=object_id))

    @admin.action(
        description="Send app notification message",
        permissions=["send_app_notifications"],
    )
    def send_app_notification_message(self, request, queryset, *args, **kwargs):
        template = ("admin/send_app_notification.html",)

        missing_userno = queryset.filter(Q(user_no="") | Q(user_no__isnull=True))
        valid_users = queryset.exclude(Q(user_no="") | Q(user_no__isnull=True))

        task_id = None

        if not hasattr(request.user, "customer") or not request.user.customer.user_no:
            self.message_user(
                request,
                "Can't send notifications without my.nofecne userNo. "
                "Your billy user is not associated with a nofence user or is missing nofence user_no.",
                messages.ERROR,
            )
            return
        elif valid_users.count() == 0:
            self.message_user(
                request,
                "All selected users are missing user_no. Can't send notifications.",
                messages.ERROR,
            )
            return
        elif "select_notification" in request.POST:
            template = ("admin/select_app_notification_message.html",)
            form = SelectNotificationMessageForm(request.POST)
            if form.is_valid():
                template = ("admin/send_app_notification_progress.html",)
                message = form.cleaned_data["message"]
                task = queue_notifications.delay(
                    request.user.id,
                    message.id,
                    [c.id for c in valid_users],
                )
                task_id = task.id
        else:
            template = ("admin/select_app_notification_message.html",)
            form = SelectNotificationMessageForm()

        opts = self.model._meta

        missing_count = missing_userno.count()
        skipped_title = ""
        if missing_count:
            self.message_user(
                request,
                f"{missing_count} selected user{pluralize(missing_count)} are missing user_no and "
                "will not receive notifications",
                messages.WARNING,
            )
            skipped_title = f" - {missing_count} user{pluralize(missing_count)} skipped"

        valid_count = valid_users.count()

        context = {
            **self.admin_site.each_context(request),
            "title": f"Send notification to {valid_count} app user{pluralize(valid_count)} {skipped_title}",
            "subtitle": "",
            "selected_objects": queryset,
            "form": form,
            "opts": opts,
            "app_label": opts.app_label,
            "task_id": task_id,
        }

        return render(request, template, context=context)

    def has_send_app_notifications_permission(self, request, obj=None):
        return request.user.has_perm("core.add_notificationmessage")

    @admin.action(
        description="Start data export",
        permissions=["2nd_line_support"],
    )
    def start_data_export(self, request, queryset, *args, **kwargs):
        template = ("admin/start_data_export.html",)

        valid_users = queryset.exclude(Q(user_no="") | Q(user_no__isnull=True))

        if queryset.count() > 1:
            self.message_user(
                request,
                "Currently you can only start the data export for a single user at a time",
                messages.ERROR,
            )
            return
        elif valid_users.count() == 0:
            self.message_user(
                request,
                "User is missing my.nofence user_no. Cant start dataexport.",
                messages.ERROR,
            )
            return

        user = valid_users.first()

        if "start_export" in request.POST:
            form = StartDataExportActionForm(request.POST)
            if form.is_valid():
                data = form.cleaned_data
                try:
                    recipients = data["recipients"].split(",")
                    data_export.start_data_export(
                        data["user_no"],
                        data["start_date"],
                        recipients,
                        end_date=data["end_date"],
                    )
                    self.message_user(
                        request,
                        f"Data export started for user started {data['user_no']}. "
                        f"Result will be emailed to {recipients}",
                        messages.SUCCESS,
                    )
                except Exception as e:
                    logger.exception("Failed to start dataexport")
                    self.message_user(request, str(e), messages.ERROR)

                return redirect(request.get_full_path())

        else:
            form = StartDataExportActionForm(
                initial={
                    "user_no": user.user_no,
                    "start_date": "2019-01-01",
                    # Set end date to the next day to include data from the current date
                    "end_date": (date.today() + timedelta(days=1)).isoformat(),
                    "recipients": user.email,
                },
            )

        opts = self.model._meta

        context = {
            **self.admin_site.each_context(request),
            "title": "Start Data Export",
            "subtitle": "",
            "selected_objects": queryset,
            "form": form,
            "opts": opts,
            "app_label": opts.app_label,
        }

        return render(request, template, context=context)

    def has_2nd_line_support_permission(self, request, obj=None):
        return request.user.has_perm("core.2nd_line_support")

    @admin.action(description="Sync customer stats to Hubspot")
    def sync_subscription_to_hubspot(self, request, queryset):
        ok, missing = 0, 0
        for customer in queryset.all():
            if customer.hubspot_id:
                task = hubspot_tasks.sync_billy_customer_stats_to_hubspot.delay(
                    customer.id,
                )
                ok += 1
                logger.info(
                    "Manually triggered sync of customer stats to hubspot "
                    f"for customer {customer.id} in task {task.id}",
                )
            else:
                missing += 1

        if ok:
            self.message_user(
                request,
                f"Started sync of stats to hubspot for {ok} customers(s)",
                messages.SUCCESS,
            )
        if missing:
            self.message_user(
                request,
                f"{missing} customers(s) missing hubspot_id was skipped",
                messages.WARNING,
            )

    @admin.action(description="Export customer invoice info")
    def export_invoice_info(self, request, queryset):
        def row_generator(queryset):
            buffer = csv_export.CsvEchoWriter()
            writer = csv.writer(buffer)
            yield writer.writerow(
                [
                    "customer_id",
                    "name",
                    "invoice provider",
                    "balance",
                    "not overdue",
                    "overdue 1-30",
                    "overdue 31-60",
                    "overdue 61-90",
                    "overdue 90+",
                ],
            )

            queryset = queryset.annotate(
                balance_sum=Coalesce(
                    Subquery(
                        InvoiceDocument.objects.values("customer_id")
                        .filter(
                            customer=OuterRef("pk"),
                        )
                        .annotate(
                            sum=Sum(
                                Coalesce(
                                    "balance",
                                    Cast(
                                        0,
                                        MoneyField(max_digits=14, decimal_places=2),
                                    ),
                                ),
                            ),
                        )
                        .values("sum"),
                    ),
                    Cast(0, output_field=MoneyField(max_digits=14, decimal_places=2)),
                ),
            )

            for customer in queryset.order_by("-balance_sum"):
                balance = customer.invoice_documents.total_balance_sums().values()
                overdue = customer.invoice_documents.is_overdue()
                not_overdue = customer.invoice_documents.is_not_overdue()
                overdue_1 = overdue.filter(
                    due_date__gte=date.today() - timedelta(days=30),
                )
                overdue_2 = overdue.filter(
                    due_date__lt=date.today() - timedelta(days=30),
                    due_date__gte=date.today() - timedelta(days=60),
                )
                overdue_3 = overdue.filter(
                    due_date__lt=date.today() - timedelta(days=60),
                    due_date__gte=date.today() - timedelta(days=90),
                )
                overdue_4 = overdue.filter(
                    due_date__lt=date.today() - timedelta(days=90),
                )
                yield writer.writerow(
                    [
                        customer.visma_id or customer.xero_id,
                        customer.name,
                        "xero" if customer.nofence_company == NOFENCE_UK else "visma",
                        ", ".join(map(str, balance)),
                        ", ".join(map(str, not_overdue.total_balance_sums().values())),
                        ", ".join(map(str, overdue_1.total_balance_sums().values())),
                        ", ".join(map(str, overdue_2.total_balance_sums().values())),
                        ", ".join(map(str, overdue_3.total_balance_sums().values())),
                        ", ".join(map(str, overdue_4.total_balance_sums().values())),
                    ],
                )

        return StreamingHttpResponse(
            row_generator(queryset),
            content_type="text/csv",
            headers={"Content-Disposition": 'attachment; filename="invoice-info.csv"'},
        )

    def has_payment_reminder_permission(self, request, obj=None):
        return request.user.has_perm("core.add_paymentreminder")

    @admin.action(
        description="Send payment reminders",
        permissions=["payment_reminder"],
    )
    def send_payment_reminders(self, request, queryset):
        service = payment_reminders.PaymentReminderService()
        total_count = queryset.count()
        total_sent_count = 0
        unique_customer_count = 0

        for customer in queryset.all():
            count = service.send_reminders_for_overdue_invoices_by_customers([customer])

            if count > 0:
                unique_customer_count += 1
                total_sent_count += count
                msg = f"Sent {count} new reminder{pluralize(count)} for overdue invoices to {customer}"
            else:
                msg = f"No new reminders sent to {customer}"

            # If 10 or less customer was selected we create a message for each customer
            if total_count <= 10:
                self.message_user(
                    request,
                    msg,
                    messages.SUCCESS if count > 0 else messages.WARNING,
                )

        # If a lot of customers was selected we just create a summary message
        if total_count > 10:
            self.message_user(
                request,
                f"Sent {total_sent_count} new reminder{pluralize(total_sent_count)} for overdue "
                f"invoices to {unique_customer_count} customers.",
                messages.SUCCESS if total_sent_count > 0 else messages.WARNING,
            )

    def has_send_mass_email_permission(self, request, obj=None):
        return request.user.has_perm("core.add_massemail")

    @admin.action(
        description="Run Price Level Notifier",
        permissions=["price_level_notifier"],
    )
    def run_price_level_notification_check(self, request, queryset):
        service = create_price_level_notifier()

        count = 0
        for customer in queryset.all():
            service.check_and_notify_customer(customer)
            count += 1

        self.message_user(
            request,
            f"Price level notifier check run for {count} customers",
            messages.SUCCESS,
        )

    def has_price_level_notifier_permission(self, request, obj=None):
        return request.user.is_superuser

    @admin.action(
        description="Run Price Cap Notifier",
        permissions=["price_cap_notifier"],
    )
    def run_price_cap_notification_check(self, request, queryset):
        service = create_price_cap_notifier()

        count = 0
        for customer in queryset.all():
            service.check_and_notify_customer(customer)
            count += 1

        self.message_user(
            request,
            f"Price cap notifier check run for {count} customers",
            messages.SUCCESS,
        )

    def has_price_cap_notifier_permission(self, request, obj=None):
        return request.user.is_superuser

    @admin.action(description="Invoice flexible usage", permissions=["create_invoice"])
    def invoice_flexible_usage(self, request, queryset):
        if queryset.count() >= 5:
            self.message_user(
                request,
                "This action is only intended for a small number of customers (< 5)",
                messages.WARNING,
            )
            return

        biller = create_usage_biller_flexible_monthy(refresh_monthly_summary=True)

        for customer in queryset:
            biller.bill_customer(customer, from_month=date(2023, 3, 1))

        self.message_user(
            request,
            f"Billed {len(queryset)} customers(s) starting from 2023-03-01",
            messages.SUCCESS,
        )

    def has_create_invoice_permission(self, request, obj=None):
        return request.user.has_perm("core.add_invoice")

    @admin.action(
        description="Create mynofence user",
        permissions=["create_mynofence_user"],
    )
    def create_mynofence_user(self, request, queryset):
        if queryset.count() > 1:
            self.message_user(
                request,
                "Only 1 customer is allowed at a time.",
                messages.WARNING,
            )
            return

        for customer in queryset:
            if not customer.user_no:
                try:
                    user_no = nofence_integration.sync_billy_customer_to_mynofence(
                        customer.id,
                        metadata={
                            "trigger": "Manually create mynofence user from admin",
                        },
                    )
                    self.message_user(
                        request,
                        f"User successfully created in mynofence with userNo {user_no}. \
                        The user should automatically receive the activation email.",
                        messages.SUCCESS,
                    )
                except Exception as e:
                    self.message_user(
                        request,
                        f"Could not create mynofence user. {e}.",
                        messages.ERROR,
                    )
            else:
                self.message_user(
                    request,
                    f"Customer already has a mynofence user with userNo {customer.user_no}.",
                    messages.WARNING,
                )

    def has_create_mynofence_user_permission(self, request, obj=None):
        return request.user.has_perm("core.change_customer")

    @admin.action(
        description="Add fake door link",
        permissions=["add_fake_door_link"],
    )
    def add_fake_door_link(self, request, queryset, *args, **kwargs):
        template = "admin/add_fake_door_link.html"

        queryset_count = queryset.count()

        if "select_fake_door" in request.POST:
            template = ("admin/select_fake_door_link.html",)
            form = SelectFakeDoorLinkForm(request.POST)
            if form.is_valid():
                selected_fake_door_link: FakeDoorLink = form.cleaned_data["fake_door_link"]
                selected_fake_door_link.customers.add(*queryset.all())
                selected_fake_door_link.save()
                self.message_user(
                    request,
                    f"Added fake door links to {queryset_count} customer{pluralize(queryset_count)}.",
                    messages.SUCCESS,
                )
                return None
        else:
            template = ("admin/select_fake_door_link.html",)
            form = SelectFakeDoorLinkForm()

        opts = self.model._meta

        context = {
            **self.admin_site.each_context(request),
            "title": f"Add fake door link to {queryset_count} customer{pluralize(queryset_count)}",
            "selected_objects": queryset,
            "form": form,
            "opts": opts,
            "app_label": opts.app_label,
        }

        return render(request, template, context=context)

    def has_add_fake_door_link_permission(self, request, obj=None):
        return request.user.has_perm("core.add_fakedoorlink")

    @admin.action(description="Reset customer response to questionnaire")
    def reset_customer_response_to_questionnaire(self, request, queryset: QuerySet[Customer]):
        template = ("admin/add_customers_to_questionnaires.html",)

        if "apply" in request.POST:
            form = CustomerResetQuestionnaireForm(request.POST)
            if form.is_valid():
                users = [customer.user for customer in queryset.select_related("user").all() if customer.user]

                questionnaire: Questionnaire = form.cleaned_data["questionnaire"]
                questions = questionnaire.questions.all()
                deletedChoiceAnswers, _ = QuestionnaireChoiceAnswer.objects.filter(
                    question__in=questions,
                    responder__in=users,
                ).delete()
                deletedTextAnswers, _ = QuestionnaireTextAnswer.objects.filter(
                    question__in=questions,
                    responder__in=users,
                ).delete()

                messages.info(
                    request,
                    f"Successfully deleted {deletedChoiceAnswers} choice answers and {deletedTextAnswers} "
                    f"text answers for {queryset.count()} customers for questionnaire: {questionnaire.title}",
                )
                return None
        else:
            initial = {}
            form = CustomerResetQuestionnaireForm(initial=initial)

        opts = self.model._meta

        context = {
            **self.admin_site.each_context(request),
            "title": f"Reset {queryset.count()} customers responses to questionnaire",
            "selected_objects": queryset,
            "form": form,
            "opts": opts,
            "app_label": "core",
        }

        return render(request, template, context=context)

    def has_reset_customer_response_to_questionnaire_permission(self, request, obj=None):
        return request.user.has_perm("core.questionnaire.can_change_questionnaire")

    @admin.action(description="Add customer to questionnaire")
    def add_customers_to_questionnaire(self, request, queryset):
        template = ("admin/add_customers_to_questionnaires.html",)

        opts = self.model._meta

        if "apply" in request.POST:
            form = CustomerAssignQuestionnaireForm(request.POST)
            if form.is_valid():
                questionnaire: Questionnaire = form.cleaned_data["questionnaire"]
                questionnaire.users.add(
                    *[customer.user for customer in queryset.select_related("user").all() if customer.user],
                )
                questionnaire.save()
                success = queryset.count()
                messages.info(
                    request,
                    f"Successfully assigned {success} customers to questionnaire {questionnaire.title}",
                )
                if questionnaire.show_notification and questionnaire.notification_message:
                    missing_userno = queryset.filter(Q(user_no="") | Q(user_no__isnull=True))
                    missing_count = missing_userno.count()
                    valid_users = queryset.exclude(Q(user_no="") | Q(user_no__isnull=True))
                    valid_count = valid_users.count()
                    if valid_count == 0:
                        messages.error(
                            request,
                            "Could not send notification messages because 0 customers had a valid user_no",
                        )
                    else:
                        template = ("admin/send_app_notification_progress.html",)
                        message = questionnaire.notification_message
                        task = queue_notifications.delay(request.user.id, message.id, [c.id for c in valid_users])
                        task_id = task.id

                        context = {
                            **self.admin_site.each_context(request),
                            "title": (
                                f"Send notification to {valid_count}/{valid_count + missing_count}"
                                f" app user{pluralize(valid_count)}"
                            ),
                            "subtitle": "",
                            "selected_objects": queryset,
                            "form": form,
                            "opts": opts,
                            "app_label": opts.app_label,
                            "task_id": task_id,
                        }
                        return render(request, template, context=context)

                return None
        else:
            initial = {}
            form = CustomerAssignQuestionnaireForm(initial=initial)

        context = {
            **self.admin_site.each_context(request),
            "title": f"Add {queryset.count()} customers to questionnaire",
            "selected_objects": queryset,
            "form": form,
            "opts": opts,
            "app_label": "core",
        }

        return render(request, template, context=context)

    def has_add_customers_to_questionnaire_permission(self, request, obj=None):
        return request.user.has_perm("core.questionnaire.can_change_questionnaire")

    @admin.action(description="Enable Referral access for customers")
    def enable_referrals(self, request, queryset):
        queryset.update(referrals_enabled=True)

        self.message_user(
            request,
            f"Referrals enabled for {queryset.count()} customer{pluralize(queryset.count())}",
            messages.SUCCESS,
        )

    @unfold_action(
        description="Change Nofence Company",
        permissions=["change_nofence_company"],
        attrs={"use_post_request": True},
    )
    def change_nofence_company(self, request, queryset=None, object_id=None, *args, **kwargs):
        if object_id:
            queryset = self.get_queryset(request).filter(id=object_id)

        template = "admin/actions/change_nofence_company.html"

        if "cancel" in request.POST:
            return redirect(self.get_redirect_url_for_inline_action(request, object_id))
        elif "change_nofence_company" in request.POST:
            form = ChangeNofenceCompanyForm(request.POST)
            if form.is_valid():
                new_nofence_company = form.cleaned_data["nofence_company"]
                for customer in queryset:
                    customer: Customer
                    with transaction.atomic():
                        try:
                            errors = customer.can_change_nofence_company()
                            if not errors:
                                if customer.change_nofence_company(new_nofence_company):
                                    signal_notifiers.notify_customer_data_changed(customer_id=customer.id)

                                self.message_user(
                                    request,
                                    f"Changed nofence company to {new_nofence_company} for {customer}.",
                                    messages.SUCCESS,
                                )
                            else:
                                self.message_user(
                                    request,
                                    f"Nofence company not changed for {customer}: {', '.join(errors)}",
                                    messages.ERROR,
                                )

                        except IntegrityError as e:
                            logger.exception("Failed to change nofence company")

                            self.message_user(
                                request,
                                f"Failed to change nofence company for {customer}: {e}",
                                messages.ERROR,
                            )

                return redirect(self.get_redirect_url_for_inline_action(request, object_id))
        else:
            nofence_companies = {c.nofence_company for c in queryset}
            initial = {}
            if len(nofence_companies) == 1:
                initial["nofence_company"] = next(iter(nofence_companies))

            form = ChangeNofenceCompanyForm(initial=initial)

        opts = self.model._meta

        context = {
            **self.admin_site.each_context(request),
            "title": "Change Nofence Company",
            "subtitle": "",
            "selected_objects": queryset,
            "form": form,
            "opts": opts,
            "app_label": opts.app_label,
            **self.get_inline_action_context(request),
        }

        return render(request, template, context=context)

    def has_change_nofence_company_permission(self, request, obj=None):
        return request.user.has_perm("core.change_customer")


@admin.register(NotificationMessage, site=unfold_admin_site)
class NotificationMessageAdmin(InlineActionsMixin, DisabledCheckboxDjangoQLSearchMixin, TranslationUnfoldModelAdmin):
    list_display = ["get_title", "created_at", "updated_at"]

    fieldsets = (
        ("Title", {"fields": ("title",)}),
        ("Message", {"fields": ("message",)}),
        ("Link Title", {"fields": ("link_title",)}),
        ("Link URL", {"fields": ("link_url",)}),
        ("Link Navigate to flow", {"fields": ("link_navigate_to_flow",)}),
        (
            "Options",
            {
                "fields": (
                    "icon",
                    "image",
                    "show_popup",
                    "popup_screen",
                    "popup_priority",
                    "always_pop",
                    "always_pop_expiry_date",
                ),
            },
        ),
        (
            "Push",
            {
                "fields": ("send_push",),
            },
        ),
        (
            "Notification Type",
            {
                "description": (
                    "Message and template id is only used for special cases. "
                    "Messages sent from the admin should have message type MANUAL with an empty template id. "
                    "A template is a message used as a template when generating autogenerated notifications from scripts."  # noqa: E501
                ),
                "fields": ("message_type", "template_id", "from_template_id"),
            },
        ),
    )

    search_fields = ["title"]
    ordering = ["-id"]
    list_filter = ["message_type", "template_id", "from_template_id", "show_popup"]

    actions = [
        "retry_failed_messages",
        "duplicate_messages",
        "test_notification_message",
    ]

    actions_detail = [
        "test_notification_message",
    ]

    def has_change_permission(self, request, obj: NotificationMessage = None):
        if obj is None or not obj.logged_messages.exists():
            # Allow edit as long as there are no sent messages
            return super().has_change_permission(request, obj)
        return False

    def get_search_results(self, request, queryset, search_term: str):
        if "autocomplete" in request.path:
            # Hide autogenerated messages from auto complete queries
            queryset = queryset.filter(message_type=NotificationMessage.Type.MANUAL)
        return super().get_search_results(request, queryset, search_term)

    def get_title(self, obj):
        if obj.message_type == NotificationMessage.Type.AUTOGENERATED:
            return f"{obj.title} <autogenerated>"
        elif obj.message_type == NotificationMessage.Type.TEMPLATE:
            return f"{obj.title} <template>"
        return obj.title

    @admin.action(
        description="Retry failed messages from messagelog",
        permissions=["send_app_notifications"],
    )
    def retry_failed_messages(self, request, queryset, *args, **kwargs):
        retry_count = 0
        for message in queryset.all():
            retry_count += retry_failed_notifications_from_log(message)

        if retry_count:
            self.message_user(
                request,
                f"Queued {retry_count} failed notification{pluralize(retry_count)} for retry",
                messages.SUCCESS,
            )
        else:
            self.message_user(request, "No failed messages found", messages.WARNING)

    def has_send_app_notifications_permission(self, request, obj=None):
        return request.user.has_perm("core.add_notificationmessage")

    @admin.action(
        description="Duplicate message(s)",
    )
    def duplicate_messages(self, request, queryset, *args, **kwargs):
        count = 0
        for message in queryset.all():
            new_msg = message.duplicate()
            count += 1
        if count == 1:
            return redirect(
                reverse("admin:core_notificationmessage_change", args=(new_msg.pk,)),
            )
        else:
            self.message_user(
                request,
                f"Duplicated {count} message{pluralize(count)}",
                messages.SUCCESS,
            )

    @transaction.atomic
    @unfold_action(
        description="Test notification message",
        permissions=["send_app_notifications"],
        attrs={"use_post_request": True},
    )
    def test_notification_message(self, request, queryset=None, object_id=None):
        if object_id:
            queryset = self.get_queryset(request).filter(id=object_id)

        if queryset.count() > 1:
            self.message_user(request, "Only one message can be tested at a time", messages.ERROR)
            return redirect(request.get_full_path())

        msg: NotificationMessage = queryset.first()
        log_msg = None
        log_msg_created = False

        template = "admin/actions/test_notification_message.html"

        if "cancel" in request.POST:
            return redirect(self.get_redirect_url_for_inline_action(request, msg.id))
        elif "send_notification" in request.POST:
            form = TestNotificationMessageForm(request.POST)
            if form.is_valid():
                customer = form.cleaned_data["customer"]

                log_msg, log_msg_created = app_notifications.queue_notification(
                    request.user.id,
                    msg.id,
                    customer.id,
                    countdown=2,
                )

                self.message_user(
                    request,
                    f"The message '{msg}' has been sent to {customer}. "
                    "The message should now appear in the app. When done the message can be cleaned up "
                    "to allow the message to be editable again.",
                    messages.SUCCESS,
                )

                if not log_msg_created:
                    self.message_user(
                        request,
                        f"The message '{msg}' was already sent to {customer}. Make sure you don't delete a real "
                        "message when cleaning up the test notifcation.",
                        messages.WARNING,
                    )

        elif "cleanup_notification" in request.POST:
            form = TestNotificationMessageForm(request.POST)
            if form.is_valid():
                customer = form.cleaned_data["customer"]

                log_msg = msg.logged_messages.filter(customer=customer).first()

                if log_msg:
                    log_msg.delete()

                    self.message_user(
                        request,
                        f"The message sent to {customer} was deleted.",
                        messages.INFO,
                    )
                else:
                    self.message_user(
                        request,
                        f"The message sent to {customer} was not found. Nothing was cleaned up. "
                        "It may have been deleted by someone else.",
                        messages.WARNING,
                    )

                return redirect(self.get_redirect_url_for_inline_action(request, msg.id))

        else:
            form = TestNotificationMessageForm()

        opts = self.model._meta

        context = {
            **self.admin_site.each_context(request),
            "title": "Test Notification Message",
            "subtitle": f"'{msg}'",
            "form": form,
            "opts": opts,
            "app_label": opts.app_label,
            "message": msg,
            "log_message": log_msg,
            "log_message_created": log_msg_created,
            "selected_objects": [msg],
            **self.get_inline_action_context(request),
        }

        return render(request, template, context=context)


class NotificationMessageFilter(AutocompleteFilter):
    title = "Message"
    field_name = "message"


@admin.register(NotificationLog, site=unfold_admin_site)
class NotificationLogAdmin(ModelAdminBase, UnfoldModelAdmin):
    list_display = [
        "message",
        "customer",
        "status",
        "read",
        "reference",
        "created_at",
        "updated_at",
    ]
    readonly_fields = [
        "customer",
        "status",
        "created_at",
        "updated_at",
        "show_notification",
        "sender",
        "message",
    ]
    exclude = ["message"]

    ordering = ["-created_at"]

    list_filter = [
        "status",
        CustomerFilter,
        NotificationMessageFilter,
        "message__from_template_id",
    ]

    actions = [
        "retry_failed_messages",
    ]

    def show_notification(self, obj):
        html = []
        for lang in settings.MODELTRANSLATION_LANGUAGES:
            html.append(
                f"""<div>
                <h2>{lang.upper()}</h2>
                <p><strong>{{title_{lang}}}</strong></p>
                <p>{{message_{lang}}}</p>
                <p>
                link_url={{link_url_{lang}}} -
                link_title={{link_title_{lang}}}
                </p>
            </div>""",
            )
        html.append(
            """
            <h2>Send Push</h2>
            <p>{send_push}</p>
        """,
        )
        html.append(
            """
            <h2>Options</h2>
            <p>icon={icon}</p>
            <p>show_popup={show_popup}</p>
            <p>popup_screen={popup_screen}</p>
            <p>popup_priority={popup_priority}</p>
            <p>alway_pop={always_pop}</p>
            <p>alway_pop={always_pop_expiry_date}</p>
        """,
        )

        return format_html(
            "".join(html),
            **model_to_dict(obj.message, exclude=["customers"]),
        )

    show_notification.short_description = "Notification"

    @admin.action(
        description="Retry failed messages",
        permissions=["send_app_notifications"],
    )
    def retry_failed_messages(self, request, queryset, *args, **kwargs):
        retry_count, not_failed_count = 0, 0
        for msg_log in queryset.all():
            if msg_log.status == NotificationLog.Status.FAILED:
                retry_failed_message_log_entry(msg_log)
                retry_count += 1
            else:
                not_failed_count += 1

        if retry_count:
            self.message_user(
                request,
                f"Queued {retry_count} notification{pluralize(retry_count)} for retry",
                messages.SUCCESS,
            )

        if not_failed_count:
            self.message_user(
                request,
                f"{not_failed_count} notification{pluralize(not_failed_count)} skipped because status was "
                f"not {NotificationLog.Status.FAILED}",
                messages.WARNING,
            )

    def has_send_app_notifications_permission(self, request, obj=None):
        return request.user.has_perm("core.add_notificationmessage")


@admin.register(CollarUsage, site=unfold_admin_site)
class CollarUsageAdmin(ModelAdminBase, UnfoldModelAdmin):
    list_display = ("date", "collar_link", "product", "customer_link")
    list_filter = (CustomerFilter, CollarFilter, ("date", RangeDateFilter), "product")
    search_fields = [
        "collar__serial_no",
        "customer__visma_id",
        "customer__xero_id",
        "customer__name",
    ]
    ordering = ["-date", "customer__pk", "collar__pk"]
    actions = ["download_csv"]
    list_select_related = ("collar", "customer__user")
    readonly_fields = ("usage_summary",)
    autocomplete_fields = ["customer", "usage_summary", "collar"]
    show_full_result_count = False
    paginator = LargeTablePaginatorPG

    def collar_link(self, obj):
        url = reverse("admin:core_collar_change", args=(obj.collar.pk,))
        return format_html("<a href='{}'>{}</a>", url, obj.collar)

    collar_link.admin_order_field = "collar"
    collar_link.short_description = "collar"

    def customer_link(self, obj):
        url = reverse("admin:core_customer_change", args=(obj.customer.pk,))
        return format_html("<a href='{}'>{}</a>", url, obj.customer)

    customer_link.admin_order_field = "customer"
    customer_link.short_description = "customer"

    @admin.action(description="Download data as CSV")
    def download_csv(self, request, queryset):
        response = HttpResponse(content_type="text/csv")
        response["Content-Disposition"] = "attachment; filename=collar_usage_data.csv"
        writer = csv.writer(response)

        writer.writerow(["date", "serial_no", "product"])
        for s in queryset:
            writer.writerow(
                [s.date, s.collar.serial_no, s.collar.get_product_display()],
            )

        return response


class CollarsWithDifferentOwnerUserListFilter(SimpleListFilter):
    title = "Different Owner/User"
    parameter_name = "diff_owner_user"

    def lookups(self, request, model_admin):
        return (("YES", "Yes"),)

    def queryset(self, request, queryset):
        if self.value() == "YES":
            return queryset.filter(
                (Q(owner__isnull=True) & Q(user__isnull=False))
                | (Q(owner__isnull=False) & Q(user__isnull=True))
                | (Q(owner__isnull=False) & Q(user__isnull=False)),
            ).exclude(owner=F("user"))


class CollarsReplacementListFilter(SimpleListFilter):
    title = "Return and Replacement"
    parameter_name = "rr_filter"

    def lookups(self, request, model_admin):
        return [
            ("is_replaced", "Is Replaced"),
            ("is_replacing", "Is Replacing"),
        ]

    def queryset(self, request, queryset):
        if self.value() == "is_replaced":
            return queryset.filter(returned__isnull=False)
        elif self.value() == "is_replacing":
            return queryset.filter(replaced__isnull=False)


class XModemUratArgAckedListFilter(SimpleListFilter):
    title = "xModemUratArg Acked"
    parameter_name = "urat_arg_acked"

    def lookups(self, request, model_admin):
        return [
            ("yes", "Yes"),
            ("no", "No"),
        ]

    def queryset(self, request, queryset):
        if self.value() == "yes":
            return queryset.filter(config_x_modem_urat_arg_ack_at__isnull=False)
        elif self.value() == "no":
            return queryset.filter(config_x_modem_urat_arg_ack_at__isnull=True)


class CollarTagInline(ImmutableMixin, TabularInlineBase):
    verbose_name = "Tag"
    verbose_name_plural = "Tags"
    model = CollarTagFromMongo
    fields = ["tag", "created_at"]
    readonly_fields = ["tag", "created_at"]
    ordering = ["-created_at"]


class CollarWarrantyInline(ImmutableMixin, TabularInlineBase):
    verbose_name = "Warranty"
    model = CollarWarranty
    fk_name = "collar"
    fields = ["start_date", "end_date", "inherited_from_collar"]
    readonly_fields = ["inherited_from_collar"]

    def has_change_permission(self, request, obj=None):
        return True


class CollarOwnerInline(ImmutableMixin, TabularInlineBase):
    verbose_name = "Owner History"
    verbose_name_plural = "Owner History"
    model = CollarOwner
    readonly_fields = ["get_owner", "updated_at"]
    fields = ["get_owner", "updated_at"]
    ordering = ["-id"]
    classes = ["collapse"]

    @admin.display(description="Owner (Assignments before February 2022 may be missing)")
    def get_owner(self, obj: CollarOwner):
        if obj.owner:
            url = reverse("admin:core_customer_change", args=(obj.owner.pk,))
            return format_html("<a href='{}'>{}</a>", url, obj.owner)
        return "-"

    def get_queryset(self, request: HttpRequest):
        return super().get_queryset(request).select_related("owner")


@admin.register(Collar, site=unfold_admin_site)
class CollarAdmin(SendMassEmailAction, InlineActionsMixin, SimpleHistoryAdmin, UnfoldModelAdmin):
    djangoql_completion_enabled_by_default = False
    list_display = (
        "serial_no",
        "alias",
        "product",
        "model",
        "warranty",
        "owner_link",
        "user_link",
        "firmware",
        "sim_card_id",
        "ems_provider",
        "pcb_revision",
        "mec_revision",
        "product_record",
        "get_modem_config",
        "server_ip",
        "last_recorded_activity",
        "blocked_reason",
        "get_return_and_replacement_link",
        "get_shipment_link",
    )
    list_filter = (
        ("produced_at", RangeDateFilter),
        ("picked_collar__shipment__delivered_date", RangeDateFilter),
        OwnerFilter,
        UserFilter,
        ("owner", EmptyFieldListFilter),
        CollarsWithDifferentOwnerUserListFilter,
        CollarsReplacementListFilter,
        ("owner__business_reg_no", EmptyFieldListFilter),
        "owner__user__is_staff",
        "product",
        "model",
        "ems_provider",
        "pcb_revision",
        "mec_revision",
        "product_record",
        "modem_model",
        "modem_version",
        "modem_upgrade_status",
        ("modem_upgrade_status_download_restarts", RangeNumericFilter),
        ("modem_upgrade_status_upgrade_restarts", RangeNumericFilter),
        CollarsWithModemUpgradeStatusChanged,
        ("modem_upgrade_status_created_at", RangeDateFilter),
        "tags__tag",
        ("config_x_modem_urat_arg", custom_titled_filter("xModemUratArg Config")),
        XModemUratArgAckedListFilter,
        ("owner__country", custom_titled_filter("Owner Country")),
        ("picked_collar", custom_titled_empty_filter("Shipment")),
        "server_ip",
    )
    search_fields = ["serial_no", "alias", "sim_card_id", "owner__name", "user__name"]
    ordering = ["-produced_at"]
    date_hierarchy = "produced_at"
    actions = [
        "change_collar_owner_and_user",
        "set_collar_tag",
        "remove_collar_tag",
        "set_clear_flash_on_next_poll",
        "set_reboot_on_next_poll",
        "collar_debug_export",
        csv_export.streaming_csv_export_action(
            "Export collar info (CSV)",
        ),
        "start_return_process",
        "send_mass_email",
        "set_communication_plan",
    ]
    actions_detail = [
        "set_communication_plan",
    ]

    # check if current environment is prod
    # following actions are available only from billy-prod
    if settings.COLLAR_ENVIRONMENT_MOVE_ACTION_ENABLED:
        actions.append("set_collar_environment")

    readonly_fields = [
        "owner",
        "user",
        "get_ble_key",
        "modem_upgrade_status",
        "modem_upgrade_status_created_at",
        "modem_upgrade_status_download_restarts",
        "modem_upgrade_status_upgrade_restarts",
        "modem_upgrade_history",
        "modem_model",
        "modem_version",
        "ems_provider",
        "pcb_revision",
        "mec_revision",
        "return_and_replacement_info",
        "get_return_and_replacement_link",
        "get_replaced_by",
        "get_replaces",
        "get_warranty_info",
        # "config_x_modem_urat_arg",
        "config_x_modem_urat_arg_ack_at",
        "last_recorded_activity",
    ]
    list_select_related = ["owner__user", "user__user", "picked_collar__shipment"]

    fieldsets = (
        (
            None,
            {
                "fields": [
                    "produced_at",
                    "serial_no",
                    "sim_card_id",
                    "product",
                    "model",
                    "model_internal",
                    "hw_version_name",
                    ("ems_provider", "product_record", "mec_revision", "pcb_revision"),
                    ("modem_model", "modem_version"),
                    "owner",
                    "user",
                    "last_recorded_activity",
                    "return_and_replacement_info",
                ],
            },
        ),
        (
            "Config",
            {
                "fields": [
                    "config_x_modem_urat_arg",
                    "config_x_modem_urat_arg_ack_at",
                ],
            },
        ),
    )

    headers = {
        "Content-Type": "application/json",
        "Authorization": "Bearer " + settings.NOFENCE_API_TOKEN,
    }

    inlines = [
        CollarTagInline,
        CollarWarrantyInline,
        CollarOwnerInline,
    ]

    def filter_customers_for_mass_email(self, queryset):
        user_ids = [user for user in queryset.values_list("owner", flat=True).distinct()]
        return Customer.objects.filter(id__in=user_ids)

    def has_send_mass_email_permission(self, request, obj=None):
        return request.user.has_perm("core.add_massemail")

    def get_rangefilter_modem_upgrade_status_download_restarts_title(
        self,
        *args,
        **kwargs,
    ):
        return _("Modem FW Download restarts")

    def get_rangefilter_modem_upgrade_status_upgrade_restarts_title(
        self,
        *args,
        **kwargs,
    ):
        return _("Modem FW Upgrade restarts")

    def get_rangefilter_modem_upgrade_status_created_at_title(self, *args, **kwargs):
        return _("Modem FW Status Changed")

    def blocked_reason(self, obj):
        return obj.get_blocked_reason()

    def get_list_select_related(self, request):
        select_related = [
            "owner__user",
            "user__user",
        ]

        # Reduce number of queries when filtering on returns
        rr_filter = request.GET.get("rr_filter", None)
        if rr_filter == "is_replaced":
            select_related.extend(
                [
                    "returned__collar",
                    "returned__replacement_collar",
                    "returned__return_and_replacement__sales_order",
                ],
            )
        elif rr_filter == "is_replacing":
            select_related.extend(
                [
                    "returned__collar",
                    "replaced__collar",
                    "replaced__replacement_collar",
                    "replaced__return_and_replacement__sales_order",
                ],
            )

        warranty = request.GET.get("warranty", None)
        if warranty == "show_warranty":
            select_related.append("collar_warranty")

        return select_related

    def get_list_display(self, request):
        list_display = [*super().get_list_display(request)]

        rr_filter = request.GET.get("rr_filter", None)
        if rr_filter == "is_replaced":
            list_display.extend(["get_return_and_replacement_link", "get_replaced_by"])
            list_display = self.filter_list_display_for_extra_values(list_display)
        elif rr_filter == "is_replacing":
            list_display.extend(["get_return_and_replacement_link", "get_replaces"])
            list_display = self.filter_list_display_for_extra_values(list_display)

        warranty = request.GET.get("warranty", None)
        if warranty == "show_warranty":
            list_display.append("get_warranty_info")
            list_display = self.filter_list_display_for_extra_values(list_display)

        return list_display

    def filter_list_display_for_extra_values(self, list_display):
        """Hide some value when we add additional items"""
        skip_fields = ["pcb_revision", "mec_revision", "product_record"]
        return [f for f in list_display if f not in skip_fields]

    def get_fieldsets(self, request, obj=None):
        fieldsets = super().get_fieldsets(request, obj)
        if obj is not None:
            if obj.modem_upgrade_status:
                return fieldsets + (
                    (
                        _("Modem Upgrade Status"),
                        {
                            "fields": (
                                "modem_upgrade_status",
                                "modem_upgrade_status_created_at",
                                "modem_upgrade_status_download_restarts",
                                "modem_upgrade_status_upgrade_restarts",
                                "modem_upgrade_history",
                            ),
                            "classes": ["collapse in"],
                        },
                    ),
                )
        return fieldsets

    def get_search_results(self, request, queryset, search_term):
        """
        try and detect a list of collar_nos and do a search on all of them
        so we can do batch operations
        """

        if request.GET.get("model_name") == "collarreplacement" and "rr_customer_id" in request.GET:
            # The request is coming from the Return and Replacements page, filter on the selected customer
            queryset = queryset.filter(owner_id=request.GET["rr_customer_id"])

        search_term_list = []
        if search_term.replace(" ", "").isnumeric():
            for word in search_term.split(" "):
                if word.strip().isnumeric():
                    search_term_list.append(word)

            queryset = Collar.objects.filter(
                Q(serial_no__in=search_term_list) | Q(sim_card_id__in=search_term_list),
            )
            return queryset, False

        return super(CollarAdmin, self).get_search_results(
            request,
            queryset,
            search_term,
        )

    def owner_link(self, obj):
        if obj.owner:
            url = reverse("admin:core_customer_change", args=(obj.owner.pk,))
            return format_html("<a href='{}'>{}</a>", url, obj.owner)

    owner_link.admin_order_field = "owner"
    owner_link.short_description = "owner"

    def user_link(self, obj):
        if obj.user:
            url = reverse("admin:core_customer_change", args=(obj.user.pk,))
            return format_html("<a href='{}'>{}</a>", url, obj.user)

    user_link.admin_order_field = "user"
    user_link.short_description = "user"

    @admin.display(description="Return and Replacement")
    def return_and_replacement_info(self, obj: Collar):
        replacement = self.get_collar_replacement(obj)
        if replacement is not None:
            rr: ReturnAndReplacement = replacement.return_and_replacement
            old_collar = replacement.collar
            new_collar = replacement.replacement_collar
            collar_info = ""
            if old_collar == obj:
                if new_collar is not None:
                    url = reverse("admin:core_collar_change", args=(new_collar.pk,))
                    collar_info = format_html(
                        "This collar is replaced by: <a href='{}'>{}</a>",
                        url,
                        new_collar,
                    )
                else:
                    collar_info = "This collar is about to get replaced"
            elif new_collar == obj:
                url = reverse("admin:core_collar_change", args=(old_collar.pk,))
                collar_info = format_html(
                    "This collar replaces: <a href='{}'>{}</a>",
                    url,
                    old_collar,
                )

            url = reverse("admin:core_returnandreplacement_change", args=(rr.pk,))
            rr_title = rr.get_sales_order().order_number if rr.get_sales_order() else rr
            return format_html(
                "<a href='{}'>{} - {}</a> - &nbsp;&nbsp; {}",
                url,
                rr_title,
                rr.get_status_display(),
                collar_info,
            )

        return "-"

    @admin.display(description="Modem Config", ordering="config_x_modem_urat_arg")
    def get_modem_config(self, obj: Collar):
        return obj.get_config_x_modem_urat_arg_display()

    @admin.display(description="Replaced By", ordering="returned")
    def get_replaced_by(self, obj: Collar) -> Optional[Collar]:
        replacement = self.get_collar_replacement(obj)
        if replacement is not None:
            collar = obj.get_replaced_by_collar()
            if collar is not None:
                url = reverse("admin:core_collar_change", args=(collar.pk,))
                return format_html("<a href='{}'>{}</a>", url, collar)
            return "Pending"
        return "No Replacement"

    @admin.display(description="Replacing", ordering="replaced")
    def get_replaces(self, obj: Collar) -> Optional[Collar]:
        collar = obj.get_replaced_collar()
        if collar is not None:
            url = reverse("admin:core_collar_change", args=(collar.pk,))
            return format_html("<a href='{}'>{}</a>", url, collar)

    @admin.display(description="R&R")
    def get_return_and_replacement_link(self, obj: Collar):
        replacement = self.get_collar_replacement(obj)
        if replacement is not None:
            rr: ReturnAndReplacement = replacement.return_and_replacement
            url = reverse("admin:core_returnandreplacement_change", args=(rr.pk,))
            if rr.status == ReturnAndReplacement.Status.DUE_FOR_REPLACEMENT:
                rr_title = f"{rr.get_status_display()} - {rr.id}"
            else:
                rr_title = rr.get_sales_order().order_number if rr.get_sales_order() else rr
            return format_html("<a href='{}'>{}</a>", url, rr_title)

    def get_collar_replacement(self, obj: Collar) -> Optional[CollarReplacement]:
        replacement = getattr(obj, "returned", None)
        if replacement is not None:
            return replacement
        replacement = getattr(obj, "replaced", None)
        if replacement is not None:
            return replacement

    @admin.display(description="Shipment")
    def get_shipment_link(self, obj: Collar):
        picked_collar: ShipmentPickedUnit = obj.picked_collar
        if not picked_collar.exists() or not picked_collar.first().shipment:
            return

        shipment = picked_collar.first().shipment
        url = reverse("admin:core_shipment_change", args=(shipment.pk,))
        shipment_title = shipment
        return format_html("<a href='{}'>{}</a>", url, shipment_title)

    @admin.display(description="Warranty End", ordering="collar_warranty__end_date")
    def get_warranty_info(self, obj: Collar) -> Optional[Collar]:
        warrant = obj.warranty
        if warrant is not None:
            return f"{date_format(warrant.end_date)}"
        return "-"

    @admin.display(description="History")
    def modem_upgrade_history(self, obj: Collar):
        try:
            doc = obj.document
            html = ['<table style="width: 100%;">']
            html.append(
                """<tr>
                <th>Status</th>
                <th>Created</th>
            </tr>
            """,
            )
            if doc and doc.modem_upgrade_info:
                for h in doc.modem_upgrade_info.history:
                    html.append(
                        format_html(
                            """
                    <tr>
                        <td>{}</td>
                        <td>{}</td>
                    </tr>
                    """,
                            h.status,
                            date_format(h.created, "DATETIME_FORMAT"),
                        ),
                    )
            else:
                html.append(
                    format_html(
                        """
                    <tr>
                        <td colspan="2">History is Empty</td>
                    </tr>
                    """,
                    ),
                )

            html.append("</table>")
            return mark_safe("".join(html))
        except mongoengine.DoesNotExist:
            return "Document not found in mongodb"

    @admin.action(
        description="Start Return Process",
        permissions=["create_return_and_replacement"],
    )
    def start_return_process(self, request, queryset):
        customer_id = None
        collars_ids = []
        for collar in queryset:
            collars_ids.append(collar.id)

            if collar.owner is None:
                self.message_user(
                    request,
                    f"Collar {collar} has no owner. RR can not be created",
                    messages.WARNING,
                )
                return

            if customer_id is not None and customer_id != collar.owner.id:
                self.message_user(
                    request,
                    "Multiple owners found. Only collars owned by the same customer can be added to the same RR",
                    messages.WARNING,
                )
                return

            customer_id = collar.owner.id

        ids = [product.id for product in queryset.all()]
        url = reverse("admin:core_returnandreplacement_add")
        params = [("customer", customer_id)] + [("collar", id) for id in ids]
        params = urlencode(params)
        return HttpResponseRedirect(f"{url}?{params}")

    def has_create_return_and_replacement_permission(self, request, obj=None):
        return request.user.has_perm("core.add_returnandreplacement")

    @admin.action(description="Collar debug export (CSV)")
    def collar_debug_export(self, request, queryset, *args, **kwargs):
        template = ("admin/collar_debug_export.html",)

        if "done" in request.POST:
            return redirect(request.get_full_path())
        elif "start_export" in request.POST:
            form = CollarDebugExportActionForm(request.POST)
            if form.is_valid():
                data = form.cleaned_data

                def row_generator(queryset):
                    writer = csv.writer(CsvEchoWriter())

                    yield writer.writerow(
                        [
                            "serial_no",
                            "date",
                            "msg",
                            "sim",
                            "mmc_mnc",
                            "rat",
                            "main_state",
                            "sub_state",
                            "extra_info",
                            "bgs_start_ctr",
                        ],
                    )

                    for collar in queryset:
                        docs = MessageDocument.objects.filter(
                            (
                                DQ(serial_no=collar.serial_no)
                                & DQ(date__gte=data["from_date"])
                                & DQ(date__lt=data["to_date"])
                            )
                            & (DQ(msg_object__poll_msg__exists=True) | DQ(msg_object__gprs_msg__exists=True)),
                        ).order_by("serial_no", "-date")
                        for doc in docs:
                            if doc.msg_object.poll_msg:
                                yield writer.writerow(
                                    [
                                        collar.serial_no,
                                        doc.date,
                                        "poll_msg",
                                        collar.sim_card_id,
                                        doc.msg_object.poll_msg.gsm_info.mmc_mnc,
                                        doc.msg_object.poll_msg.gsm_info.rat,
                                        "",
                                        "",
                                        "",
                                        "",
                                    ],
                                )
                            if doc.msg_object.gprs_msg:
                                for error in doc.msg_object.gprs_msg.error_details.errors:
                                    yield writer.writerow(
                                        [
                                            collar.serial_no,
                                            doc.date,
                                            "gprs_msg",
                                            collar.sim_card_id,
                                            "",
                                            "",
                                            error.main_state,
                                            error.sub_state,
                                            error.extra_info,
                                            error.bgs_start_ctr,
                                        ],
                                    )

                return StreamingHttpResponse(
                    row_generator(queryset),
                    content_type="text/csv",
                    headers={
                        "Content-Disposition": 'attachment; filename="collar_debug.csv"',
                    },
                )

        else:
            form = CollarDebugExportActionForm(
                initial={
                    "from_date": date.today() - timedelta(days=7),
                    "to_date": date.today(),
                },
            )

        opts = self.model._meta

        context = {
            **self.admin_site.each_context(request),
            "title": "Collar Debug Export",
            "subtitle": "",
            "selected_objects": queryset,
            "form": form,
            "opts": opts,
            "app_label": opts.app_label,
        }

        return render(request, template, context=context)

    @admin.action(description="Change collar owner and user")
    def change_collar_owner_and_user(self, request, queryset, *args, **kwargs):
        template = ("admin/change_collar_owner_and_user.html",)

        result = {}

        if "done" in request.POST:
            return redirect(request.get_full_path())
        elif "apply" in request.POST:
            form = CollarAssignOwnerUserForm(request.POST)
            if self._is_owner_user_form_valid(request, form):
                updates = {}
                if "apply_owner_and_user" in request.POST or "apply_owner" in request.POST:
                    updates["new_owner"] = form.cleaned_data["owner"]
                if "apply_owner_and_user" in request.POST or "apply_user" in request.POST:
                    updates["new_user"] = form.cleaned_data["user"]

                success, failed = update_collars_owner_and_user(
                    queryset.all(),
                    **updates,
                )

                result = {"success": success, "warning": [], "failed": failed}
        else:
            initial = {}

            # If there is only a single owner or user we prepopulate the form
            # with these users, else we leave them empty.
            owners = {collar.owner.id if collar.owner else None for collar in queryset}
            if len(owners) == 1:
                initial["owner"] = next(iter(owners))
            users = {collar.user.id if collar.user else None for collar in queryset}
            if len(users) == 1:
                initial["user"] = next(iter(users))

            form = CollarAssignOwnerUserForm(initial=initial)

        opts = self.model._meta
        context = {
            **self.admin_site.each_context(request),
            "title": "Change owner and user",
            "subtitle": "",
            "selected_objects": queryset,
            "form": form,
            "opts": opts,
            "app_label": opts.app_label,
            "result": result,
            "action_origin": "owner",
        }

        return render(request, template, context=context)

    def _is_owner_user_form_valid(
        self,
        request,
        form: CollarAssignOwnerUserForm,
    ) -> bool:
        if form.is_valid():
            return True

        # Small hack to ignore missing owner error when using the change user only button
        if "apply_user" in request.POST:
            errors = form.errors.as_data()
            if len(errors) == 1 and "owner" in errors:
                return form.has_error("owner", Collar.ERROR_CODE_MISSING_OWNER) and len(errors["owner"]) == 1

        return False

    @unfold_action(description="Set Communication plan", attrs={"use_post_request": True})
    def set_communication_plan(self, request, queryset=None, object_id=None, *args, **kwargs):
        if "done" in request.POST or "cancel" in request.POST:
            return redirect(self.get_redirect_url_for_inline_action(request, object_id))

        if object_id:
            queryset = self.get_queryset(request).filter(id=object_id)

        template = "admin/actions/set_communication_plan.html"

        service = create_iotvalue_service()

        try:
            plans = service.get_communication_plans()
        except Exception as e:
            logger.exception("Failed to fetch communication plans from IOTValue")
            self.message_user(
                request,
                f"Failed to fetch communication plans from IOTValue. Please wait a bit and try again. Error was: {e}",
                messages.ERROR,
            )
            return redirect(self.get_redirect_url_for_inline_action(request, object_id))

        plan_choices = [("", "Select the new plan")] + [
            (plan.name, f"{plan.name} ({plan.description})") for plan in plans
        ]
        current_plans_by_collar = {}
        update_status_by_collar = {}
        did_update = False
        form_class = get_set_communication_plan_form(plan_choices)

        if "apply" in request.POST:
            form = form_class(request.POST)
            if form.is_valid():
                new_plan = form.cleaned_data["plan"]

                for collar in queryset.order_by("id").all():
                    collar: Collar
                    try:
                        if not collar.sim_card_id:
                            update_status_by_collar[collar.id] = dict(status="error", msg="No Sim Card ID")
                        else:
                            service.set_communication_plan(collar.sim_card_id, new_plan)
                            logger.info(f"Set communicatin plan {new_plan=} on {collar}")
                            current_plans_by_collar[collar.id] = new_plan
                            update_status_by_collar[collar.id] = dict(status="success", msg="OK")
                    except Exception as e:
                        logger.exception(f"Failed to set communication plan on {collar}")
                        update_status_by_collar[collar.id] = dict(status="error", msg=str(e))

                did_update = True

        else:
            initial = {}
            form = form_class(initial=initial)

        fetching_current_plans = queryset.count() <= 10

        collars = []
        description_lookup = {plan: desc for plan, desc in plan_choices}

        for collar in queryset:
            collar: Collar

            update_status = update_status_by_collar.get(collar.id, {})

            if collar.id in current_plans_by_collar or (fetching_current_plans and collar.sim_card_id):
                current_plan_name = current_plans_by_collar.get(collar.id, None)
                if not current_plan_name:
                    current_plan_name = service.get_communication_plan(collar.sim_card_id).name

                # Use the description from the form choices field if available, to show consistent values to the user.
                if current_plan_name in description_lookup:
                    collars.append(
                        dict(
                            collar=collar,
                            current_plan=description_lookup[current_plan_name],
                            update_status=update_status,
                        )
                    )
                else:
                    collars.append(dict(collar=collar, current_plan=current_plan_name, update_status=update_status))
            elif not fetching_current_plans:
                collars.append(
                    dict(
                        collar=collar,
                        current_plan="Not fetched because of high number of selected collars. You can still set a new value.",
                        update_status=update_status,
                    )
                )
            else:
                collars.append(dict(collar=collar, current_plan="n/a", update_status=update_status))

        opts = self.model._meta
        context = {
            **self.admin_site.each_context(request),
            "title": "Set Communication Plan",
            "subtitle": "",
            "form": form,
            "opts": opts,
            "app_label": opts.app_label,
            "selected_objects": queryset,
            "fetching_current_plans": fetching_current_plans,
            "collars": collars,
            "did_update": did_update,
            **self.get_inline_action_context(request),
        }

        return render(request, template, context=context)

    @admin.action(description="Set collar tag", permissions=["collar_tag"])
    def set_collar_tag(self, request, queryset, *args, **kwargs):
        template = ("admin/set_collar_tag.html",)

        if not hasattr(request.user, "customer") or not request.user.customer.user_no:
            self.message_user(
                request,
                "Can't set collar tag without my.nofence userNo. "
                "Your billy user is not associated with a nofence user or is missing nofence user_no.",
                messages.ERROR,
            )
            return
        elif "select_tag" in request.POST:
            form = SetCollarTagForm(request.POST)
            if form.is_valid():
                serials = queryset.values_list("serial_no", flat=True).all()
                serials = list(filter(bool, serials))
                tag = form.cleaned_data["tag"]
                nofence = create_nofence2_client(
                    user=request.user,
                    auth_user_permissions=["readwrite-all"],
                )
                nofence.set_tag_on_collars(serials, tag.name)
                self.message_user(
                    request,
                    f"Tag {tag.name} set on collars: #{',#'.join(map(str, serials))}",
                    messages.SUCCESS,
                )
        else:
            form = SetCollarTagForm()

        opts = self.model._meta

        context = {
            **self.admin_site.each_context(request),
            "title": f"Set tag on {queryset.count()} collars",
            "subtitle": "",
            "selected_objects": queryset,
            "form": form,
            "opts": opts,
            "app_label": opts.app_label,
        }

        return render(request, template, context=context)

    @admin.action(description="Remove collar tag", permissions=["collar_tag"])
    def remove_collar_tag(self, request, queryset, *args, **kwargs):
        template = ("admin/remove_collar_tag.html",)

        if not hasattr(request.user, "customer") or not request.user.customer.user_no:
            self.message_user(
                request,
                "Can't remove collar tag without my.nofence userNo. "
                "Your billy user is not associated with a nofence user or is missing nofence user_no.",
                messages.ERROR,
            )
            return
        elif "select_tag" in request.POST:
            form = RemoveCollarTagForm(request.POST)
            if form.is_valid():
                serials = queryset.values_list("serial_no", flat=True).all()
                serials = list(filter(bool, serials))
                tag = form.cleaned_data["tag"]
                nofence = create_nofence2_client(
                    user=request.user,
                    auth_user_permissions=["readwrite-all"],
                )
                nofence.remove_tag_on_collars(serials, tag.name)
                self.message_user(
                    request,
                    f"Tag {tag.name} removed on collars: #{',#'.join(map(str, serials))}",
                    messages.SUCCESS,
                )
        else:
            form = RemoveCollarTagForm()

        opts = self.model._meta

        context = {
            **self.admin_site.each_context(request),
            "title": f"Remove tag on {queryset.count()} collars",
            "subtitle": "",
            "selected_objects": queryset,
            "form": form,
            "opts": opts,
            "app_label": opts.app_label,
        }

        return render(request, template, context=context)

    def has_collar_tag_permission(self, request, obj=None):
        return request.user.has_perm("core.view_collartag")

    @admin.action(
        description="Set clear flash on next poll reply",
        permissions=["2nd_line_support"],
    )
    def set_clear_flash_on_next_poll(self, request, queryset, *args, **kwargs):
        serials = queryset.values_list("serial_no", flat=True).all()
        serials = list(filter(bool, serials))
        nofence = create_nofence_client()
        nofence.set_clear_flash_on_poll_reply(serials, True)
        self.message_user(
            request,
            f"Collars set to clear flash on next poll reply: #{',#'.join(map(str, serials))}",
            messages.SUCCESS,
        )

    @admin.action(
        description="Set reboot on next poll reply",
        permissions=["2nd_line_support"],
    )
    def set_reboot_on_next_poll(self, request, queryset, *args, **kwargs):
        serials = queryset.values_list("serial_no", flat=True).all()
        serials = list(filter(bool, serials))
        nofence = create_nofence_client()
        nofence.set_reboot_on_poll_reply(serials, True)
        self.message_user(
            request,
            f"Collars set to reboot on next poll reply: #{',#'.join(map(str, serials))}",
            messages.SUCCESS,
        )

    def has_2nd_line_support_permission(self, request, obj=None):
        return request.user.has_perm("core.2nd_line_support")

    def has_change_env_permission(self, request, obj=None):
        return request.user.is_superuser

    @admin.display(description="rgubcBleKey")
    def get_ble_key(self, obj: Collar):
        try:
            return obj.document.get_ble_key() or ""
        except mongoengine.DoesNotExist:
            logger.warning(f"No document found for collar {obj.serial_no}")
            return ""

    @admin.action(description="Set Collar Environment", permissions=["change_env"])
    def set_collar_environment(self, request, queryset, *args, **kwargs):
        template = ("admin/set_collar_environment.html",)
        result = {}
        if "done" in request.POST:
            return redirect(request.get_full_path())
        elif "set_collar_environment" in request.POST:
            form = SetCollarEnvironment(request.POST)
            if form.is_valid():
                # get target and "from"/origin environment
                environment_update = str(form.cleaned_data["environment"])
                from_env = environment_update.split("->")[0]
                target_env = environment_update.split("->")[1]
                server_ip = settings.ENVIRONMENTS[target_env]["IP"]

                # update collars in mongo db, log results
                success, warning, failed = update_collars_environment(
                    queryset.all(),
                    from_env,
                    target_env,
                    request.user,
                )

                success_after_billy = []
                for succ_mongo_collar in success:
                    try:
                        succ_mongo_collar[0].server_ip = server_ip
                        succ_mongo_collar[0].save()
                        success_after_billy.append(
                            (
                                succ_mongo_collar[0],
                                succ_mongo_collar[1] + " | OK billy update current env",
                            ),
                        )
                    except Exception as e:
                        warning.append(
                            (
                                succ_mongo_collar[0],
                                succ_mongo_collar[1] + " | FAIL on billy update current env (not critical)",
                            ),
                        )
                        logger.warning(
                            f"Failed to update collar server_ip in billy {succ_mongo_collar[0]}, {e}",
                        )
                failed = [(fail[0], f"{fail[1]} | FAIL billy update not attempted") for fail in failed]
                result = {
                    "success": success_after_billy,
                    "warning": warning,
                    "failed": failed,
                }
        else:
            form = SetCollarEnvironment()

        opts = self.model._meta

        context = {
            **self.admin_site.each_context(request),
            "title": f"Switch environment for {queryset.count()} collars",
            "subtitle": "",
            "selected_objects": queryset,
            "form": form,
            "opts": opts,
            "app_label": opts.app_label,
            "action_origin": "collar_env",
            "result": result,
        }

        return render(request, template, context=context)


@admin.register(CollarUsageSummary, site=unfold_admin_site)
class CollarUsageSummaryAdmin(ModelAdminBase, UnfoldModelAdmin):
    fieldsets = (
        (None, {"fields": ("date", "customer", "product")}),
        (
            "Usage",
            {
                "fields": (
                    ("total_usage", "total_caps_reached"),
                    ("fixed_price_usage", "variable_price_usage", "usage_caps_applied"),
                    "missing_subscriptions",
                    "return_and_replacement_double_usage",
                    "invoice",
                ),
            },
        ),
    )
    list_display = (
        "date",
        "customer_link",
        "product",
        "total_usage",
        "usage_caps_applied",
        "usage_billed",
        "missing_subscriptions",
        "invoice",
    )
    list_filter = (CustomerFilter, InvoiceFilter, ("date", RangeDateFilter), "product")
    search_fields = ["customer__visma_id", "customer__xero_id", "customer__name"]
    ordering = ["-date", "customer"]
    actions = ["download_csv"]
    readonly_fields = ("invoice",)
    list_select_related = ("customer__user", "invoice__customer__user")
    autocomplete_fields = ["customer", "invoice"]
    show_full_result_count = False
    paginator = LargeTablePaginatorPG

    def customer_link(self, obj):
        url = reverse("admin:core_customer_change", args=(obj.customer.pk,))
        return format_html("<a href='{}'>{}</a>", url, obj.customer)

    customer_link.admin_order_field = "customer"
    customer_link.short_description = "customer"

    @admin.action(description="Download data as CSV")
    def download_csv(self, request, queryset):
        response = HttpResponse(content_type="text/csv")
        response["Content-Disposition"] = "attachment; filename=collar_usage_summary.csv"
        writer = csv.writer(response)

        writer.writerow(
            [
                "date",
                "customer",
                "product",
                "total_usage",
                "usage_caps_applied",
                "usage_billed",
                "missing_subscriptions",
                "invoice",
            ],
        )
        for s in queryset:
            writer.writerow(
                [
                    s.date,
                    s.customer,
                    s.get_product_display(),
                    s.total_usage,
                    s.usage_caps_applied,
                    s.usage_billed,
                    s.missing_subscriptions,
                    s.invoice,
                ],
            )

        return response


class InvoiceLineInline(TabularInlineBase):
    model = InvoiceLine
    fields = ("sku", "description", "item_price", "quantity", "discount", "value")
    ordering = ["created_at"]
    readonly_fields = ["value"]
    extra = 0


class InvoiceNofenceCompanyFilter(SimpleListFilter):
    title = "Nofence Company"
    parameter_name = "nofence_company"

    def lookups(self, request, model_admin):
        return [(c.id, c.name) for c in NofenceCompany.objects.all()]

    def queryset(self, request, queryset):
        if self.value():
            return queryset.filter(
                Q(nofence_company__id=self.value()) | Q(customer__nofence_company__id=self.value()),
            )
        return queryset


class InvoicedUsageInline(ImmutableMixin, TabularInlineBase):
    model = InvoicedUsage

    autocomplete_fields = ["usage", "invoice"]


class BillyInvoiceInvoiceDocumentInline(ImmutableMixin, TabularInlineBase):
    model = InvoiceDocument
    fields = ["doc_id", "doc_ref", "provider", "nofence_company", "status", "type"]
    show_change_link = True


@admin.register(Invoice, site=unfold_admin_site)
class InvoiceAdmin(SimpleHistoryAdmin, UnfoldModelAdmin):
    list_display = ("customer", "state", "updated_at", "sum_total", "number_of_lines")
    list_filter = [
        CustomerFilter,
        ("updated_at", RangeDateFilter),
        InvoiceNofenceCompanyFilter,
        "invoice_provider",
        "state",
        "invoice_lines__sku",
        "purchase_order_flow_state",
    ]
    search_fields = [
        "invoice_provider_id",
        "invoice_provider_ref",
        "customer__name",
        "customer__user__name",
        "customer__visma_id",
        "customer__xero_id",
        "pk",
    ]
    ordering = ["-created_at"]
    readonly_fields = [
        "sum_total",
        "nofence_company",
        "sync_status_link",
        "current_sync_status",
    ]
    date_hierarchy = "created_at"
    autocomplete_fields = ["customer"]
    actions = [
        "delete_selected",
        "queue_for_invoicing",
        "send_to_invoice_provider",
        "write_off",
        "download_csv",
        "download_invoice_csv",
    ]

    def delete_queryset(self, request, queryset):
        response = HttpResponseRedirect(request.get_full_path())
        for invoice in queryset:
            if invoice.state not in [Invoice.STATE_DRAFT]:
                self.message_user(
                    request,
                    "Can only delete draft or cancelled invoices",
                    messages.ERROR,
                )
                return response

        super().delete_queryset(request, queryset)

    def delete_model(self, request, invoice):
        response = HttpResponseRedirect(request.get_full_path())
        if invoice.state not in [Invoice.STATE_DRAFT]:
            self.message_user(
                request,
                "Can only delete a draft or cancelled invoice",
                messages.ERROR,
            )
            return response

        super().delete_model(request, invoice)

    @admin.action(description="Queue for invoicing")
    def queue_for_invoicing(self, request, queryset):
        if queryset.exclude(state=Invoice.STATE_DRAFT):
            self.message_user(
                request,
                "Invoices must be drafts to be queued",
                messages.WARNING,
            )
            return HttpResponseRedirect(request.get_full_path())

        queryset.update(state=Invoice.STATE_QUEUED)
        self.message_user(request, "Successfully queued invoices")

    @admin.action(description="Send invoice to invoice provider")
    def send_to_invoice_provider(self, request, queryset):
        if queryset.count() > 1:
            self.message_user(
                request,
                "Only 1 invoice is allowed at a time. For larger quantities use tasks.",
                messages.WARNING,
            )
            return
        if queryset.exclude(state=Invoice.STATE_QUEUED):
            self.message_user(
                request,
                "Invoices must be queued to be sent to invoice provider",
                messages.WARNING,
            )
            return HttpResponseRedirect(request.get_full_path())

        invoicer = create_invoicer_service(
            catch_errors=True,
        )

        ok, failed = 0, 0

        for invoice in queryset:
            invoice: Invoice
            invoicer.invoice(invoice)
            invoice.refresh_from_db()
            if invoice.state == Invoice.STATE_FAILED:
                failed += 1
            else:
                ok += 1

        if ok:
            self.message_user(request, f"Successfully sent {ok}invoices to invoice provider")
        if failed:
            self.message_user(request, f"Failed to send {failed} invoices to invoice provider", messages.ERROR)

    @admin.action(description="Mark as written off")
    def write_off(self, request, queryset):
        if queryset.exclude(state=Invoice.STATE_DRAFT):
            self.message_user(
                request,
                "Invoices must be drafts to be written off",
                messages.WARNING,
            )
            return HttpResponseRedirect(request.get_full_path())

        queryset.update(state=Invoice.STATE_WRITTEN_OFF)
        self.message_user(request, "Successfully written off invoices")

    def sum_total(self, obj):
        return obj.sum_total

    def number_of_lines(self, obj):
        return len(obj.invoice_lines.all())

    def get_inlines(self, request, obj: Invoice):
        inlines = [
            InvoiceLineInline,
            BillyInvoiceInvoiceDocumentInline,
        ]

        if obj and obj.invoice_type == Invoice.Type.FLEXIBLE_USAGE:
            inlines.append(InvoicedUsageInline)

        return inlines

    @admin.action(description="Download usage as CSV")
    def download_csv(self, request, queryset):
        if len(queryset) > 1:
            self.message_user(request, "Please select only 1 invoice", messages.WARNING)
            return HttpResponseRedirect(request.get_full_path())

        q = queryset.first()

        response = HttpResponse(content_type="text/csv")
        response["Content-Disposition"] = f"attachment; filename=invoice_summary_for_{q.customer.visma_id}.csv"
        writer = csv.writer(response)

        writer.writerow(["date", "product", "total usages", "billed usage"])
        for s in q.collarusagesummaries.all().order_by("-date", "product"):
            writer.writerow(
                [s.date, s.get_product_display(), s.total_usage, s.usage_billed],
            )

        return response

    @admin.action(description="Download invoices as CSV")
    def download_invoice_csv(self, request, queryset):
        response = HttpResponse(content_type="text/csv")
        response["Content-Disposition"] = "attachment; filename=invoices.csv"
        writer = csv.writer(response)

        writer.writerow(
            [
                "visma_id",
                "name",
                "country",
                "line description",
                "sku",
                "quantity",
                "currency",
                "item_price",
                "discount",
                "total",
            ],
        )
        for invoice in queryset:
            for line in InvoiceLine.objects.filter(invoice=invoice):
                writer.writerow(
                    [
                        invoice.customer.visma_id,
                        invoice.customer.name,
                        invoice.customer.country,
                        line.description,
                        line.sku,
                        line.quantity,
                        line.value.currency,
                        line.item_price.amount,
                        line.discount,
                        line.value.amount,
                    ],
                )

        return response

    @admin.display(description="Sync Status")
    def sync_status_link(self, obj):
        url = reverse("admin:core_syncstatus_changelist") + "?" + urlencode({"invoice__pk__exact": obj.id})
        return format_html("<a href='{}'>Sync Status</a>", url)

    @admin.display(description="Current", boolean=True)
    def current_sync_status(self, obj):
        qs = SyncStatus.objects.filter(invoice=obj)
        if qs.exists():
            return not qs.filter(status=SyncStatus.Status.FAILED).exists()
        return None

    def get_fieldsets(self, request, obj=None):
        fieldsets = super().get_fieldsets(request, obj)
        fieldsets[0][1]["fields"].remove("sync_status_link")
        fieldsets[0][1]["fields"].remove("current_sync_status")
        if obj is not None:
            fieldsets[0][1]["fields"].append(
                ("sync_status_link", "current_sync_status"),
            )
        return fieldsets


class ShipmentLineInline(TabularInlineBase):
    model = ShipmentLine

    template = "admin/inlines/shipment_lines.html"

    search_fields = ["sku"]
    fields = ("sku", "product", "quantity", "serial_no")
    ordering = ["id", "product__sku", "serial_no"]

    autocomplete_fields = ["product"]

    def get_readonly_fields(self, request, obj=None):
        if obj is not None and settings.IS_PROD:
            return ["sku", "product", "quantity"]
        return []

    def has_add_permission(self, request, obj=None):
        return request.user.is_superuser or settings.DEBUG

    def has_delete_permission(self, request, obj=None):
        return request.user.is_superuser or settings.DEBUG


class ShipmentPickedUnitInline(TabularInlinePaginated, StackedInlineBase):
    model = ShipmentPickedUnit

    fields = ("line", "quantity", "serial_no", "collar")
    ordering = ["line", "serial_no"]
    search_fields = ["collar__serial_no"]
    autocomplete_fields = ["line", "collar"]

    per_page = 20

    def has_change_permission(self, request, obj=None):
        return request.user.is_superuser or settings.DEBUG

    def has_add_permission(self, request, obj=None):
        return request.user.is_superuser or settings.DEBUG

    def has_delete_permission(self, request, obj=None):
        return request.user.is_superuser or settings.DEBUG


@admin.register(ShipmentLine, site=unfold_admin_site)
class ShipmentLineAdmin(SimpleHistoryAdmin, UnfoldModelAdmin):
    search_fields = ["sku"]
    autocomplete_fields = ["shipment", "product"]
    # This admin class is only registered so that we can have a autocomplete field in ShipmentPickedUnitInline.

    def has_module_permission(self, request):
        return False


class IsInterNofenceCompanyShipmentListFilter(SimpleListFilter):
    title = "Is to Nofence Company"
    parameter_name = "is_to_nofence_company"

    def lookups(self, request, model_admin):
        return [
            ("yes", "Yes"),
            ("no", "No"),
        ]

    def queryset(self, request, queryset):
        if self.value() == "yes":
            return queryset.filter(
                customer__is_nofence_company_shipment_receiver=True,
                invoice_document__isnull=False,
            )
        elif self.value() == "no":
            return queryset.filter(
                customer__is_nofence_company_shipment_receiver=False,
            )


class ShipmentGoodsReceiptInline(ImmutableMixin, TabularInlineBase):
    model = GoodsReceipt
    fields = ["status", "type", "warehouse"]
    show_change_link = True


@admin.register(Shipment, site=unfold_admin_site)
class ShipmentAdmin(InlineActionsMixin, SimpleHistoryAdmin, UnfoldModelAdmin):
    djangoql_completion_enabled_by_default = False
    list_display = (
        "shipment_no",
        "sales_order_link",
        "customer",
        "from_warehouse",
        "status",
        "transporter",
        "get_tracking_link",
        "picking_status",
        "updated_at",
    )

    search_fields = ["shipment_no", "tracking_number"]

    fieldsets = [
        (
            None,  # No heading for this section
            {
                "fields": [
                    "shipment_no",
                    "status",
                    "delivered_date",
                    "transporter",
                    ("tracking_number", "get_tracking_link"),
                    "return_label_tracking_number",
                    "inco_terms",
                    "from_warehouse",
                    "label_file",
                    "customer",
                    "sales_order",
                    "invoice_document",
                    "picking_status",
                    "packing_started_at",
                    "registration_completed_at",
                    "packing_completed_at",
                ],
            },
        ),
        (
            _("Shipping Label Data"),
            {
                "fields": (
                    "pretty_label_request_payload",
                    "pretty_label_response",
                ),
                "classes": ["collapse in"],
            },
        ),
    ]

    def pretty_label_request_payload(self, obj):
        return format_html("<pre>{}</pre>", pprint.pformat(obj.label_request_payload))

    def pretty_label_response(self, obj):
        return format_html("<pre>{}</pre>", pprint.pformat(obj.label_response))

    readonly_fields = (
        "get_tracking_link",
        "picking_status",
        "pretty_label_request_payload",
        "pretty_label_response",
    )

    autocomplete_fields = ["customer", "sales_order", "invoice_document"]

    ordering = ["-updated_at", "-shipment_no"]

    list_filter = (
        ("delivered_date", RangeDateFilter),
        CustomerFilter,
        "status",
        "from_warehouse",
        SalesOrderFilter,
        AutocompleteFilterFactory("Invoice Document", "invoice_document"),
        IsInterNofenceCompanyShipmentListFilter,
    )

    inlines = [
        ShipmentLineInline,
        ShipmentPickedUnitInline,
    ]

    list_select_related = ["customer__user", "customer__nofence_company", "from_warehouse", "sales_order"]

    actions = [
        "sync_shipment_info_to_hubspot",
        csv_export.streaming_csv_export_action(
            "Export shipment info (CSV)",
            extra_fields=[
                ("sales_order__customer__name", "company_name"),
                ("sales_order__invoice_address__address1", "invoice_address_address1"),
                ("sales_order__invoice_address__address2", "invoice_address_address2"),
                ("sales_order__invoice_address__zip_code", "invoice_address_zip_code"),
                ("sales_order__invoice_address__city", "invoice_address_city"),
                ("sales_order__invoice_address__country", "invoice_address_country"),
                ("sales_order__invoice_address__region", "invoice_address_region"),
                ("sales_order__delivery_address__address1", "delivery_address_address1"),
                ("sales_order__delivery_address__address2", "delivery_address_address2"),
                ("sales_order__delivery_address__zip_code", "delivery_address_zip_code"),
                ("sales_order__delivery_address__city", "delivery_address_city"),
                ("sales_order__delivery_address__country", "delivery_address_country"),
                ("sales_order__delivery_address__region", "delivery_address_region"),
                ("inco_terms", "inco_terms"),
            ],
        ),
        "create_packing_list",
        "create_proforma_invoice",
        "sync_status_from_visma",
        "confirm_goods_receipt",
        "export_shipment_lines",
        "set_packing_complete",
        "change_shipment_warehouse",
        "cancel_shipment",
    ]

    # All possible names must be defined in the list, they may be filered by the get_actions_detail method below.
    actions_detail = ["confirm_goods_receipt", "change_shipment_warehouse", "cancel_shipment"]

    def get_actions_detail(self, request, object_id=None):
        actions = []
        shipment = self.get_object(request, object_id)
        if shipment is not None and shipment.is_intercompany_shipment():
            actions = ["confirm_goods_receipt"]

        if (
            shipment is not None
            and shipment.sales_order
            and shipment.sales_order.status in SalesOrder.STATUSES_FOR_SHIPMENT_ROUTING
        ):
            actions.append("change_shipment_warehouse")

        if shipment is not None and shipment.status not in Shipment.FINAL_STATUSES and shipment.invoice_document:
            actions.append("cancel_shipment")

        return self._filter_unfold_actions_by_permissions(
            request,
            [self.get_unfold_action(action) for action in actions],
            object_id,
        )

    def get_readonly_fields(self, request, obj=None):
        readonly_fields = super().get_readonly_fields(request, obj)
        if obj and settings.IS_PROD:
            # Don't allow change of some values after creation
            readonly_fields = (*readonly_fields, "from_warehouse", "status")
        return readonly_fields

    def changeform_view(self, request, object_id=None, form_url="", extra_context=None):
        if object_id is not None:
            # Add some extra context used by the custom-inline document
            extra_context = extra_context or {}
            shipment: Shipment = self.get_object(request, object_id)
            if (
                shipment is not None
                and shipment.sales_order
                and shipment.sales_order.status in SalesOrder.STATUSES_FOR_SHIPMENT_ROUTING
            ):
                extra_context["show_update_order_message"] = True
        return super().changeform_view(request, object_id, form_url, extra_context)

    def get_queryset(self, request):
        qs = super().get_queryset(request)

        user: User = request.user

        # Used to return an empty list so users that should not have access to shipments will not get access.
        # This is due to the group access. So if no warehouse is selected, the user has access to all shipments.
        if not user.has_perm("core.view_shipment"):
            return qs.none()

        if not user.is_superuser:
            # Limit access to shipments from warehouses defined on any of the user's auth groups
            # or on the user itself. If no warehouses are defined the user will have access to
            # all warehouses.

            warehouse_access = user.get_warehouse_access()

            if warehouse_access:
                filters = Q(from_warehouse__isnull=False, from_warehouse__in=warehouse_access)

                if self.has_confirm_goods_receipt_permission(request):
                    # If the user has this permission we also show shipments that are destined for
                    # the warehouses that the user has access to. (SW-2705)
                    # This does not handle the special case of the Hapro customer, which have their
                    # own warehouse, but that shoudn't be a problem here.
                    filters |= Q(customer__nofence_company__domestic_shipment_warehouse__in=warehouse_access)
                    filters |= Q(customer__nofence_company__international_shipment_warehouse__in=warehouse_access)

                qs = qs.filter(filters)

        return qs

    def get_inlines(self, request, obj: Shipment = None):
        inlines = super().get_inlines(request, obj=obj)
        if obj is not None and obj.is_intercompany_shipment():
            inlines = [*inlines, ShipmentGoodsReceiptInline]
        return inlines

    def get_tracking_link(self, obj: Shipment):
        url = obj.tracking_url
        if url is not None:
            return format_html(
                "<a href='{url}' target='_blank' rel='noopener noreferrer'>{tracking_number}</a>",
                url=url,
                tracking_number=obj.tracking_number,
            )
        return obj.tracking_number

    get_tracking_link.short_description = "Tracking link"

    @admin.display(description="Sales Order", ordering="sales_order")
    def sales_order_link(self, obj):
        if obj.sales_order is not None:
            url = reverse("admin:core_salesorder_change", args=(obj.sales_order.pk,))
            return format_html(
                "<a href='{}'>{}</a>",
                url,
                obj.sales_order.order_reference(),
            )

    @admin.action(description="Trigger shipment status sync to Hubspot")
    def sync_shipment_info_to_hubspot(self, request, queryset):
        count = 0
        for shipment in queryset.all():
            if shipment.sales_order is not None:
                hubspot_tasks.sync_billy_sales_order_shipment_status_to_hubspot.delay(
                    shipment.sales_order.id,
                    metadata={"trigger": "from admin action"},
                )
            count += 1
        self.message_user(
            request,
            f"Started sync of {count} shipments to hubspot",
            messages.SUCCESS,
        )

        return HttpResponseRedirect(request.get_full_path())

    @admin.action(description="Create packing list")
    def create_packing_list(self, request, queryset):
        context = {"shipments": queryset.all()}
        from django.utils import translation

        with translation.override("en"):
            return render(request, "shipments/packing_list.html", context)

    @admin.action(description="Create proforma invoice")
    def create_proforma_invoice(self, request, queryset):
        context = {"shipments": queryset.all()}
        return render(request, "shipments/proforma_invoice.html", context)

    @admin.action(
        description="Sync status from Visma",
        permissions=["visma_status_sync"],
    )
    def sync_status_from_visma(self, request, queryset):
        count = 0
        for shipment in queryset.iterator():
            task_id = visma_tasks.sync_visma_shipment_status_to_billy.delay(shipment.id)
            logger.info(f"Started visma shipment status sync in {task_id}")
            count += 1

        self.message_user(
            request,
            f"Status sync triggered for {count} shipment(s)",
            messages.SUCCESS,
        )

    def has_visma_status_sync_permission(self, request, obj=None):
        return request.user.is_superuser

    @unfold_action(
        description="Confirm Arrival",
        permissions=["confirm_goods_receipt"],
        attrs={"use_post_request": True},
    )
    def confirm_goods_receipt(self, request, queryset=None, object_id=None):
        if object_id:
            queryset = self.get_queryset(request).filter(id=object_id)

        shipments = queryset.all()
        if len(shipments) != 1:
            self.message_user(
                request,
                f"Excactly one shipment must be selected. {len(shipments)} selected.",
                messages.ERROR,
            )
            return
        shipment: Shipment = shipments[0]

        if not shipment.is_intercompany_shipment():
            self.message_user(
                request,
                "This action is only usable for shipments between Nofence Companies",
                messages.ERROR,
            )
            return

        receipt = create_goods_receipt_service().create_receipt_draft_from_shipment(shipment)
        url = reverse("admin:core_goodsreceipt_change", args=(receipt.pk,))
        return HttpResponseRedirect(url)

    def has_confirm_goods_receipt_permission(self, request, obj=None):
        return request.user.has_perm("core.add_goodsreceipt")

    @admin.action(description="Export shipment lines (CSV)")
    def export_shipment_lines(self, request, queryset):
        response = HttpResponse(content_type="text/csv")
        response["Content-Disposition"] = "attachment; filename=shipments.csv"
        writer = csv.writer(response)

        writer.writerow(
            [
                "shipment",
                "salesorder",
                "picking_status",
                "hubspot_id",
                "name",
                "country",
                "product",
                "sku",
                "quantity",
                "picked_quantity",
                "delivered_date",
                "transporter",
                "tracking_number",
                "from_warehouse",
            ],
        )
        for shipment in queryset:
            for line in ShipmentLine.objects.filter(shipment=shipment):
                writer.writerow(
                    [
                        shipment,
                        shipment.sales_order,
                        shipment.picking_status,
                        shipment.customer.hubspot_id,
                        shipment.customer.name,
                        shipment.customer.country,
                        line.product,
                        line.sku,
                        line.quantity,
                        line.picked_units_quantity,
                        shipment.delivered_date,
                        shipment.transporter,
                        shipment.tracking_number,
                        shipment.from_warehouse,
                    ],
                )

        return response

    @admin.action(
        description="Set packing complete",
        permissions=["pick_n_pack"],
    )
    def set_packing_complete(self, request, queryset):
        service = create_shipment_service()
        for shipment in queryset.iterator():
            try:
                with transaction.atomic():
                    service.set_picking_complete(shipment)
                self.message_user(
                    request,
                    f"Set shipment complete: {shipment}",
                    messages.SUCCESS,
                )
            except Exception as e:
                logger.exception("Failed to set shipment complete")
                self.message_user(
                    request,
                    f"Failed to set shipment complete: {shipment} - {e}",
                    messages.ERROR,
                )

    def has_pick_n_pack_permission(self, request, obj=None):
        return request.user.has_perm("core.pick_and_pack_app")

    @unfold_action(
        description="Change Shipment Warehouse",
        permissions=["change_shipment_warehouse"],
        attrs={"use_post_request": True, "form_name": "change-shipment-warehouse"},
    )
    def change_shipment_warehouse(self, request, queryset=None, object_id=None, *args, **kwargs):
        if object_id:
            queryset = self.get_queryset(request).filter(id=object_id)

        so_queryset = SalesOrder.objects.filter(shipments__in=queryset)

        if so_queryset.count() == 0:
            self.message_user(
                request, "No sales orders found. Only sales order shipments currently supported.", messages.INFO
            )
            return redirect(self.get_redirect_url_for_inline_action(request, object_id))

        return admin_actions.do_change_shipment_warehouse(
            self, request, so_queryset, object_id, original_queryset=queryset, *args, **kwargs
        )

    def has_change_shipment_warehouse_permission(self, request, obj=None):
        return request.user.has_perm("core.change_shipment")

    @unfold_action(
        description="Cancel shipment",
        permissions=["cancel_shipment"],
        attrs={"use_post_request": True},
    )
    def cancel_shipment(self, request, queryset=None, object_id=None):
        template = "admin/actions/cancel_shipment.html"

        if object_id:
            queryset = self.get_queryset(request).filter(id=object_id)

        if "cancel" in request.POST:
            return redirect(self.get_redirect_url_for_inline_action(request, object_id))
        elif "confirm" in request.POST:
            form = ConfirmCancelShipmentForm(request.POST)
            if form.is_valid():
                for shipment in queryset.all():
                    shipment: Shipment
                    if shipment.status in Shipment.FINAL_STATUSES:
                        self.message_user(
                            request,
                            f"Skipped shipment {shipment.shipment_no}. Shipment is completed ({shipment.get_status_display()})",
                            messages.INFO,
                        )
                    elif not shipment.invoice_document:
                        self.message_user(
                            request,
                            f"Skipped shipment {shipment.shipment_no}. Only shipments created from invocies are supported. "
                            "To cancel sales order shipments, cancel the order instead.",
                            messages.INFO,
                        )
                    else:
                        inventory_service = create_inventory_service()
                        with transaction.atomic():
                            shipment.status = Shipment.Status.DELETED
                            shipment._change_reason = "Cancelled from admin action"
                            shipment.save(update_fields=["status", "updated_at"])
                            inventory_service.revert_inventory_reservation_for_invoice_document(
                                shipment.from_warehouse, shipment.invoice_document
                            )
                        self.message_user(
                            request,
                            f"Cancelled shipment {shipment.shipment_no}.",
                            messages.SUCCESS,
                        )
                return redirect(self.get_redirect_url_for_inline_action(request, object_id))
        else:
            form = ConfirmCancelShipmentForm()

        opts = self.model._meta

        context = {
            **self.admin_site.each_context(request),
            "title": "Change Shipment Warehouse",
            "subtitle": "",
            "selected_objects": queryset,
            "form": form,
            "opts": opts,
            "app_label": opts.app_label,
            "sales_orders": queryset,
            **self.get_inline_action_context(request),
        }

        return render(request, template, context=context)

    def has_cancel_shipment_permission(self, request, obj=None):
        return request.user.has_perm("core.change_shipment")


class SalesOrderLineInline(TabularInlineBase):
    model = SalesOrderLine
    autocomplete_fields = ["product"]

    template = "admin/inlines/sales_order_lines.html"

    fields = [
        "name",
        "description",
        "sku",
        "product",
        "price",
        "quantity",
        "discount",
        "discount_percentage",
    ]

    ordering = ["display_order", "id"]

    if settings.DEBUG:
        fields += ["generic_sku", "source_id", "display_order"]

    def get_readonly_fields(self, request, obj=None):
        if obj is not None and settings.IS_PROD:
            return ["sku", "product", "quantity"]
        return []

    def has_add_permission(self, request, obj=None):
        return request.user.is_superuser or settings.DEBUG

    def has_delete_permission(self, request, obj=None):
        return request.user.is_superuser or settings.DEBUG


class SalesOrderDeliveryAddressInline(TabularInlineBase):
    model = SalesOrderDeliveryAddress


class SalesOrderInvoiceAddressInline(ImmutableMixin, TabularInlineBase):
    model = SalesOrderInvoiceAddress


class SalesOrderSubscriptionChangeInline(ImmutableMixin, TabularInlineBase):
    model = SubscriptionChange
    show_change_link = True
    fields = [
        "start_date",
        "end_date",
        "visma_document_id",
        "price_plan",
        "quantity_delta",
    ]


class SalesOrderShipmentInline(ImmutableMixin, TabularInlineBase):
    model = Shipment
    show_change_link = True
    exclude = ["customer"]


class SalesOrderInvoiceDocumentInline(ImmutableMixin, TabularInlineBase):
    model = InvoiceDocument
    show_change_link = True

    fields = [
        "reference_number",
        "provider",
        "nofence_company",
        "status",
        "type",
        "total",
        "balance",
        "due_date",
    ]

    readonly_fields = ["reference_number"]


class SalesOrderTermsAndConditionsInline(TabularInlineBase):
    model = SalesOrder.terms_and_conditions.through
    extra = 0
    verbose_name = _("Terms and Conditions")
    verbose_name_plural = _("Terms and Conditions")


class PipelineStageLabelFilter(SimpleListFilter):
    title = "Pipeline stage label"
    parameter_name = "pipeline_stage_label"

    def lookups(self, request, model_admin):
        qs = (
            HubSpotPipelineStageModel.objects.filter(pipeline__object_type="deal")
            .values_list("label", flat=True)
            .order_by("label")
        )
        # Filter the labels by hubspot pipeline if selected in the other filter
        hubspot_pipeline = request.GET.get("hubspot_pipeline", None)
        if hubspot_pipeline:
            qs = qs.filter(pipeline__id=hubspot_pipeline)
        return [(label, label) for label in qs.distinct()]

    def queryset(self, request, queryset):
        if self.value():
            return queryset.filter(
                hubspot_pipeline_stage__label=self.value(),
            )


class TaxCalculationInline(ImmutableMixin, TabularInlineBase):
    model = TaxCalculation
    fields = ["amount_total", "tax_amount_exclusive", "user", "source"]
    show_change_link = True


class CardPaymentInline(ImmutableMixin, TabularInlineBase):
    model = CardPayment
    fields = ["amount", "status"]
    show_change_link = True


class ShipmentRoutingEvalLogInline(ImmutableMixin, TabularInlineBase):
    model = ShipmentRoutingEvalLog
    fields = ["result", "shipment_warehouse", "filter", "created_at"]
    readonly_fields = ["created_at"]
    ordering = ["eval_index"]
    classes = ["collapse"]
    verbose_name_plural = "Shipment Routing Eval Log"


class CreatedByImpersonationFilter(SimpleListFilter):
    title = "Created by impersonation"
    parameter_name = "by_impersonation"

    def lookups(self, request, model_admin):
        return [("yes", "Yes"), ("no", "No")]

    def queryset(self, request, queryset):
        if self.value() == "yes":
            return queryset.filter(
                impersonator__isnull=False,
            )
        elif self.value() == "no":
            return queryset.filter(
                impersonator__isnull=True,
            )


@admin.register(ShipmentRoutingRule, site=unfold_admin_site)
class ShipmentRoutingRuleAdmin(DynamicListEditableMixin, SortableAdminMixin, SimpleHistoryAdmin, UnfoldModelAdmin):
    list_display = ["filter", "shipment_warehouse", "is_active"]

    list_filter_sheet = True

    ordering = ["sort_order"]

    search_fields = ["filter"]

    list_filter = ["is_active"]
    dynamic_list_editable = ["is_active"]

    readonly_fields = ["hit_count"]

    add_form_template = "admin/shipment_routing_change_form.html"
    change_form_template = "admin/shipment_routing_change_form.html"

    actions_detail = ["preview_matching_orders"]

    def add_view(self, request, form_url="", extra_context=None):
        extra_context = extra_context or {}
        extra_context.update(self.get_djangoql_context())
        return super().add_view(request, form_url, extra_context)

    def change_view(self, request, object_id, form_url="", extra_context=None):
        extra_context = extra_context or {}
        extra_context.update(self.get_djangoql_context())
        return super().change_view(request, object_id, form_url, extra_context)

    def get_djangoql_context(self):
        return {
            "introspections": json.dumps(
                DjangoQLSchemaSerializer().serialize(
                    shipment_routing.SalesOrderRoutingQLSchema(SalesOrder),
                )
            )
        }

    @unfold_action(
        description="Preview Rule",
    )
    def preview_matching_orders(self, request, object_id=None):
        template = "admin/actions/preview_shipment_routing_rule.html"

        rule: ShipmentRoutingRule = self.get_queryset(request).get(id=object_id)

        routing_filter = request.GET.get("filter") or rule.filter

        matching_orders = []
        error = ""

        try:
            if request.method == "POST":
                if request.POST.get("action") == "save":
                    rule.filter = request.POST.get("filter")
                    rule.full_clean()
                    rule.save()
                return redirect(reverse("admin:core_shipmentroutingrule_change", args=(rule.pk,)))

            matching_orders = (
                shipment_routing.find_orders_matching_filter(routing_filter)
                .exclude(order_type__in=[SalesOrder.Type.PROPOSAL])
                .order_by("-created_at")
                .select_related("nofence_company", "customer")[0:30]
            )
        except Exception as e:
            error = str(e)

        opts = self.model._meta

        orders = []
        for order in matching_orders:
            lines: list[SalesOrderLine] = list(order.lines.select_related("product").all())
            orders.append(
                {
                    "order": order,
                    "cattle_collar_count": sum(
                        li.quantity
                        for li in lines
                        if li.product.is_collar_product() and li.product.get_collar_product_type() == CATTLE
                    ),
                    "sheep_goat_collar_count": sum(
                        li.quantity
                        for li in lines
                        if li.product.is_collar_product() and li.product.get_collar_product_type() == SHEEP_GOAT
                    ),
                }
            )

        context = {
            **self.admin_site.each_context(request),
            "title": "Preview",
            "subtitle": "Orders Matching Routing Rule",
            "opts": opts,
            "app_label": opts.app_label,
            "shipment_routing_rule": rule,
            "routing_filter": routing_filter,
            "matching_orders": orders,
            "media": DjangoQLSearchMixin.media.__get__(self),
            "error": error,
            **self.get_djangoql_context(),
        }
        return render(request, template, context=context)


@admin.register(SalesOrder, site=unfold_admin_site)
class SalesOrderAdmin(InlineActionsMixin, SimpleHistoryAdmin, UnfoldModelAdmin):
    list_display = (
        "created_at",
        "status",
        "customer_link",
        "show_hubspot_url",
        "hubspot_pipeline_stage",
        "estimated_delivery_date",
        "pre_paid",
        "accounting_system",
        "order_no",
        "generated_order_number",
        "updated_at",
    )

    list_select_related = [
        "customer__user",
        "customer__nofence_company",
        "hubspot_pipeline",
        "hubspot_pipeline_stage__pipeline",
    ]

    autocomplete_fields = ["customer", "return_and_replacement"]

    readonly_fields = [
        "nofence_company",
        "hubspot_pipeline",
        "hubspot_pipeline_stage",
        "sync_status_link",
        "current_sync_status",
        "collars_blocked_by_pending_payment",
        "delivered_at",
        "booked_at",
        "paid_at",
        "deleted_at",
        "shipped_at",
        "show_hubspot_url",
        "hubspot_create_date",
        "impersonator",
    ]

    search_fields = [
        "description",
        "source_id",
        "customer__name",
        "customer__user__name",
        "accounting_order_id",
        "accounting_order_ref",
        "generated_order_number",
    ]

    list_filter = (
        ("created_at", RangeDateFilter),
        ("estimated_delivery_date", RangeDateFilter),
        "status",
        "source",
        "order_type",
        "nofence_company",
        "pre_paid",
        "post_payment",
        "payment_status",
        "accounting_system",
        CustomerFilter,
        CreatedByImpersonationFilter,
        AutocompleteFilterFactory("Impersonator", "impersonator"),
        "customer__country",
        "shipment_warehouse",
        SalesOrderProductTypeFilter,
        SalesOrderInventoryReservationFilter,
        "collars_blocked_by_pending_payment",
        AutocompleteFilterFactory("HubSpotPipeline", "hubspot_pipeline"),
        AutocompleteFilterFactory("HubSpotPipelineStage", "hubspot_pipeline_stage"),
        PipelineStageLabelFilter,
    )

    ordering = ["-created_at"]
    date_hierarchy = "estimated_delivery_date"

    inlines = [
        SalesOrderDeliveryAddressInline,
        SalesOrderInvoiceAddressInline,
        SalesOrderLineInline,
        SalesOrderSubscriptionChangeInline,
        SalesOrderShipmentInline,
        SalesOrderInvoiceDocumentInline,
        SalesOrderTermsAndConditionsInline,
        TaxCalculationInline,
        CardPaymentInline,
        ShipmentRoutingEvalLogInline,
    ]
    exclude = ["terms_and_conditions", "collar_block_processed"]

    actions = [
        "trigger_resync_from_hubspot",
        "trigger_status_sync_from_visma",
        csv_export.streaming_csv_export_action(
            "Export sales order info (CSV)",
            fields=[
                "created_at",
                "name",
                "customer_link",
                "status",
                "estimated_delivery_date",
                "accounting_system",
                "pre_paid",
                "order_no",
                "updated_at",
                "booked_at",
                "paid_at",
                "deleted_at",
                "shipped_at",
                "total",
            ],
        ),
        "trigger_shipment_routing",
        "reserve_inventory",
        "release_inventory_reservation",
        "send_payment_request_email",
        "send_vat_invoice_email",
        "send_order_confirmation_email",
        "unblock_blocked_collars",
        "send_to_hapro",
        "trigger_sales_order_changed_signal",
        "defer_payment_and_move_to_pending_delivery",
        "credit_payment_request_for_post_paid_order",
        "mark_as_manually_paid",
        "cancel_order",
        "change_shipment_warehouse",
    ]

    actions_detail = [
        "change_shipment_warehouse",
    ]

    def get_actions_detail(self, request, object_id=None):
        actions = []
        so = self.get_object(request, object_id)
        if so is not None and so.status in SalesOrder.STATUSES_FOR_SHIPMENT_ROUTING:
            actions = ["change_shipment_warehouse"]

        return self._filter_unfold_actions_by_permissions(
            request,
            [self.get_unfold_action(action) for action in actions],
            object_id,
        )

    def changeform_view(self, request, object_id=None, form_url="", extra_context=None):
        if object_id is not None:
            # Add some extra context used by the custom-inline document
            extra_context = extra_context or {}
            so: SalesOrder = self.get_object(request, object_id)
            if so:
                extra_context["show_update_order_message"] = so.status in SalesOrder.STATUSES_FOR_SHIPMENT_ROUTING
        return super().changeform_view(request, object_id, form_url, extra_context)

    def show_hubspot_url(self, obj):
        if obj.source == SalesOrderSource.HUBSPOT and obj.source_id:
            return format_html(
                "<a href='{url}'>{source_id}</a>",
                source_id=obj.source_id,
                url=f"https://app.hubspot.com/contacts/{settings.HUBSPOT_PORTAL_ID}/deal/{obj.source_id}",
            )

        if obj.source == SalesOrderSource.WEBSHOP and obj.hubspot_deal_id:
            return format_html(
                "<a href='{url}'>{hubspot_deal_id}</a>",
                hubspot_deal_id=obj.hubspot_deal_id,
                url=f"https://app.hubspot.com/contacts/{settings.HUBSPOT_PORTAL_ID}/deal/{obj.hubspot_deal_id}",
            )

        return "-"

    show_hubspot_url.short_description = "Hubspot"

    def customer_link(self, obj):
        url = reverse("admin:core_customer_change", args=(obj.customer.pk,))
        return format_html("<a href='{}'>{}</a>", url, obj.customer)

    customer_link.admin_order_field = "customer"
    customer_link.short_description = "customer"

    def name(self, obj):
        if obj.source == "HUBSPOT":
            return f"HubSpot Deal - {obj.order_number}"
        return obj.order_number

    def order_no(self, obj):
        return obj.accounting_order_ref or obj.accounting_order_id

    def has_add_permission(self, request, obj=None):
        return request.user.is_superuser or (settings.DEBUG and super().has_add_permission(request))

    def has_delete_permission(self, request, obj=None):
        return request.user.is_superuser or (settings.DEBUG and super().has_delete_permission(request, obj))

    def get_readonly_fields(self, request, obj):
        fields = super().get_readonly_fields(request, obj=obj)
        if obj and obj.status not in [SalesOrderStatus.CONFIRMED] and settings.IS_PROD:
            # Warehouse should not be changed after pending delivery.
            fields = (*fields, "shipment_warehouse")
        if obj and settings.IS_PROD:
            # Don't allow manual changes to status after creation
            fields = (*fields, "status")
        if not request.user.has_perm("core.set_order_post_payment"):
            fields = (*fields, "post_payment")
        return fields

    def get_fieldsets(self, request, obj=None):
        fieldsets = super().get_fieldsets(request, obj)
        fieldsets[0][1]["fields"].remove("sync_status_link")
        fieldsets[0][1]["fields"].remove("current_sync_status")
        if obj and settings.IS_PROD:
            # Keep the original position of the field when it's readonly
            fieldsets[0][1]["fields"].insert(fieldsets[0][1]["fields"].index("payment_status"), "status")
        if obj is not None:
            fieldsets[0][1]["fields"].append(
                ("sync_status_link", "current_sync_status"),
            )

        return fieldsets

    @admin.display(description="Sync Status")
    def sync_status_link(self, obj):
        url = reverse("admin:core_syncstatus_changelist") + "?" + urlencode({"sales_order__pk__exact": obj.id})
        return format_html("<a href='{}'>Sync Status</a>", url)

    @admin.display(description="Current", boolean=True)
    def current_sync_status(self, obj):
        qs = SyncStatus.objects.filter(sales_order=obj)
        if qs.exists():
            return not qs.filter(status=SyncStatus.Status.FAILED).exists()
        return None

    @admin.action(description="Trigger re-sync from HubSpot")
    def trigger_resync_from_hubspot(self, request, queryset):
        missing_source_ids = []
        hubspot_order_ids = []
        for order in queryset:
            if not order.source_id:
                missing_source_ids.append(order.id)
            elif order.source == SalesOrderSource.HUBSPOT:
                hubspot_order_ids.append((order.id, order.source_id))

        for order_id, hubspot_id in hubspot_order_ids:
            task = hubspot_tasks.sync_hubspot_deal_to_billy.delay(
                hubspot_id,
                metadata={"trigger": "Manual trigger from admin"},
            )
            logger.info(
                f"Manually triggered hubspot deal sync for order {order_id} in task {task.id}",
            )

        if missing_source_ids:
            self.message_user(
                request,
                f"The following {len(missing_source_ids)} order(s) "
                "are missing source_id and can't be synced "
                f"{', '.join(map(str, missing_source_ids))}",
                messages.WARNING,
            )

        if hubspot_order_ids:
            self.message_user(
                request,
                f"Hubspot sync triggered for {len(hubspot_order_ids)} order(s)",
                messages.SUCCESS,
            )

    @admin.action(description="Trigger shipment routing")
    def trigger_shipment_routing(self, request, queryset):
        count = 0
        for so in queryset.all():
            logger.info(f"Triggering shipment routing for sales order {so} from admin")
            shipment_routing.update_preferred_shipment_warehouse(so)
            count += 1

        self.message_user(
            request,
            f"Triggered shipment routing for {count} sales orders",
            messages.SUCCESS,
        )

    @admin.action(description="Mark order as manually paid", permissions=["mark_as_paid"])
    def mark_as_manually_paid(self, request, queryset):
        """Can be used if someone forget to use the option in the web shop."""
        for so in queryset.all():
            so: SalesOrder
            if not so.is_web_shop_order():
                self.message_user(request, f"{so} is not a web shop order", messages.ERROR)
            elif so.status != SalesOrderStatus.PENDING_PAYMENT:
                self.message_user(request, f"{so} status pending payment", messages.ERROR)
            else:
                logger.info(f"Marking sales order {so} as paid from admin")
                if so.payment_method != SalesOrder.PaymentMethod.MANUALLY_PAID:
                    so.payment_method = SalesOrder.PaymentMethod.MANUALLY_PAID
                    so.payment_status = SalesOrder.PaymentStatus.PAID
                    so._change_reason = "Set manually paid from admin"
                    so.save(update_fields=["payment_method", "payment_status", "updated_at"])
                if so.accounting_payment_request_document is not None:
                    create_sales_order_service().transition_to_pending_delivery(so)
                    self.message_user(
                        request,
                        f"Marking {so} as paid. It may take about a minute to finish. "
                        "We assume that the payment for the order has been handled elsewhere.",
                        messages.SUCCESS,
                    )
                else:
                    self.message_user(
                        request,
                        f"{so} payment request document is missing.",
                        messages.ERROR,
                    )

    def has_mark_as_paid_permission(self, request, obj=None):
        # If you can change invoice you can also mark things as paid
        return request.user.has_perm("core.change_invoicedocument")

    @admin.action(description="Reserve Inventory")
    def reserve_inventory(self, request, queryset):
        inventory = create_inventory_service()

        count = 0
        for so in queryset.all():
            logger.info(
                f"Triggering inventory reservation for sales order {so} from admin",
            )
            valid_statuses = [
                SalesOrderStatus.CONFIRMED,
                SalesOrderStatus.PENDING_PAYMENT,
                SalesOrderStatus.PENDING_DELIVERY,
            ]

            so: SalesOrder
            if so.status not in valid_statuses:
                self.message_user(
                    request,
                    f"{so} status '{so.get_status_display()}' not valid for inventory reservation.",
                    messages.WARNING,
                )
            elif so.shipment_warehouse is not None:
                inventory.reserve_inventory_for_sales_order(so.shipment_warehouse, so)
                count += 1

        if count > 0:
            self.message_user(
                request,
                f"Reserved inventory for {count} sales orders",
                messages.SUCCESS,
            )

    @admin.action(description="Release Inventory Reservation")
    def release_inventory_reservation(self, request, queryset):
        inventory = create_inventory_service()

        count = 0
        for so in queryset.all():
            logger.info(
                f"Triggering release of inventory reservation for sales order {so} from admin",
            )
            if so.shipment_warehouse is not None:
                inventory.revert_inventory_reservation_for_sales_order(
                    so.shipment_warehouse,
                    so,
                )
                count += 1

        self.message_user(
            request,
            f"Released reserved inventory for {count} sales orders",
            messages.SUCCESS,
        )

    @admin.action(description="Send Payment Request Email")
    def send_payment_request_email(self, request, queryset):
        emails = create_email_service()

        count = 0
        for so in queryset.all():
            so: SalesOrder
            if so.pre_paid and so.status == SalesOrderStatus.PENDING_PAYMENT:
                emails.send_payment_request_email(so)
                logger.info(
                    f"Triggering payment request email for sales order {so} from admin",
                )
                count += 1

        self.message_user(
            request,
            f"Started sending of payment request email for {count} sales order(s)",
            messages.SUCCESS,
        )

    @admin.action(description="Send VAT Invoice Email")
    def send_vat_invoice_email(self, request, queryset):
        emails = create_email_service()

        count = 0
        for so in queryset.all():
            so: SalesOrder
            if so.pre_paid and so.status == SalesOrderStatus.DELIVERED:
                emails.send_vat_invoice_email(so)
                logger.info(
                    f"Triggering vat invoice email for sales order {so} from admin",
                )
                count += 1

        self.message_user(
            request,
            f"Started sending of vat invoice email for {count} sales order(s)",
            messages.SUCCESS,
        )

    @admin.action(description="Send Order Confirmation Email")
    def send_order_confirmation_email(self, request, queryset):
        emails = create_email_service()

        count = 0
        for so in queryset.all():
            so: SalesOrder
            if so.status == SalesOrderStatus.PENDING_DELIVERY:
                emails.send_order_confirmation_email(so)
                logger.info(
                    f"Triggering order confirmation email for sales order {so} from admin",
                )
                count += 1

        self.message_user(
            request,
            f"Started sending of order confirmation email for {count} sales order(s)",
            messages.SUCCESS,
        )

    @admin.action(description="Cancel order", permissions=["cancel_order"])
    def cancel_order(self, request, queryset):
        from core.salesorders.cancel_reasons import other

        service = create_sales_order_service()
        for so in queryset.all():
            so: SalesOrder
            if so.source != SalesOrderSource.WEBSHOP:
                self.message_user(
                    request,
                    f"Could not cancel {so.order_number} because source is not from WebShop",
                    messages.INFO,
                )
            elif so.status not in [
                SalesOrderStatus.CONFIRMED,
                SalesOrderStatus.PENDING_PAYMENT,
                SalesOrderStatus.PENDING_DELIVERY,
            ]:
                self.message_user(
                    request,
                    f"Could not cancel {so.order_number} because of status {so.get_status_display()}",
                    messages.INFO,
                )
            else:
                was_pending_delivery = so.status == SalesOrderStatus.PENDING_DELIVERY
                service.cancel_order(so, other, "Cancelled from admin.")
                if was_pending_delivery:
                    self.message_user(
                        request,
                        f"Cancelled order {so.order_number}. "
                        "Order was pending delivery, make sure any refunds to the customer is handed if necessary.",
                        messages.SUCCESS,
                    )
                else:
                    self.message_user(
                        request,
                        f"Cancelled order {so.order_number}",
                        messages.SUCCESS,
                    )

    def has_cancel_order_permission(self, request, obj=None):
        return request.user.has_perm("core.change_salesorder")

    @admin.action(
        description="Unblock collars pending payment",
        permissions=["unblock_collars"],
    )
    def unblock_blocked_collars(self, request, queryset):
        if queryset.count() > 1:
            self.message_user(
                request,
                "Only a single order can unblocked at a time.",
                messages.ERROR,
            )
            return

        blocker = create_collar_blocker()

        for so in queryset.all():
            so: SalesOrder
            blocker.unblock_collars_on_sales_order(so)
            logger.info(f"Unblocking collars on {so} from admin")
            break

        self.message_user(
            request,
            f"Collars on {so} has been unblocked",
            messages.SUCCESS,
        )

    def has_unblock_collars_permission(self, request, obj=None):
        # Not sure what's the best permissions. Maybe we should add a customer one,
        # but for now allow anyone that can change customer to unblock collars.
        return request.user.has_perm("core.change_customer")

    @admin.action(description="Send to Hapro", permissions=["send_to_hapro"])
    def send_to_hapro(self, request, queryset):
        queryset = queryset.select_related("shipment_warehouse").prefetch_related("lines__product")
        skipped = []
        to_move = []
        hapro_warehouse = Warehouse.objects.hapro()
        for so in queryset.all():
            so: SalesOrder

            if so.status != SalesOrderStatus.CONFIRMED:
                skipped.append(f"{so.order_number} status {so.status}")
                continue
            elif so.shipment_warehouse == hapro_warehouse:
                skipped.append(f"{so.order_number} already set to Hapro")
                continue

            collar_counts = Counter()
            other_counts = Counter()
            for line in so.lines.all():
                line: SalesOrderLine
                product: Product = line.product
                collar_type = product.get_collar_type()
                if collar_type is not None:
                    collar_counts[collar_type] += 1
                elif product.model:
                    product_type = {
                        "SG": SHEEP_GOAT,
                        "C": CATTLE,
                    }.get(product.model[0:2], None)
                    if product_type is not None:
                        other_counts[product_type] += 1

            total_collars = sum(collar_counts.values())
            if total_collars == 0:
                skipped.append(f"{so.order_number} no collars")
            elif collar_counts[SHEEP_GOAT] > 0:
                skipped.append(f"{so.order_number} SG collars")
            elif other_counts[SHEEP_GOAT] > 0:
                skipped.append(f"{so.order_number} SG accessories")
            else:
                to_move.append(so)

        if to_move:
            for so in to_move:
                so.shipment_warehouse = hapro_warehouse

            bulk_update_with_history(to_move, SalesOrder, ["shipment_warehouse", "updated_at"], batch_size=30)

            self.message_user(
                request,
                f"Sent {len(to_move)} order(s) to {hapro_warehouse}",
                messages.SUCCESS,
            )

        if skipped:
            reasons = ", ".join(skipped)
            self.message_user(
                request,
                f"{len(skipped)} order(s) skipped: {reasons}",
                messages.WARNING,
            )

    def has_send_to_hapro_permission(self, request, obj=None):
        return request.user.has_perm("core.change_salesorder")

    @admin.action(description="Trigger sales order changed signal")
    def trigger_sales_order_changed_signal(self, request, queryset):
        for so in queryset.all():
            so: SalesOrder
            logger.info(f"Triggering sales order changed signal for {so} from admin")
            signals.billy_sales_order_changed.send(
                sender=None,
                sales_order_id=so.id,
            )
        self.message_user(
            request,
            f"Triggered sales order changed signal for {queryset.count()} sales orders",
            messages.SUCCESS,
        )

    @admin.action(
        description="Defer payment and proceed to pending delivery",
        permissions=["set_post_payment"],
    )
    def defer_payment_and_move_to_pending_delivery(self, request, queryset):
        orders = queryset

        order_service = create_sales_order_service()

        already_post_payment = 0
        not_pending_payment = 0
        no_invoice = 0
        moved = 0
        exeeded_limit = 0
        exeeded_limit_values = set()
        no_limit_defined = 0
        no_limit_defined_values = set()
        hubspot_deals = False

        service = create_sales_order_service()

        for so in orders.all():
            so: SalesOrder
            if so.post_payment:
                already_post_payment += 1
                continue
            elif so.status != SalesOrderStatus.PENDING_PAYMENT:
                not_pending_payment += 1
                continue
            elif so.accounting_payment_request_document is None:
                no_invoice += 1
                continue

            max_unpaid_amount = so.nofence_company.get_max_unpaid_amount_in_post_payment(so.currency)
            if max_unpaid_amount is None:
                no_limit_defined += 1
                no_limit_defined_values.add((so.nofence_company.code, str(so.currency)))
                continue

            try:
                salesorders_validation.validate_order_for_post_payment(so)
            except salesorders_validation.PostPaymentLimitError as e:
                exeeded_limit += 1
                exeeded_limit_values.add(f"{e}")
                continue

            with transaction.atomic():
                service.setup_order_for_post_payment(so)

                if so.source == SalesOrderSource.WEBSHOP:
                    order_service.transition_to_pending_delivery(so)
                else:
                    signal_notifiers.notify_sales_order_changed(so.id, sender=self.__class__)
                    hubspot_deals = True
                moved += 1

        if already_post_payment:
            self.message_user(
                request,
                f"Skipped {already_post_payment} order(s) already set to post payment.",
                messages.WARNING,
            )
        if not_pending_payment:
            self.message_user(
                request,
                f"Skipped {not_pending_payment} order(s) not having status Pending Payment.",
                messages.WARNING,
            )
        if no_invoice:
            self.message_user(
                request,
                f"Skipped {no_invoice} order(s) not having a invoie document. "
                "You may have to wait a bit for the invoice document to be synced back to billy.",
                messages.WARNING,
            )
        if exeeded_limit:
            self.message_user(
                request,
                f"Skipped {exeeded_limit} order(s) because total unpaid amount will exeeded "
                f"max unpaid amount limit on nofence company. {exeeded_limit_values}",
                messages.WARNING,
            )
        if no_limit_defined:
            self.message_user(
                request,
                f"Skipped {no_limit_defined} order(s) because no post payment limit defined on nofence "
                f"company. Missing definitions: {no_limit_defined_values}",
                messages.WARNING,
            )
        if moved:
            self.message_user(
                request,
                f"Moved {moved} order(s) to Pending Delivery and set to use post payment.",
                messages.SUCCESS,
            )
        if hubspot_deals:
            self.message_user(
                request,
                "The status of the hubspot order may take a few seconds to be synced back from hubspot.",
                messages.SUCCESS,
            )

    def has_set_post_payment_permission(self, request, obj=None):
        return request.user.has_perm("core.set_order_post_payment")

    @admin.action(
        description="Credit payment request for post paid order",
        permissions=["set_post_payment"],
    )
    def credit_payment_request_for_post_paid_order(self, request, queryset):
        for so in queryset.all():
            so: SalesOrder
            if so.post_payment and so.accounting_payment_request_document:
                signal_notifiers.notify_sales_order_marked_for_post_payment(so.id, sender=self.__class__)
                self.message_user(
                    request,
                    f"Triggered payment request credit note for {so.order_number}",
                    messages.SUCCESS,
                )
            elif not so.accounting_payment_request_document:
                self.message_user(
                    request,
                    f"Order {so.order_number} has no payment request document, only Visma orders (NO) has this. "
                    "Credit note not created.",
                    messages.WARNING,
                )
            else:
                self.message_user(
                    request,
                    f"Order {so.order_number} is not using post payment. Credit note not created.",
                    messages.WARNING,
                )

    @unfold_action(
        description="Change Shipment Warehouse",
        permissions=["change_shipment_warehouse"],
        attrs={"use_post_request": True, "form_name": "change-shipment-warehouse"},
    )
    def change_shipment_warehouse(self, request, queryset=None, object_id=None, *args, **kwargs):
        if object_id:
            queryset = self.get_queryset(request).filter(id=object_id)

        return admin_actions.do_change_shipment_warehouse(self, request, queryset, object_id, *args, **kwargs)

    def has_change_shipment_warehouse_permission(self, request, obj=None):
        return request.user.has_perm("core.change_shipment")


@admin.register(KeyValue, site=unfold_admin_site)
class KeyValueAdmin(ModelAdminBase, UnfoldModelAdmin):
    list_display = ("key", "value")


class InventoryProductStatusFilter(SimpleListFilter):
    title = "Product status"
    parameter_name = "product__status"

    def lookups(self, request, model_admin):
        return [
            ("ACTIVE", "Active"),
            ("INACTIVE", "Inactive"),
        ]

    def choices(self, changelist: ChangeList):
        # get rid of the "All" choice
        for lookup, title in self.lookup_choices:
            yield {
                "selected": self.value() == lookup,
                "query_string": changelist.get_query_string(
                    {
                        self.parameter_name: lookup,
                    },
                    [],
                ),
                "display": title,
            }

    def queryset(self, request, queryset):
        if self.value() is None:
            self.used_parameters[self.parameter_name] = Product.ProductStatus.ACTIVE
        return queryset.filter(product__status=self.value())


def get_is_active_filter_with_default(title: str, skip_on_query_param: str = None):
    # If we name the filter BooleanFieldListFilter, unfold will use the horizintal layout.
    class BooleanFieldListFilter(admin.BooleanFieldListFilter):
        """is_active filter with default set to True"""

        def __init__(self, field, request, *args, **kwargs):
            super().__init__(field, request, *args, **kwargs)

            self.horizontal = True  # Will make the filter layout horizontal from unfold version 0.50.0

            if self.lookup_kwarg not in self.used_parameters:
                # Sometimes we may skip the default value, as it could be confusing in some cases
                # if one of the other filters are selected and nothing is shown when the is activate
                # filter was not explicitly set. E.g. when explicitly filtering on a warehouse that has been disabled.
                if skip_on_query_param and skip_on_query_param not in request.GET:
                    self.used_parameters[self.lookup_kwarg] = True
                    self.lookup_val = "True"
            elif self.used_parameters[self.lookup_kwarg] == "ALL":
                del self.used_parameters[self.lookup_kwarg]

            self.title = title

        def choices(self, changelist):
            choices = [
                ("ALL", "All"),
                ("True", "Yes"),
                ("False", "No"),
            ]
            for lookup, title in choices:
                yield {
                    "selected": self.lookup_val == lookup,
                    "query_string": changelist.get_query_string({self.lookup_kwarg: lookup}, [self.lookup_kwarg2]),
                    "display": title,
                }

        def queryset(self, request, queryset):
            return queryset.filter(**self.used_parameters)

    return BooleanFieldListFilter


def get_is_shop_product_filter():
    # If we name the filter BooleanFieldListFilter, unfold will use the horizintal layout.
    class BooleanFieldListFilter(SimpleListFilter):
        title = "Web Shop Product"
        parameter_name = "is_web_shop_product"

        def lookups(self, request, model_admin):
            return [
                ("yes", "Yes"),
                ("no", "No"),
            ]

        def queryset(self, request, queryset):
            if self.value() == "yes":
                return queryset.filter(
                    Exists(
                        NofenceCompany.objects.filter(
                            Q(shop_products=OuterRef("product")),
                        ),
                    )
                )
            elif self.value() == "no":
                return queryset.filter(
                    ~Exists(
                        NofenceCompany.objects.filter(
                            Q(shop_products=OuterRef("product")),
                        ),
                    )
                )
            return queryset

    return BooleanFieldListFilter


@admin.register(Inventory, site=unfold_admin_site)
class InventoryAdmin(InlineActionsMixin, SimpleHistoryAdmin, UnfoldModelAdmin):
    list_select_related = ["location__warehouse__nofence_company", "product"]

    search_fields = ["product__sku", "location__warehouse__name"]

    list_filter = (
        (
            "location__warehouse__is_active",
            get_is_active_filter_with_default("Warehouse is Active", "location__warehouse__in"),
        ),
        (
            "location__is_active",
            get_is_active_filter_with_default("Location is Active", "location__location_type__in"),
        ),
        "location__warehouse",
        "location__warehouse__nofence_company",
        "location__warehouse__inventory_mode",
        "location__location_type",
        AutocompleteFilterFactory("Product", "product"),
        get_is_shop_product_filter(),
        "product__compound_product",
        InventoryProductTypeFilter,
        InventoryProductStatusFilter,
        AutocompleteFilterFactory("Components of", "product__child_product_links__compound_product"),
        AutocompleteFilterFactory("Compound products containing", "product__compound_product_links__child_product"),
    )

    ordering = ["product__sku", "-location__warehouse__is_active", "location__location_type"]

    autocomplete_fields = ["product"]

    actions = [
        csv_export.streaming_csv_export_action(
            "Export inventory info (CSV)",
            skip_fields=["journal_link"],
        ),
        "adjust_components",
        "assemble_inventory",
        "generate_cost_table",
    ]

    actions_list = ["inventory_value_report"]

    actions_detail = [
        "assemble_inventory",
        "adjust_components",
    ]

    def get_form(self, request, obj=None, **kwargs):
        # We use the standard form when creating the inventory. The on exisitng inventory
        # we use the adjustment form
        if obj is None:
            super().get_form(request, obj=obj, **kwargs)
        return get_inventory_change_form(obj)

    def get_actions_detail(self, request, object_id=None):
        actions = []
        obj: Inventory = self.get_object(request, object_id)
        if obj is not None and obj.product.compound_product and obj.is_internally_managed():
            actions = ["assemble_inventory", "adjust_components"]

        return self._filter_unfold_actions_by_permissions(
            request,
            [self.get_unfold_action(action) for action in actions],
            object_id,
        )

    @unfold_action(description="Inventory value report", permissions=["view_inventory"])
    def inventory_value_report(self, request, queryset=None):
        return redirect(reverse("admin-inventory-value-export"))

    def has_view_inventory_permission(self, request, obj=None):
        return request.user.has_perm("core.view_inventory")

    def get_fieldsets(self, request, obj: Inventory = None):
        standard_fields = (
            None,
            {
                "fields": (
                    "location",
                    "product",
                    "available_quantity",
                    "shelf",
                    "stock_buffer",
                ),
            },
        )
        adjustment_fields = (
            _("Adjust Quantity"),
            {"fields": ("adjustment", "unit_cost", "adjustment_type", "adjustment_note")},
        )
        read_only_fields = (_("Adjust Quantity"), {"fields": ("read_only_note",)})

        fieldsets = [standard_fields]

        if obj is not None and not obj.warehouse.is_editable_in_admin():
            fieldsets.append(read_only_fields)
        elif obj is not None:
            fieldsets.append(adjustment_fields)

        if obj is not None and obj.product.compound_product:
            fieldsets.append(
                (
                    "Components",
                    {
                        "fields": ("component_inventory",),
                        "classes": ["collapse in"],
                    },
                ),
            )

        return fieldsets

    def get_readonly_fields(self, request, obj=None):
        if obj is not None:
            return [
                "available_quantity",
                "read_only_note",
                "component_inventory",
            ]
        return []

    def get_queryset(self, request):
        qs = super().get_queryset(request)

        user: User = request.user

        # Used to return an empty list so users that should not have access to inventory will not get access.
        # This is due to the group access. So if no warehouse is selected, the user has access to all inventory.
        if not user.has_perm("core.view_inventory"):
            return qs.none()

        if not user.is_superuser:
            # Limit access to inventory from warehouses defined on any of the user's auth groups
            # or on the user itself. If no warehouses are defined the user will have access to
            # all warehouses.

            warehouse_access = user.get_warehouse_access()

            if warehouse_access:
                qs = qs.filter(
                    location__warehouse__isnull=False,
                    location__warehouse__in=warehouse_access,
                )

        return qs

    def lookup_allowed(self, lookup: str, value: str) -> bool:
        if lookup == InventoryProductTypeFilter.parameter_name:
            # Seems to be a bug in django that nested relationship lookups using __in does not work,
            # so we just manually allow this lookup parameter
            return True
        return super().lookup_allowed(lookup, value)

    @admin.display(description="Read only warehouse")
    def read_only_note(self, obj=None):
        if obj is not None:
            return _("Inventory in warehouse {warehouse} is not editable in admin").format(
                warehouse=obj.warehouse,
            )

    @admin.display(description="Compound Product", boolean=True)
    def is_compound_product(self, obj: Inventory = None):
        if obj is not None:
            return obj.product.compound_product
        return "-"

    @admin.display(
        description="Location Type",
    )
    def location_type(self, obj):
        return obj.location.get_location_type_display()

    def get_list_display(self, request):
        list_display = [
            "product",
            "product_name",
            "product_item_number",
            "is_compound_product",
            "get_warehouse",
            "get_location",
            "available_quantity",
            "location_type",
        ]

        if request.user.has_perm("core.view_inventoryjournal"):
            list_display.append("journal_link")

        list_display.append("market")

        return list_display

    def product_name(self, obj):
        return obj.product.name

    def product_item_number(self, obj):
        return obj.product.item_number

    @admin.display(description="Warehouse", ordering="inventory__location__warehouse")
    def get_warehouse(self, inventory: Inventory):
        return inventory.location.warehouse

    @admin.display(description="Location", ordering="inventory__location")
    def get_location(self, inventory: Inventory):
        return inventory.location.name

    def journal_link(self, inventory: Inventory):
        url = reverse("admin:core_inventoryjournal_changelist")
        url = f"{url}?inventory__pk__exact={inventory.pk}"
        return format_html('<a href="{}">Journal</a>', url)

    journal_link.short_description = "Journal"

    def market(self, inventory: Inventory):
        if not inventory.location.warehouse.nofence_company:
            return None
        return inventory.location.warehouse.nofence_company.code

    market.short_description = "Market"

    def has_change_permission(self, request, obj: Inventory = None) -> bool:
        if obj is not None and obj.id:
            # We have to refetch the object from the database, because during
            # a save operation in the admin the obj may be an in-memory object
            # with a changed warehouse. E.g when a form is redisplayed after a
            # validation error.
            inventory = Inventory.objects.get(id=obj.id)
            if not inventory.warehouse.is_editable_in_admin():
                return False
        return super().has_change_permission(request, obj)

    @unfold_action(
        description="Adjust components",
        permissions=["adjust_inventory"],
    )
    def adjust_components(self, request, queryset=None, object_id=None):
        template = "admin/actions/adjust_inventory_components.html"

        if object_id:
            queryset = self.get_queryset(request).filter(id=object_id)

        inventory = list(queryset.all())
        if len(inventory) > 1:
            self.message_user(
                request,
                "Only one inventory item can be adjusted at a time",
                messages.ERROR,
            )
            return redirect(request.get_full_path())

        inventory: Inventory = inventory[0]

        if not inventory.is_internally_managed():
            self.message_user(
                request,
                f"Inventory is not managed by billy: {inventory.warehouse.get_inventory_mode_display()}",
                messages.ERROR,
            )
            return redirect(request.get_full_path())

        if not inventory.product.compound_product:
            self.message_user(
                request,
                f"Product {inventory.product} is not a compound product",
                messages.ERROR,
            )
            return redirect(request.get_full_path())

        components = inventory.get_child_inventory()

        child_products = [product for product, inventory in components]

        if "cancel" in request.POST:
            return redirect(self.get_redirect_url_for_inline_action(request, inventory.pk))
        elif "do_adjustment" in request.POST:
            form = get_adjust_inventory_components_form(child_products)(request.POST)
            if form.is_valid():
                inventory_service = create_inventory_service()
                data = form.cleaned_data
                adjusted_inventory = []
                if data["adjustment"] != 0:
                    with transaction.atomic():
                        for child_product, child_inventory in components:
                            if child_inventory is None:
                                child_inventory, created = Inventory.objects.get_or_create(
                                    product=child_product,
                                    location=inventory.location,
                                )
                                if created:
                                    logger.info(f"Created inventory for adjustment {child_inventory}")

                            unit_cost = data.get(f"unit_cost_{child_product.id}", None)
                            if unit_cost is None and data["adjustment"] > 0:
                                unit_cost = child_inventory.get_latest_historical_unit_price()
                                logger.info(
                                    f"Using historical unit cost {unit_cost} for {child_inventory} in "
                                    "component adjustment.",
                                )

                            inventory_service.adjust_inventory(
                                location=inventory.location,
                                sku=child_product.sku,
                                adjustment=data["adjustment"],
                                adjustment_type=data["adjustment_type"],
                                note=data.get("adjustment_note", ""),
                                unit_cost=unit_cost,
                            )
                            adjusted_inventory.append(inventory)
                    self.message_user(
                        request,
                        f"Inventory adjusted for {adjusted_inventory}",
                    )
                else:
                    self.message_user(request, "No adjustments made", messages.WARNING)

                return redirect(self.get_redirect_url_for_inline_action(request, inventory.pk))
        else:
            form = get_adjust_inventory_components_form(child_products)()

        opts = self.model._meta

        context = {
            **self.admin_site.each_context(request),
            "title": "Adjust components",
            "subtitle": f"{inventory}",
            "form": form,
            "opts": opts,
            "app_label": opts.app_label,
            "selected_objects": [inventory],
            "inventory": inventory,
            "components": components,
            **self.get_inline_action_context(request),
        }
        return render(request, template, context=context)

    def has_adjust_inventory_permission(self, request, obj=None):
        return request.user.has_perm("core.change_inventory")

    @unfold_action(
        description="Assemble inventory",
        permissions=["assemble_inventory"],
    )
    def assemble_inventory(self, request, queryset=None, object_id=None):
        template = "admin/actions/assemble_inventory.html"

        if object_id:
            queryset = self.get_queryset(request).filter(id=object_id)

        inventory = list(queryset.all())
        if len(inventory) > 1:
            self.message_user(
                request,
                "Only one inventory item can be adjusted at a time",
                messages.ERROR,
            )
            return redirect(request.get_full_path())

        inventory: Inventory = inventory[0]

        if not inventory.is_internally_managed():
            self.message_user(
                request,
                f"Inventory is not managed by billy: {inventory.warehouse.get_inventory_mode_display()}",
                messages.ERROR,
            )
            return redirect(request.get_full_path())

        if not inventory.product.compound_product:
            self.message_user(
                request,
                f"Product {inventory.product} is not a compound product",
                messages.ERROR,
            )
            return redirect(request.get_full_path())

        components = inventory.get_child_inventory()

        if "cancel" in request.POST:
            return redirect(self.get_redirect_url_for_inline_action(request, inventory.pk))
        elif "do_adjustment" in request.POST:
            form = AssembleInventoryForm(inventory, request.POST)
            if form.is_valid():
                inventory_service = create_inventory_service()
                data = form.cleaned_data
                if data["adjustment"] != 0:
                    try:
                        with transaction.atomic():
                            for child_product, child_inventory in components:
                                inventory_service.adjust_inventory(
                                    location=inventory.location,
                                    sku=child_product.sku,
                                    adjustment=-data["adjustment"],
                                    adjustment_type=InventoryAdjustmentType.ASSEMBLE,
                                    note=data.get("adjustment_note", ""),
                                )
                            inventory_service.adjust_inventory(
                                location=inventory.location,
                                sku=inventory.product.sku,
                                adjustment=data["adjustment"],
                                adjustment_type=InventoryAdjustmentType.ASSEMBLE,
                                note=data.get("adjustment_note", ""),
                            )
                        self.message_user(
                            request,
                            "Inventory adjusted",
                        )
                        return redirect(self.get_redirect_url_for_inline_action(request, inventory.pk))
                    except InventoryAdjustmentError as e:
                        form.add_error(None, str(e))
                else:
                    self.message_user(request, "No adjustments made", messages.WARNING)
                    return redirect(self.get_redirect_url_for_inline_action(request, inventory.pk))
        else:
            form = AssembleInventoryForm(inventory)

        opts = self.model._meta

        context = {
            **self.admin_site.each_context(request),
            "title": "Assemble inventory",
            "subtitle": f"{inventory}",
            "form": form,
            "opts": opts,
            "app_label": opts.app_label,
            "selected_objects": [inventory],
            "inventory": inventory,
            "components": components,
            **self.get_inline_action_context(request),
        }
        return render(request, template, context=context)

    def has_assemble_inventory_permission(self, request, obj=None):
        return request.user.has_perm("core.change_inventory")

    @unfold_action(
        description="Generate cost table",
        permissions=["generate_cost_table"],
    )
    def generate_cost_table(self, request, queryset):
        generate_inventory_cost_table.delay()
        self.message_user(request, "Started generating inventory cost table", messages.SUCCESS)
        return

    def has_generate_cost_table_permission(self, request, obj=None):
        return request.user.has_perm("core.change_inventory")

    @admin.display(description="Inventory")
    def component_inventory(self, instance: Inventory):
        """Function to display pretty version of the data"""
        # Convert the data to sorted, indented JSON
        html = ['<table style="width: 100%;">']
        html.append(
            """<tr>
            <th>Product</th>
            <th>Location</th>
            <th>Available Quantity</th>
        </tr>
        """,
        )
        for product, inventory in instance.get_child_inventory():
            html.append(
                format_html(
                    """
            <tr>
                <td>{}</td>
                <td>{}</td>
                <td>{}</td>
            </tr>
            """,
                    product,
                    inventory.location if inventory else "n/a",
                    inventory.available_quantity if inventory else "n/a",
                ),
            )
        html.append("</table>")

        return mark_safe("".join(html))


class WarehouseLocationInline(TabularInlineBase):
    model = WarehouseLocation

    fields = ("name", "location_type", "is_active")

    ordering = ["-is_active", "name"]


class WarehouseAddressInline(TabularInlineBase):
    model = WarehouseAddress


@admin.register(Warehouse, site=unfold_admin_site)
class WarehouseAdmin(DynamicListEditableMixin, ModelAdminBase, UnfoldModelAdmin):
    list_display = ("name", "is_active")
    list_filter = ("nofence_company", "is_active")
    search_fields = ["name"]
    # Use the inline edit on the auth group instead. I think it's more intuitive.
    exclude = ["group_access"]

    ordering = ["-is_active", "name"]

    dynamic_list_editable = ["is_active", "stock_buffer"]

    inlines = [WarehouseLocationInline, WarehouseAddressInline]


@admin.register(WarehouseLocation, site=unfold_admin_site)
class WarehouseLocationAdmin(DynamicListEditableMixin, ModelAdminBase, UnfoldModelAdmin):
    list_display = ("warehouse", "name", "location_type", "is_active")
    search_fields = ["name", "warehouse__name"]

    list_filter = (
        "warehouse",
        "location_type",
        ("is_active", get_is_active_filter_with_default("Location is Active", "location_type__in")),
        "warehouse__nofence_company",
    )

    ordering = ["-is_active", "-warehouse__is_active", "warehouse__name", "location_type"]

    dynamic_list_editable = ["is_active"]


@admin.register(PlannedInventoryAdjustment, site=unfold_admin_site)
class PlannedInventoryAdjustmentAdmin(ModelAdminBase, UnfoldModelAdmin):
    list_display = (
        "product",
        "warehouse",
        "adjustment_quantity",
        "planned_at",
        "completed_at",
        "adjustment_type",
    )
    search_fields = ["product__sku"]
    date_hierarchy = "planned_at"
    list_filter = (
        "warehouse",
        AutocompleteFilterFactory("Product", "product"),
        "adjustment_type",
        ("completed_at", EmptyFieldListFilter),
    )
    ordering = ["product__sku", "warehouse", "planned_at"]
    actions = ("mark_complete",)

    @admin.action(description="Mark as complete")
    def mark_complete(self, request, queryset):
        for adjustment in queryset.all():
            adjustment: PlannedInventoryAdjustment
            if not adjustment.completed_at:
                adjustment.completed_at = datetime.now()
                adjustment.save()

        self.message_user(
            request,
            f"Completed {queryset.count()} adjustment(s)",
            messages.SUCCESS,
        )


class InventoryFilter(AutocompleteFilter):
    title = "Inventory"
    field_name = "inventory"


@admin.register(InventoryJournal, site=unfold_admin_site)
class InventoryJournalAdmin(SimpleHistoryAdmin, UnfoldModelAdmin):
    djangoql_completion_enabled_by_default = False
    list_display = (
        "get_sku",
        "get_is_compound_product",
        "get_location",
        "quantity_delta",
        "adjustment_type",
        "get_object_link",
        "user",
        "created_at",
        "shipped_at",
        "sales_order_id",
        "sales_order_type",
        "warehouse",
        "product_type",
        "sales_order_line_total",
    )

    readonly_fields = [
        "inventory",
        "user",
        "adjustment_type",
        "shipment",
        "invoice_document",
        "purchase_receipt",
        "get_sales_order_link",
        "old_quantity",
        "new_quantity",
        "get_request_id_link",
        "created_at",
        "updated_at",
    ]

    list_select_related = [
        "inventory__location__warehouse",
        "user",
        "inventory__product",
        "shipment",
        "sales_order_line__order",
        "invoice_document",
        "purchase_receipt",
    ]

    ordering = ["-created_at"]

    autocomplete_fields = ["inventory"]

    search_fields = [
        "inventory__product__sku",
        "inventory__location__name",
        "inventory__location__warehouse__name",
    ]

    list_filter = (
        ("created_at", RangeDateFilter),
        "inventory__location__warehouse",
        ("inventory__location__warehouse__is_active", custom_titled_filter("Warehosue is Active")),
        "inventory__location__warehouse__nofence_company",
        "adjustment_type",
        AutocompleteFilterFactory("Location", "inventory__location"),
        AutocompleteFilterFactory("Product", "inventory__product"),
        InventoryFilter,
        InventoryJournalTypeProductTypeFilter,
        AutocompleteFilterFactory("Shipment", "shipment"),
        AutocompleteFilterFactory("Sales Order", "sales_order_line__order"),
        AutocompleteFilterFactory("Invoice Document", "invoice_document"),
        AutocompleteFilterFactory("Goods Receipt", "goods_receipt"),
        AutocompleteFilterFactory("Purchase Receipt", "purchase_receipt"),
        AutocompleteFilterFactory("Purchase Order", "purchase_receipt__purchase_order"),
    )

    fieldsets = ((None, {"fields": readonly_fields + ["unit_cost", "note"]}),)

    actions = [
        csv_export.streaming_csv_export_action(
            "Export journals (CSV)",
        ),
    ]

    def get_queryset(self, request):
        qs = super().get_queryset(request)

        user: User = request.user

        # Used to return an empty list so users that should not have access to inventory will not get access.
        # This is due to the group access. So if no warehouse is selected, the user has access to all inventory.
        if not user.has_perm("core.view_inventory"):
            return qs.none()

        if not user.is_superuser:
            warehouse_access = user.get_warehouse_access()
            if warehouse_access:
                qs = qs.filter(
                    inventory__location__warehouse__isnull=False,
                    inventory__location__warehouse__in=warehouse_access,
                )

        return qs

    def lookup_allowed(self, lookup: str, value: str) -> bool:
        if lookup == InventoryJournalTypeProductTypeFilter.parameter_name:
            # Seems to be a bug in django that nested relationship lookups using __in does not work,
            # so we just manually allow this lookup parameter
            return True
        return super().lookup_allowed(lookup, value)

    def has_add_permission(self, request, obj=None) -> bool:
        return request.user.is_superuser or settings.DEBUG

    def has_delete_permission(self, request, obj=None) -> bool:
        return request.user.is_superuser or settings.DEBUG

    @admin.display(
        description="SKU",
    )
    def get_sku(self, obj: InventoryJournal):
        return obj.inventory.product.sku

    @admin.display(
        description="Location",
    )
    def get_location(self, obj: InventoryJournal):
        return obj.inventory.location

    def sales_order_id(self, obj: InventoryJournal):
        return obj.sales_order_line.order.pk if obj.sales_order_line else None

    def sales_order_type(self, obj: InventoryJournal):
        return obj.sales_order_line.order.order_type if obj.sales_order_line else None

    def warehouse(self, obj: InventoryJournal):
        return obj.inventory.location.warehouse

    def product_type(self, obj: InventoryJournal):
        return obj.inventory.product.product_type

    def sales_order_line_total(self, obj: InventoryJournal):
        return obj.sales_order_line.discounted_sum() if obj.sales_order_line else None

    def shipped_at(self, obj: InventoryJournal):
        return obj.shipment.packing_completed_at if obj.shipment else None

    @admin.display(
        description="Compound Product",
        boolean=True,
    )
    def get_is_compound_product(self, obj: InventoryJournal):
        return obj.inventory.product.compound_product

    @admin.display(
        description="Request ID",
    )
    def get_request_id_link(self, obj: InventoryJournal):
        if obj.request_id:
            url = reverse("admin:core_inventoryjournal_changelist") + f"?request_id={obj.request_id}"
            return format_html("<a href='{}'>{}</a>", url, obj.request_id)
        return "-"

    @admin.display(
        description="Object",
    )
    def get_object_link(self, obj: InventoryJournal):
        if (
            obj.adjustment_type
            in [
                InventoryAdjustmentType.SHIPMENT,
                InventoryAdjustmentType.REPLACEMENT,
                InventoryAdjustmentType.GOODS_RECEIPT,
                InventoryAdjustmentType.DEVIATION,
                InventoryAdjustmentType.INTERNAL_TRANSFER,
                InventoryAdjustmentType.INTERNAL_SHIPMENT,
            ]
            and obj.shipment is not None
        ):
            url = reverse("admin:core_shipment_change", args=(obj.shipment.pk,))
            return format_html("<a href='{}'>{}</a>", url, obj.shipment.shipment_no)
        elif (
            obj.adjustment_type
            in [
                InventoryAdjustmentType.PURCHASE_RECEIPT,
            ]
            and obj.purchase_receipt is not None
        ):
            url = reverse("admin:core_purchasereceipt_change", args=(obj.purchase_receipt.pk,))
            return format_html("<a href='{}'>{}</a>", url, obj.purchase_receipt.receipt_reference)
        elif obj.adjustment_type in [
            InventoryAdjustmentType.RESERVATION,
            InventoryAdjustmentType.RESERVATION_REVERT,
        ]:
            if obj.sales_order_line is not None:
                order = obj.sales_order_line.order
                url = reverse("admin:core_salesorder_change", args=(order.pk,))
                return format_html(
                    "<a href='{}'>{}</a>",
                    url,
                    order.source_id or order.id,
                )
            elif obj.invoice_document is not None:
                inv = obj.invoice_document
                url = reverse("admin:core_invoicedocument_change", args=(inv.pk,))
                return format_html(
                    "<a href='{}'>{}</a>",
                    url,
                    inv.reference_number,
                )

    @admin.display(
        description="Sales order",
    )
    def get_sales_order_link(self, obj: InventoryJournal):
        if obj.sales_order_line is not None:
            order = obj.sales_order_line.order
            url = reverse("admin:core_salesorder_change", args=(order.pk,))
            return format_html("<a href='{}'>{}</a>", url, order)
        return "-"


class CompoundProductLinkInline(TabularInlineBase):
    model = CompoundProductLink
    fk_name = "compound_product"
    verbose_name = "Component"

    autocomplete_fields = ["child_product"]


class CompoundProductParentInline(ImmutableMixin, TabularInlineBase):
    model = CompoundProductLink
    fk_name = "child_product"
    verbose_name = "Part of compound product"
    autocomplete_fields = ["compound_product"]


class DisplayNameInline(TranslationTabularInline, TabularInlineBase):
    model = ProductDisplayName
    fk_name = "product"
    verbose_name = "Display name"


class ProductPriceInline(TabularInlineBase):
    model = ProductPrice
    fk_name = "product"
    form = ProductPriceForm


class ProductStatusFilter(SimpleListFilter):
    title = "Product status"
    parameter_name = "status"

    def lookups(self, request, model_admin):
        return [
            ("ACTIVE", "Active"),
            ("INACTIVE", "Inactive"),
        ]

    def choices(self, changelist: ChangeList):
        for lookup, title in self.lookup_choices:
            yield {
                "selected": self.value() == lookup,
                "query_string": changelist.get_query_string(
                    {
                        self.parameter_name: lookup,
                    },
                    [],
                ),
                "display": title,
            }

    def queryset(self, request, queryset):
        if self.value() is None:
            self.used_parameters[self.parameter_name] = Product.ProductStatus.ACTIVE
        return queryset.filter(status=self.value())


@admin.register(Product, site=unfold_admin_site)
class ProductAdmin(
    DynamicListEditableMixin,
    SimpleHistoryAdmin,
    TranslationUnfoldModelAdmin,
):
    formfield_override_by_name = {
        "shop_exclude_countries": {"widget": UnfoldSelect2MultipleAdminWidget},
    }

    search_fields = ["name", "sku", "item_number"]

    ordering = ["status", "name"]

    list_filter = (
        "category",
        "model",
        "compound_product",
        AutocompleteFilterFactory("Components of", "child_product_links__compound_product"),
        AutocompleteFilterFactory("Compound products containing", "compound_product_links__child_product"),
        "shop_markets",
        ProductStatusFilter,
        "collar_product_type",
        "tax_category",
        "shop_exclude_countries",
    )

    actions = [
        "view_inventory_forecast",
        csv_export.streaming_csv_export_action(
            "Export Product Info (CSV)",
            fields=[],
            extra_fields=[
                "sku",
                "item_number",
                "name",
                "description",
                "shop_description",
                "image",
                ("get_prices", "product_prices"),
                "tax_category",
                ("display_name__name_en", "display_name (en)"),
                ("display_name__name_nb", "display_name (nb)"),
                ("display_name__name_es", "display_name (es)"),
                ("get_shop_category", "shop_category"),
                "shop_description_en",
                "shop_description_nb",
                "shop_description_es",
                ("get_shop_exclude_countries", "shop_exclude_countries"),
                ("get_shop_markets", "shop_markets"),
                "hs_code",
                "origin_country",
                "status",
                "category",
                "product_type",
                "model",
                "collar_product_type",
                "is_inventory_product",
                "include_in_weee",
                "unit_weight",
                ("get_child_products", "child_products"),
                "is_subscription_product",
                "compound_product",
                "draft",
            ],
        ),
    ]

    filter_horizontal = ["shop_markets"]

    fieldsets = (
        (
            "Core Product Information",
            {
                "fields": (
                    "sku",
                    "item_number",
                    "hs_code",
                    "origin_country",
                    "category",
                    "product_type",
                    "model",
                    "collar_product_type",
                    "status",
                    "unit_weight",
                    "compound_product",
                    "include_in_weee",
                    "is_inventory_product",
                ),
                "classes": ["tab"],
            },
        ),
        (
            "Invoicing Product Information",
            {
                "fields": ("tax_category",),
                "classes": ["tab"],
            },
        ),
        (
            "Commercial Product Information",
            {
                "fields": (
                    "name",
                    "description",
                    "image",
                    "is_subscription_product",
                    "shop_category",
                    "shop_markets",
                    "shop_exclude_countries",
                    "shop_description",
                ),
                "classes": ["tab"],
            },
        ),
        (
            "Product Prices",
            {
                "fields": (),
                "classes": ["tab"],
            },
        ),
    )
    readonly_fields = ("shop_category",)

    dynamic_list_editable = ["name", "category", "tax_category", "model", "unit_weight"]

    list_select_related = ["display_name", "tax_category"]

    # Some values used for the product info export
    @admin.display()
    def get_prices(self, obj: Product):
        return ", ".join(sorted([f"{p.country}={p.price}" for p in obj.prices.all()]))

    @admin.display()
    def get_shop_exclude_countries(self, obj: Product):
        return ", ".join(sorted([f"{p}" for p in obj.shop_exclude_countries]))

    @admin.display()
    def get_shop_markets(self, obj: Product):
        return ", ".join(sorted([f"{p}" for p in obj.shop_markets.all()]))

    @admin.display()
    def get_child_products(self, obj: Product):
        return ", ".join(sorted([f"{p.sku}" for p in obj.child_products.all()]))

    @admin.display()
    def get_shop_category(self, obj: Product):
        category = getattr(obj, "shop_category", None)
        if category:
            parent = category.get_parent()
            if parent:
                category = parent
            return category.name

    def get_list_display(self, request):
        list_display = [
            "name",
            "sku",
            "get_display_name",
            "item_number",
            "category",
            "tax_category",
            "model",
            "compound_product",
            "image",
            "translated_and_prices",
        ]

        if request.GET.get("_enable_edit_inline", "0") == "yes" and request.user.is_superuser:
            # Change the order and add some extra fields when editing
            list_display = ["sku", "name", "category", "tax_category", "model", "unit_weight"]

        return list_display

    @admin.display(description="Display name")
    def get_display_name(self, obj: Product):
        return obj.product_display_name

    @admin.display(description="Translations and prices")
    def translated_and_prices(self, instance: Product):
        if (
            not hasattr(instance, "display_name")
            or not instance.display_name.name_en
            or not instance.display_name.name_es
            or not instance.display_name.name_nb
        ):
            return format_html(
                '<span style="background-color: #B70000; color: white; padding: 0 4px; border-radius: 4px;">No</span>',
            )
        if not instance.shop_description_en or not instance.shop_description_es or not instance.shop_description_nb:
            return format_html(
                '<span style="background-color: #B70000; color: white; padding: 0 4px; border-radius: 4px;">No</span>',
            )
        if ProductPrice.objects.filter(product=instance).count() != 5:
            return format_html(
                '<span style="background-color: #B70000; color: white; padding: 0 4px; border-radius: 4px;">No</span>',
            )
        return format_html(
            '<span style="background-color: #64A70B; color: white; padding: 0 4px; border-radius: 4px;">Yes</span>',
        )

    @admin.action(
        description="Inventory forecast",
        permissions=["view_forecast"],
    )
    def view_inventory_forecast(self, request, queryset):
        ids = [product.id for product in queryset.all()]
        url = reverse("inventory_forecast")
        params = urlencode([("p", id) for id in ids])
        return HttpResponseRedirect(f"{url}?{params}")

    def has_view_forecast_permission(self, request, obj=None):
        return request.user.has_perm("core.view_inventory")

    def get_search_results(self, request, queryset, search_term):
        if request.GET.get("model_name") == "collarreplacement":
            # The request is coming from the Return and Replacements page, filter on collar products only
            queryset = queryset.filter(category="COLLAR")

        return super(ProductAdmin, self).get_search_results(
            request,
            queryset,
            search_term,
        )

    def get_inlines(self, request, obj: Product = None):
        inlines = [
            DisplayNameInline,
            CompoundProductLinkInline,
            CompoundProductParentInline,
            ProductPriceInline,
        ]
        return inlines

    class Media:
        js = ("admin/js/product-inline-fieldset.js",)


class InvoiceDocumentOverdueFilter(SimpleListFilter):
    title = "Overdue"
    parameter_name = "overdue"

    def lookups(self, request, model_admin):
        return ("yes", "Yes"), ("14days", "14+ days"), ("no", "No")

    def queryset(self, request, queryset):
        if self.value() == "yes":
            return queryset.is_overdue()
        elif self.value() == "14days":
            return queryset.is_overdue().filter(
                due_date__lt=date.today() - timedelta(days=14),
            )
        elif self.value() == "no":
            return queryset.is_not_overdue()


class InvoiceDocumentInvoiceTypeFilter(SimpleListFilter):
    title = "Invoice type"
    parameter_name = "invoice_type"

    def lookups(self, request, model_admin):
        return (
            ("usage_invoice", "Usage invoice"),
            ("payment_request", "Payment request"),
            ("other", "Other"),
        )

    def queryset(self, request, queryset):
        if self.value() == "usage_invoice":
            return queryset.filter(invoice__isnull=False)
        elif self.value() == "payment_request":
            return queryset.filter(sales_order__isnull=False)
        elif self.value() == "other":
            return queryset.filter(
                invoice__isnull=True,
                sales_order__isnull=True,
            )


class InvoiceDocumentActiveCustomerFilter(SimpleListFilter):
    title = "Customer is active"
    parameter_name = "customer_active"

    def lookups(self, request, model_admin):
        return ("True", "Yes"), ("False", "No")

    def queryset(self, request, queryset):
        if self.value() == "True":
            return queryset.filter(customer__user__is_active=True)
        elif self.value() == "False":
            return queryset.filter(customer__user__is_active=False)


class InvoiceDocumentBalanceFilter(SimpleListFilter):
    title = "Balance"
    parameter_name = "balance"

    def lookups(self, request, model_admin):
        return ("positive", "Positive"), ("zero", "Zero")

    def queryset(self, request, queryset):
        if self.value() == "positive":
            return queryset.filter(amount_due__gt=0)
        if self.value() == "zero":
            return queryset.filter(amount_due=0)


class PaymentReminderInline(TabularInlineBase):
    model = PaymentReminder
    fields = ["reminder_date", "app_notification", "email_notification"]

    readonly_fields = ["app_notification", "email_notification"]

    def app_notification(self, obj):
        log = obj.notification
        if log is not None:
            url = reverse("admin:core_notificationlog_change", args=(log.pk,))
            return format_html(
                '<a href="{url}">{title}</a>',
                url=url,
                title=f"{log.status}",
            )

    def email_notification(self, obj):
        email_record = obj.email_record
        if email_record is not None:
            url = reverse("admin:core_emailrecord_change", args=(email_record.pk,))
            return format_html(
                '<a href="{url}">{title}</a>',
                url=url,
                title=f"{email_record.status}",
            )
        # CustomerEmailQueue is the old way of saving email records.
        # The code below can probably be removed in the future.
        email = obj.email
        if email is not None:
            url = reverse("admin:core_customeremailqueue_change", args=(email.pk,))
            return format_html(
                '<a href="{url}">{title}</a>',
                url=url,
                title=f"{email.status}",
            )


class TicketBAIInline(TabularInlineBase):
    model = TicketBAI
    fields = ("created_at", "ticket_bai")

    readonly_fields = ["created_at", "code", "ticket_bai"]

    def ticket_bai(self, obj):
        return format_html("<a href='{}'>{}</a>", obj.code, obj.code)


class InvoiceDocumentFileInline(TabularInlineBase):
    model = InvoiceDocumentFile
    fields = ("download_file", "type", "created_at")
    readonly_fields = [
        "download_file",
        "type",
        "created_at",
    ]

    @admin.display(description="File")
    def download_file(self, obj):
        url = obj.file.url
        name = obj.file.name
        return format_html(
            '<a target="_blank" href="{url}">{name}</a>',
            url=url,
            name=name,
        )


class InvoiceDocumentPaymentInline(ImmutableMixin, TabularInlineBase):
    model = InvoiceDocumentPayment
    fields = ("payment_id", "type", "status", "amount", "payment_date")
    show_change_link = True
    ordering = ["-payment_date"]


class InvoiceDocumentCardPaymentInline(ImmutableMixin, TabularInlineBase):
    model = CardPayment
    fields = ["amount", "status"]
    show_change_link = True


@admin.register(InvoiceDocument, site=unfold_admin_site)
class InvoiceDocumentAdmin(ImmutableMixin, SimpleHistoryAdmin, UnfoldModelAdmin):
    list_display = [
        "reference_number",
        "company_code",
        "type",
        "status",
        "date",
        "due_date_colored",
        "total",
        "balance",
        "customer_link",
        "download_xero_pdf",
        "download_latest_pdf",
    ]

    list_select_related = ["customer__user", "nofence_company", "sales_order"]

    readonly_fields = [
        "download_xero_pdf",
        "invoice_lines",
        "document_data",
        "status",
        "created_at",
        "updated_at",
        "nofence_company",
        "submitted_purchase_order_number",
        "connected_invoice_documents",
    ]

    search_fields = ["doc_id", "doc_ref"]

    ordering = ["-date", "-doc_id"]

    autocomplete_fields = ["customer", "sales_order", "invoice"]

    fieldsets = (
        (
            None,
            {
                "fields": [
                    "doc_id",
                    "doc_ref",
                    "provider",
                    "nofence_company",
                    "status",
                    "type",
                    "total",
                    "balance",
                    "date",
                    "due_date",
                    "customer",
                    "email_sent_to_customer",
                    "sales_order",
                    "invoice",
                    "invoice_lines",
                    "created_at",
                    "updated_at",
                    "download_xero_pdf",
                    "use_direct_debit",
                    "submitted_purchase_order_number",
                    "connected_invoice_documents",
                ],
            },
        ),
        (
            "Document Status",
            {
                "fields": (
                    "internal_status",
                    "external_status",
                ),
            },
        ),
        (
            _("Document Data"),
            {
                "fields": ("document_data",),
                "classes": ["collapse in"],
            },
        ),
    )

    list_filter = (
        CustomerFilter,
        "type",
        "status",
        "nofence_company",
        "provider",
        InvoiceDocumentOverdueFilter,
        InvoiceDocumentInvoiceTypeFilter,
        InvoiceDocumentActiveCustomerFilter,
        ("due_date", RangeDateFilter),
        "customer__is_nofence_company_shipment_receiver",
        "internal_status",
        "external_status",
    )

    exclude = ["body", "group_access"]

    history_unified_diff = ["body"]

    inlines = [
        InvoiceDocumentFileInline,
        PaymentReminderInline,
        TicketBAIInline,
        InvoiceDocumentPaymentInline,
        InvoiceDocumentCardPaymentInline,
    ]

    actions = [
        csv_export.streaming_csv_export_action(
            "Export Data (CSV)",
            fields=[
                "reference_number",
                "provider",
                "type",
                "status",
                "date",
                "due_date",
                "total",
                "balance",
                "customer",
                "invoice_type",
            ],
            extra_fields=[
                ("customer__visma_id", "customer visma_id"),
                ("customer__visma_prepaid_id", "customer visma_prepaid_id"),
                ("customer__xero_id", "customer xero id"),
                ("customer__hubspot_id", "customer hubspot id"),
                ("customer__business_reg_no", "customer business registration number"),
                ("customer__customer_status", "customer status"),
                ("customer__customer_type", "customer type"),
                ("customer__name", "customer name"),
                ("customer__user__name", "customer contact name"),
                ("customer__email", "customer email"),
                ("customer__phone_number", "customer phone_number"),
                ("customer__phone_number_mobile", "customer mobile"),
                ("customer__company_address__address1", "customer address1"),
                ("customer__company_address__address2", "customer address2"),
                ("customer__company_address__zip_code", "customer zip_code"),
                ("customer__company_address__city", "customer city"),
                ("customer__company_address__region", "customer region"),
                ("customer__company_address__country", "customer country"),
            ],
        ),
        "send_reminder",
        "revert_inventory_reservation",
        "trigger_invoice_document_changed",
        "trigger_invoice_document_changed_created",
    ]

    def get_queryset(self, request):
        qs = super().get_queryset(request)

        # Used to return an empty list so users that should not have access to invoices will not get access.
        # This is due to the group access. So if no company is selected, the user has access to all invoices.
        if not request.user.has_perm("core.view_invoicedocument"):
            return qs.none()

        if not request.user.is_superuser:
            # Limit access to invoice_documents from companies defined on any of the user's
            # auth groups or on the user itself. If no companies are defined the user will
            # have access to all invoice_documents.
            groups = request.user.groups.prefetch_related("company_access").all()

            company_access = [company for group in groups for company in group.company_access.all()]

            company_access += request.user.company_access.all()

            if company_access:
                qs = qs.filter(
                    nofence_company__isnull=False,
                    nofence_company__in=company_access,
                )

        return qs

    def has_change_permission(self, request, obj=None):
        return request.user.is_superuser or settings.DEBUG

    def has_add_permission(self, request, obj=None):
        return request.user.is_superuser or settings.DEBUG

    def has_delete_permission(self, request, obj=None):
        return request.user.is_superuser or settings.DEBUG

    def document_data(self, instance):
        """Function to display pretty version of the data"""
        # Convert the data to sorted, indented JSON
        return format_html("<pre>{}</pre>", pprint.pformat(instance.body))

    @admin.display(description="Due date")
    def due_date_colored(self, instance: InvoiceDocument):
        if instance.due_date is not None:
            if instance.is_overdue_by(days=14):
                return format_html(
                    '<span style="color: #ff0000;">{}</span>',
                    date_format(instance.due_date),
                )
            elif instance.is_overdue():
                return format_html(
                    '<span style="color: #e58500">{}</span>',
                    date_format(instance.due_date),
                )
        return instance.due_date

    def invoice_lines(self, instance: InvoiceDocument):
        """Function to display pretty version of the data"""
        # Convert the data to sorted, indented JSON
        html = ['<table style="width: 100%;">']
        html.append(
            """<tr>
            <th>Item</th>
            <th>Quantity</th>
            <th>Price</th>
            <th>Discount</th>
            <th>Total</th>
        </tr>
        """,
        )
        for line in instance.data.lines:
            html.append(
                format_html(
                    """
            <tr>
                <td>{}</td>
                <td>{}</td>
                <td>{}</td>
                <td>{}%</td>
                <td>{}</td>
            </tr>
            """,
                    line.item or line.description,
                    line.quantity,
                    line.price,
                    line.discount,
                    line.total,
                ),
            )
        html.append("</table>")

        return mark_safe("".join(html))

    def connected_invoice_documents(self, instance: InvoiceDocument):
        """Function to display pretty version of the data"""
        # Convert the data to sorted, indented JSON
        html = ['<table style="width: 100%;">']
        html.append(
            """<tr>
            <th>Type</th>
            <th>Reference number</th>
            <th>Status</th>
            <th>Total</th>
            <th>Created at</th>
        </tr>
        """,
        )
        if instance.credited_from:
            html.append(
                format_html(
                    """
                    <tr>
                        <td>{}</td>
                        <td><a href={}>{}</a></td>
                        <td>{}</td>
                        <td>{}</td>
                        <td>{}</td>
                    </tr>
                    """,
                    "Invoice",
                    reverse(
                        "admin:core_invoicedocument_change",
                        args=(instance.credited_from.pk,),
                    ),
                    instance.credited_from.reference_number,
                    instance.credited_from.status,
                    instance.credited_from.total,
                    instance.credited_from.created_at.strftime("%d/%m/%y"),
                ),
            )
        elif instance.has_credit_notes:
            for credit_note in instance.credit_notes.all():
                html.append(
                    format_html(
                        """
                        <tr>
                            <td>{}</td>
                            <td><a href={}>{}</a></td>
                            <td>{}</td>
                            <td>{}</td>
                            <td>{}</td>
                        </tr>
                        """,
                        "Credit note",
                        reverse(
                            "admin:core_invoicedocument_change",
                            args=(credit_note.pk,),
                        ),
                        credit_note.reference_number,
                        credit_note.status,
                        credit_note.total,
                        credit_note.created_at.strftime("%d/%m/%y"),
                    ),
                )
        html.append("</table>")
        return mark_safe("".join(html))

    @admin.display(description="Visma/Xero")
    def download_xero_pdf(self, obj):
        if obj.id is not None:
            url = reverse("download_document_pdf", kwargs={"id": obj.id})
            return format_html('<a href="{url}">PDF</a>', url=url)
        return "-"

    @admin.display(description="Latest")
    def download_latest_pdf(self, obj):
        if obj.id is not None and obj.files.first() is not None:
            url = obj.files.order_by("-created_at").first().file.url
            return format_html('<a target="_blank" href="{url}">PDF</a>', url=url)
        return "-"

    @admin.display(
        ordering="customer",
        description="Customer",
    )
    def customer_link(self, obj):
        if obj.customer is not None:
            url = reverse("admin:core_customer_change", args=(obj.customer.pk,))
            return format_html("<a href='{}'>{}</a>", url, obj.customer)

    @admin.display(
        ordering="nofence_company",
        description="Company",
    )
    def company_code(self, obj):
        if obj.nofence_company is not None:
            return obj.nofence_company.code

    @admin.action(description="Send reminder")
    def send_reminder(self, request, queryset):
        service = payment_reminders.PaymentReminderService()
        total_count = queryset.count()
        sent_count = 0
        for inv in queryset.all():
            sent, msg = service.send_reminder(inv)
            logger.info(f"Invoice reminder from admin: {sent} - {msg}")
            if total_count <= 10:
                self.message_user(
                    request,
                    msg,
                    messages.SUCCESS if sent else messages.WARNING,
                )
            elif sent:
                sent_count += 1

        if total_count > 10:
            if sent_count != total_count:
                self.message_user(
                    request,
                    f"Sent {sent_count} reminders from {total_count} invoices. "
                    "Possible reasons for some reminders not getting sent are invoice not overdue, "
                    "reminder already sent.",
                    messages.WARNING,
                )
            else:
                self.message_user(
                    request,
                    f"Sent {sent_count} reminders from {total_count} invoices.",
                    messages.SUCCESS,
                )

    @admin.action(
        description="Revert inventory reservation",
        permissions=["adjust_inventory"],
    )
    def revert_inventory_reservation(self, request, queryset):
        if queryset.count() > 1:
            self.message_user(request, "Please select only one invoice", messages.ERROR)
            return

        inv: InvoiceDocument = queryset.first()

        shipment: Shipment = getattr(inv, "shipment", None)

        if not shipment:
            self.message_user(request, "Found no shipment linked to invoice", messages.ERROR)
            return

        if not shipment.is_intercompany_shipment():
            self.message_user(
                request,
                "Linked shipment does not look like an internal nofence company shipment",
                messages.ERROR,
            )
            return

        if not shipment.from_warehouse:
            self.message_user(request, "Shipment is missing from warehouse", messages.ERROR)
            return

        try:
            create_inventory_service().revert_inventory_reservation_for_invoice_document(
                shipment.from_warehouse,
                inv,
            )
            self.message_user(request, "Reverted inventory reservation", messages.SUCCESS)
        except Exception as e:
            logger.exception(f"Failed to revert inventory reservation for invoice document {inv=} and {shipment=}")
            self.message_user(request, f"Failed to revert inventory reservations: {e}", messages.ERROR)

    def has_adjust_inventory_permission(self, request, obj=None):
        return request.user.has_perm("core.change_inventory")

    @admin.action(
        description="Trigger invoice document changed",
        permissions=["trigger_changed_signal"],
    )
    def trigger_invoice_document_changed(self, request, queryset):
        queryset = queryset.select_related("customer", "nofence_company")

        count = 0
        for inv in queryset:
            logger.info(f"Triggering billy invoice document changed (created=False) for {inv} from admin")
            signals.billy_invoicedocument_changed.send(
                sender=self.__class__,
                invoicedocument_id=inv.id,
                created=False,
            )
            count += 1

        self.message_user(request, f"Sent {count} signals", messages.SUCCESS)

    def has_trigger_changed_signal_permission(self, request, obj=None):
        return request.user.has_perm("core.change_invoicedocument")

    @admin.action(
        description="Trigger invoice document changed (created=True)",
        permissions=["trigger_changed_signal"],
    )
    def trigger_invoice_document_changed_created(self, request, queryset):
        queryset = queryset.select_related("customer", "nofence_company")

        count = 0
        for inv in queryset:
            logger.info(f"Triggering billy invoice document changed (created=True) for {inv} from admin")
            signals.billy_invoicedocument_changed.send(
                sender=self.__class__,
                invoicedocument_id=inv.id,
                created=True,
            )
            count += 1

        self.message_user(request, f"Sent {count} signals (created=True)", messages.SUCCESS)


@admin.register(PaymentReminder, site=unfold_admin_site)
class PaymentReminderAdmin(ImmutableMixinWithDelete, ModelAdminBase, UnfoldModelAdmin):
    change_list_template = "admin/payment_reminder_changelist.html"

    list_display = [
        "invoice",
        "invoice_status",
        "customer",
        "app_notification",
        "email_notification",
        "reminder_date",
    ]

    exclude = ["notification", "email"]

    readonly_fields = ["app_notification", "email_notification"]

    list_select_related = ["invoice__customer__user", "notification", "email"]

    autocomplete_fields = ["invoice"]

    search_fields = ["invoice__doc_id", "invoice__doc_ref"]

    ordering = ["-reminder_date"]

    list_filter = [
        AutocompleteFilterFactory("Customer", "invoice__customer"),
        "invoice__status",
        ("reminder_date", RangeDateFilter),
    ]

    def customer(self, obj):
        return obj.invoice.customer

    def app_notification(self, obj):
        log = obj.notification
        if log is not None:
            url = reverse("admin:core_notificationlog_change", args=(log.pk,))
            return format_html(
                '<a href="{url}">{title}</a>',
                url=url,
                title=f"{log.status}",
            )

    def email_notification(self, obj):
        email_record = obj.email_record
        if email_record is not None:
            url = reverse("admin:core_emailrecord_change", args=(email_record.pk,))
            return format_html(
                '<a href="{url}">{title}</a>',
                url=url,
                title=f"{email_record.status}",
            )
        # CustomerEmailQueue is the old way of saving email records.
        # The code below can probably be removed in the future.
        email = obj.email
        if email is not None:
            url = reverse("admin:core_customeremailqueue_change", args=(email.pk,))
            return format_html(
                '<a href="{url}">{title}</a>',
                url=url,
                title=f"{email.status}",
            )

    def invoice_status(self, obj):
        return obj.invoice.status


@admin.register(CustomerEmailQueue, site=unfold_admin_site)
class CustomerEmailQueueAdmin(ImmutableMixinWithDelete, ModelAdminBase, UnfoldModelAdmin):
    list_display = ("type", "status", "customer")

    readonly_fields = ["created_at", "updated_at"]
    search_fields = ["customer__visma_id", "customer__xero_id", "customer__name", "customer__email"]
    list_filter = ("type", "status")

    autocomplete_fields = ["customer"]


@admin.register(PurchaseBlockExclude, site=unfold_admin_site)
class PurchaseBlockExcludeAdmin(ModelAdminBase, UnfoldModelAdmin):
    list_display = (
        "object_type",
        "object_id",
        "short_reason",
        "added_by",
        "enabled",
        "created_at",
    )

    list_filter = ("object_type", AutocompleteFilterFactory("User", "added_by"))

    autocomplete_fields = ["added_by"]

    search_fields = ["object_id"]

    def get_fieldsets(self, request, obj=None):
        if obj is not None:
            return (
                (
                    None,
                    {
                        "fields": [
                            "object_type",
                            "object_id",
                            "reason",
                            "added_by",
                            "hit_count",
                            "created_at",
                            "updated_at",
                        ],
                    },
                ),
                (
                    None,
                    {
                        "fields": [
                            "enabled",
                        ],
                    },
                ),
            )
        return (
            (
                None,
                {
                    "fields": [
                        "object_type",
                        "object_id",
                        "reason",
                    ],
                    "description": "<ul>"
                    "<li>A hubspot deal can be blocked from getting moved into pending delivery if a customer has "
                    "unpaid invoices overdue by more than 4 weeks.</li> "
                    "<li>In special cases this block can be lifted by "
                    "adding a purchase block exclude rule using this form.</li>"
                    "<li><br></li>"
                    "<li><strong>Hubspot deal</strong>: By adding a exclude rule for a hubspot deal, the deal "
                    "will be pass through the block no matter how many unpaid invoices the customer has.</li>"
                    "<li><strong>Invoice</strong>: By excluding an invoice, the specific invoice will not be "
                    "considered by the purchase block. If a customer has other unpaid invoices a deal can "
                    "still be blocked.</li>"
                    "<li><br><hr></li>"
                    "</ul>",
                },
            ),
        )

        return super().get_fieldsets(request, obj)

    def get_readonly_fields(self, request, obj=None):
        if obj is not None:
            return [
                "object_type",
                "object_id",
                "reason",
                "added_by",
                "hit_count",
                "created_at",
                "updated_at",
                "hit_count",
            ]
        return []

    @admin.display(description="Reason")
    def short_reason(self, obj):
        return truncatechars(obj.reason, 35)

    def has_delete_permission(self, request, obj=None) -> bool:
        # Should not be able to delete, so that we can keep track of how
        # often the excludes are used
        return False


class ActiveAppUserFilter(SimpleListFilter):
    title = "Is Active"
    parameter_name = "is_active"

    def lookups(self, request, model_admin):
        return [("yes", "Yes"), ("no", "No")]

    def queryset(self, request, queryset):
        if self.value() == "yes":
            return queryset.filter(user__is_active=True).exclude(user__password__startswith="!")
        elif self.value() == "no":
            return queryset.filter(Q(user__is_active=False) | Q(user__password__startswith="!"))


@admin.register(AppUser, site=unfold_admin_site)
class AppUserAdmin(ModelAdminBase, UnfoldModelAdmin):
    list_display = ("user", "name", "is_active", "created_at")

    ordering = ("-created_at",)

    readonly_fields = ["user", "activation_link", "is_active", "name"]

    list_filter = [ActiveAppUserFilter]

    autocomplete_fields = ["user"]

    search_fields = ["user__name", "user__email"]

    fieldsets = (
        (
            None,
            {
                "fields": (
                    "user",
                    "name",
                    "is_active",
                    "activation_link",
                ),
            },
        ),
    )

    custom_add_form = CreateAppUserForm
    fieldsets_add_form = (
        (
            None,
            {
                "fields": CreateAppUserForm.Meta.fields,
            },
        ),
    )

    @admin.display(description="Is Active", boolean=True)
    def is_active(self, obj: AppUser):
        return obj.user.is_active_with_password()

    @admin.display(description="Name")
    def name(self, obj: AppUser):
        if obj.user.has_usable_password():
            return obj.user.name
        return "<Invite Pending>"

    def activation_link(self, obj: AppUser):
        if obj.user.is_active and not obj.user.has_usable_password():
            # We should probably our own thread local context to access the request in cases like this
            context = HistoricalRecords.context
            if context.request:
                return obj.create_invite_accept_url(context.request)
        return "-"

    def get_form(self, request, obj=None, change=False, **kwargs):
        """
        We need a special form when creating a new user from the admin,
        because that the associated user model must also be created.
        """
        defaults = {}
        if not change:
            defaults["form"] = self.custom_add_form
        defaults.update(kwargs)
        form = super().get_form(request, obj, change, **defaults)

        return form

    def get_fieldsets(self, request, obj=None):
        if obj is None:
            return self.fieldsets_add_form
        return super().get_fieldsets(request, obj=obj)

    def get_readonly_fields(self, request, obj=None):
        if obj is None:
            # Hide readonly fields when creating a new user
            return []
        return super().get_readonly_fields(request, obj=obj)

    def get_inlines(self, request, obj=None):
        if obj is None:
            # Hide inlines when creating a new user
            return []
        return super().get_inlines(request, obj=obj)

    def response_add(self, request, obj, post_url_continue=None):
        # Taken from django.contrib.auth.admin.UserAdmin
        if "_addanother" not in request.POST and IS_POPUP_VAR not in request.POST:
            request.POST = request.POST.copy()
            request.POST["_continue"] = 1
        return super().response_add(request, obj, post_url_continue=post_url_continue)


@admin.register(PushToken, site=unfold_admin_site)
class PushTokenAdmin(ModelAdminBase, UnfoldModelAdmin):
    list_display = (
        "user_account_access",
        "unique_id",
        "created_at",
        "updated_at",
        "os",
        "lang",
    )

    autocomplete_fields = ["user_account_access"]

    readonly_fields = ("created_at", "updated_at")
    search_fields = [
        "unique_id",
        "user_account_access__user__email",
        "user_account_access__user__name",
        "user_account_access__customer__name",
        "user_account_access__customer__delivery_contact_email",
        "user_account_access__customer__invoice_contact_email",
    ]
    list_filter = [
        ("created_at", RangeDateFilter),
        ("updated_at", RangeDateFilter),
        "user_account_access__type",
        "user_account_access__is_accepted",
    ]

    actions = ["send_test_notification_fcm_v1"]

    @admin.action(
        description="Send Test Notification FCM V1",
        permissions=["send_app_notifications"],
    )
    def send_test_notification_fcm_v1(self, request, queryset):
        results = []
        for push_token in queryset:
            data = {
                "type": PushNotificationType.NOTIFICATIONMESSAGE,
                "path": "notifications/messages",
                "originalMsgId": "654b322d1793e6f04d6d87b9",
            }
            notification = PushNotification.objects.create(data, push_token.user_account_access.customer.email)
            send_results = PushNotificationHelper.send_fcm(notification, True)
            results.append(send_results)
        self.message_user(request, f"Results: {results}", messages.INFO)

    def has_send_app_notifications_permission(self, request, obj=None):
        return request.user.has_perm("core.add_notificationmessage")


@admin.register(PushNotification, site=unfold_admin_site)
class PushNotificationAdmin(ModelAdminBase, UnfoldModelAdmin):
    list_display = (
        "original_msg_id",
        "type",
        "customer_link",
        "status",
        "collar",
        "created_at",
    )

    readonly_fields = ("created_at", "updated_at")
    autocomplete_fields = ["collar", "customer"]
    search_fields = [
        "type",
        "original_msg_id",
        "customer__name",
        "customer__invoice_contact_email",
        "customer__delivery_contact_email",
        "collar__serial_no",
    ]
    list_filter = [
        CustomerFilter,
        CollarFilter,
        "type",
        ("created_at", RangeDateFilter),
        ("updated_at", RangeDateFilter),
    ]
    actions = ["send_notification", "send_notification_fcm_v1"]
    list_select_related = [
        "customer",
        "customer__nofence_company",
        "collar",
    ]
    show_full_result_count = False
    paginator = LargeTablePaginatorPG

    @admin.action(
        description="Send Notification",
        permissions=["send_app_notifications"],
    )
    def send_notification(self, request, queryset):
        results = []
        for notification in queryset:
            send_results = PushNotificationHelper.send(notification, True)
            results.append(send_results)
        self.message_user(request, f"Results: {results}", messages.INFO)

    @admin.action(
        description="Send Notification FCM V1",
        permissions=["send_app_notifications"],
    )
    def send_notification_fcm_v1(self, request, queryset):
        results = []
        for notification in queryset:
            send_results = PushNotificationHelper.send_fcm(notification, True)
            results.append(send_results)
        self.message_user(request, f"Results: {results}", messages.INFO)

    def has_send_app_notifications_permission(self, request, obj=None):
        return request.user.has_perm("core.add_notificationmessage")

    def customer_link(self, obj):
        if obj.customer is not None:
            url = reverse("admin:core_customer_change", args=(obj.customer.pk,))
            return format_html("<a href='{}'>{}</a>", url, obj.customer)
        return "-"


@admin.register(PushNotificationSettings, site=unfold_admin_site)
class PushNotificationSettingsAdmin(ModelAdminBase, UnfoldModelAdmin):
    list_display = ("user", "created_at", "updated_at")

    readonly_fields = ("created_at", "updated_at")
    search_fields = ["user__email, user__name"]
    list_filter = [
        AutocompleteFilterFactory("User", "user"),
    ]


@admin.register(JiraReturnIssue, site=unfold_admin_site)
class JiraReturnIssueAdmin(ModelAdminBase, UnfoldModelAdmin):
    list_display = (
        "created_at",
        "key",
        "status",
        "serial_no",
        "collar_model",
        "issue_updated_at",
        "jira_link",
        "collar_link",
    )

    readonly_fields = (
        "created_at",
        "updated_at",
        "issue_updated_at",
        "issue_created_at",
        "jira_link",
        "support_ticket",
    )

    list_filter = [
        ("created_at", RangeDateFilter),
        "status",
        "collar__model",
        "country",
        "fault_description",
    ]

    exclude = ("product",)

    ordering = ["-issue_updated_at"]

    autocomplete_fields = ["collar"]

    search_fields = ["serial_no", "key"]

    @admin.display(description="Collar Model")
    def collar_model(self, obj):
        if obj.collar is not None:
            return obj.collar.model
        return "n/a"

    def jira_link(self, obj):
        return format_html(
            '<a href="{url}">{title}</a>',
            url=f"{settings.JIRA_BASE_URL}/browse/{obj.key}",
            title="JIRA",
        )

    def collar_link(self, obj):
        if obj.collar is not None:
            url = reverse("admin:core_collar_change", args=(obj.collar.pk,))
            return format_html("<a href='{}'>{}</a>", url, obj.collar)


class NofenceCompanyEmailConfigInline(TabularInlineBase):
    model = EmailConfig
    ordering = ["email_type"]

    def get_queryset(self, request):
        return super().get_queryset(request).select_related("nofence_company")


class PostPaymentLimitInline(TabularInlineBase):
    model = PostPaymentLimit

    readonly_fields = ["current_outstanding_amount"]

    @admin.display(description="Current outstanding post paid amount")
    def current_outstanding_amount(self, obj: PostPaymentLimit = None):
        if obj and obj.id:
            currently_unpaid = sum(
                o.sum_total()
                for o in SalesOrder.objects.filter(
                    nofence_company=obj.nofence_company,
                    payment_status=SalesOrder.PaymentStatus.PENDING_PAYMENT,
                    post_payment=True,
                    currency=obj.max_unpaid_amount.currency,
                ).prefetch_related("lines")
            )

            currently_unpaid = currently_unpaid or Money("0", obj.max_unpaid_amount.currency)
            percentage = ""
            if obj.max_unpaid_amount:
                percentage = f" ({round((currently_unpaid / obj.max_unpaid_amount) * 100, 1)}%)"

            return f"{currently_unpaid}{percentage}"
        return "-"


class DefaultReplacementProductInline(TabularInlineBase):
    model = DefaultReplacementProduct

    autocomplete_fields = ["product"]


class TaxDefinitionInline(TabularInlineBase):
    model = TaxDefinition

    autocomplete_fields = ["nofence_company", "tax_category", "tax_code"]


class FinancingProviderInline(TabularInlineBase):
    model = FinancingPartner
    show_change_link = True


@admin.register(NofenceCompany, site=unfold_admin_site)
class NofenceCompanyAdmin(AdminAdvancedFiltersMixin, ModelAdminBase, UnfoldModelAdmin):
    formfield_override_by_name = {
        "countries": {"widget": UnfoldSelect2MultipleAdminWidget},
        "referrals_enabled_for_countries": {"widget": UnfoldSelect2MultipleAdminWidget},
        "customer_statuses_referral_enabled": {"widget": ArrayWidget(choices=CustomerStatus.choices)},
    }

    list_display = ("name", "accounting_system")

    readonly_fields = ("created_at", "updated_at", "code", "accounting_system")

    autocomplete_fields = [
        "hubspot_sales_pipeline",
        "hubspot_sales_pipeline_stage",
        "hubspot_support_pipeline",
        "hubspot_support_pipeline_stage",
    ]

    list_filter = [
        "accounting_system",
        "countries",
    ]

    advanced_filter_fields = ["accounting_system", "countries"]

    fieldsets = (
        (
            None,
            {
                "fields": [
                    "name",
                    "created_at",
                    "updated_at",
                    "code",
                    "countries",
                    "accounting_system",
                    "pre_paid",
                    "sales_order_invoice_due_date_days",
                    "first_payment_reminder_days",
                    "second_payment_reminder_days",
                    "is_part_of_debt_collection_flow",
                    "store_active",
                    "tax_number",
                    "one_stop_shop_number",
                    "domestic_shipment_warehouse",
                    "international_shipment_warehouse",
                    "sales_contact_us_email",
                    "sales_contact_us_phone",
                    "support_contact_us_phone",
                    "sales_page_url",
                    "help_center_url",
                    "feedback_url",
                    "collar_not_reporting_url",
                    "beacon_info_url",
                    "cattle_forum_url",
                    "sheep_and_goat_forum_url",
                    "international_sales_contact_us_email",
                    "international_sales_contact_us_phone",
                    "international_support_contact_us_phone",
                    "international_sales_page_url",
                    "international_help_center_url",
                    "international_feedback_url",
                    "international_collar_not_reporting_url",
                    "international_beacon_info_url",
                    "international_cattle_forum_url",
                    "international_sheep_and_goat_forum_url",
                    "hubspot_sales_pipeline",
                    "hubspot_sales_pipeline_stage",
                    "hubspot_support_pipeline",
                    "hubspot_support_pipeline_stage",
                    "freight_method_order_with_collars",
                    "delayed_delivery_popup",
                ],
            },
        ),
        (
            "Campaigns",
            {
                "fields": [
                    "referrals_enabled",
                    "referrals_enabled_for_countries",
                    "referee_discount",
                    "referee_discount_expires_after_days",
                    "referrer_discount",
                    "referrer_discount_expires_after_days",
                ],
            },
        ),
        (
            "Referral Status Settings",
            {
                "fields": ["customer_statuses_referral_enabled"],
                "classes": ["collapse"],
                "description": "Configure which customer statuses are allowed to participate in the referral program.",
            },
        ),
    )

    ordering = ["name"]

    search_fields = ["name"]

    inlines = [
        NofenceCompanyAddressInline,
        NofenceCompanyEmailConfigInline,
        PostPaymentLimitInline,
        DefaultReplacementProductInline,
        TaxDefinitionInline,
        FinancingProviderInline,
    ]

    def has_delete_permission(self, *args, **kwargs):
        return False

    def has_add_permission(self, *args, **kwargs):
        return True


class GroupWarehouseAccessInline(TabularInlineBase):
    model = Warehouse.group_access.through
    extra = 0
    verbose_name_plural = _("Warehouse access for shipments")


class GroupCompanyAccessInline(TabularInlineBase):
    model = NofenceCompany.group_access.through
    extra = 0


class GroupsAdmin(GroupAdmin, UnfoldModelAdmin):
    """Override the auth group admin view.

    Override to include an inline warehouse list, used to limit
    access to shipments for group members
    """

    inlines = [
        GroupWarehouseAccessInline,
        GroupCompanyAccessInline,
    ]


admin.site.unregister(Group)
unfold_admin_site.register(Group, GroupsAdmin)
unfold_admin_site.register(AdvancedFilter, AdvancedFilterAdmin)


class SupportTicketPipelineStageFilter(SimpleListFilter):
    title = "Pipeline stage"
    parameter_name = "Pipeline stage"

    def lookups(self, request, model_admin):
        qs = HubSpotPipelineStageModel.objects.filter(pipeline__object_type="ticket")
        return sorted(list(set([(s.label, s.label) for s in qs.all()])))

    def queryset(self, request, queryset):
        if self.value():
            return queryset.filter(pipeline_stage__label=self.value())


class SupportTicketWithReturnIssueFilter(SimpleListFilter):
    title = "Has Return Issues"
    parameter_name = "hasreturns"

    def lookups(self, request, model_admin):
        return (("YES", "Yes"),)

    def queryset(self, request, queryset):
        if self.value() == "YES":
            return queryset.filter(
                Exists(
                    JiraReturnIssue.objects.filter(
                        support_ticket=OuterRef("pk"),
                    ),
                ),
            )


class JiraReturnIssueInline(ImmutableMixin, TabularInlineBase):
    model = JiraReturnIssue

    ordering = ["-issue_created_at"]


class HubSpotTasksInline(TabularInlineBase):
    model = SupportTicket.tasks.through


@admin.register(SupportTicket, site=unfold_admin_site)
class SupportTicketAdmin(ImmutableMixin, ModelAdminBase, UnfoldModelAdmin):
    list_display = (
        "hubspot_id",
        "ticket_created_at",
        "subject_truncated",
        "status",
        "priority",
        "customer_link",
        "hubspot_link",
    )

    list_filter = [
        AutocompleteFilterFactory("Customer", "customer"),
        "priority",
        "status",
        ("pipeline", admin.RelatedOnlyFieldListFilter),
        SupportTicketPipelineStageFilter,
        "animal_type",
        "source_type",
        "ticket_category",
        SupportTicketWithReturnIssueFilter,
    ]

    list_select_related = ["customer__user"]

    ordering = ["-ticket_created_at"]

    search_fields = ["hubspot_id", "subject"]

    inlines = [JiraReturnIssueInline, HubSpotTasksInline]

    fieldsets = (
        (
            None,
            {
                "fields": [
                    "customer",
                    "hubspot_link",
                    "priority",
                    "status",
                    "source_type",
                    "animal_type",
                    "ticket_category",
                    "pipeline",
                    "pipeline_stage",
                    "ticket_created_at",
                    "ticket_modified_at",
                    "subject",
                    "content",
                ],
            },
        ),
        (
            _("Billy timestamps"),
            {
                "fields": [
                    "created_at",
                    "updated_at",
                ],
            },
        ),
        (
            _("Extra Data"),
            {
                "fields": ("extra_data",),
                "classes": ["collapse in"],
            },
        ),
    )

    def extra_data(self, instance):
        """Function to display pretty version of the data"""
        return format_html("<pre>{}</pre>", pprint.pformat(instance.extra))

    @admin.display(description="Customer")
    def customer_link(self, obj):
        if obj.customer is not None:
            url = reverse("admin:core_customer_change", args=(obj.customer.pk,))
            return format_html("<a href='{}'>{}</a>", url, obj.customer)
        return "-"

    @admin.display(description="Subject", ordering="subject")
    def subject_truncated(self, obj):
        return truncatechars(obj.subject, 40)


@admin.register(XeroAuthConnection, site=unfold_admin_site)
class XeroAuthConnectionAdmin(ModelAdminBase, UnfoldModelAdmin):
    change_list_template = "admin/xeroauthconnection_changelist.html"

    list_display = ["tenant_name", "tenant_type", "linked_to"]
    search_fields = ["tenant_name"]
    readonly_fields = [
        "tenant_id",
        "tenant_name",
        "tenant_type",
        "connection_id",
        "connection_created_at",
        "connection_updated_at",
        "created_at",
        "updated_at",
    ]

    @admin.display(description="Linked to company")
    def linked_to(self, obj):
        if obj.nofence_company is None:
            url = reverse("admin:core_xeroauthconnection_change", args=(obj.pk,))
            return format_html(
                '<span style="color: #db851d;">Link missing.</span> (<a href="{}">Must be linked manually</a>)',
                url,
            )
        url = reverse(
            "admin:core_nofencecompany_change",
            args=(obj.nofence_company.pk,),
        )
        return format_html("<a href='{}'>{}</a>", url, obj.nofence_company)

    def has_add_permission(self, *args, **kwargs):
        # Should be added through the xero-auth-connect url endpoint
        return False


@admin.register(XeroItem, site=unfold_admin_site)
class XeroItemAdmin(ModelAdminBase, UnfoldModelAdmin):
    list_display = ["item_id", "sku", "name", "nofence_company"]
    search_fields = ["sku", "name"]
    list_filter = ["nofence_company"]


@admin.register(SalesOrderRecord, site=unfold_admin_site)
class SalesOrderRecordAdmin(ImmutableMixin, ModelAdminBase, UnfoldModelAdmin):
    list_display = (
        "record_id",
        "record_type",
        "status",
        "created_at_date",
        "record_updated_at",
        "accounting_system",
        "customer",
    )

    list_filter = [
        "accounting_system",
        "nofence_company",
        "record_type",
        "status",
        AutocompleteFilterFactory("Customer", "customer"),
    ]

    ordering = ["-record_created_at"]

    search_fields = ["record_id"]

    fieldsets = (
        (
            None,
            {
                "fields": [
                    "record_id",
                    "record_type",
                    "status",
                    "accounting_system",
                    "nofence_company",
                    "record_created_at",
                    "record_updated_at",
                    "customer",
                ],
            },
        ),
        (
            _("Billy timestamps"),
            {
                "fields": [
                    "created_at",
                    "updated_at",
                ],
            },
        ),
        (
            _("Document Data"),
            {
                "fields": ("document_data",),
                "classes": ["collapse in"],
            },
        ),
    )

    def document_data(self, instance):
        """Function to display pretty version of the data"""
        # Convert the data to sorted, indented JSON
        return format_html("<pre>{}</pre>", pprint.pformat(instance.body))

    @admin.display(ordering="record_created_at")
    def created_at_date(self, instance):
        return instance.record_created_at.date()


@admin.register(HubSpotTaskModel, site=unfold_admin_site)
class HubSpotTaskModelAdmin(ImmutableMixin, ModelAdminBase, UnfoldModelAdmin):
    list_display = (
        "hubspot_id",
        "task_type",
        "task_status",
        "task_subject",
        "task_created_at",
        "task_updated_at",
    )

    ordering = ["-task_created_at"]

    search_fields = ["hubspot_id"]

    list_filter = [
        "task_type",
        "task_status",
        AutocompleteFilterFactory("Support Tickets", "support_tickets"),
    ]

    fieldsets = (
        (
            None,
            {
                "fields": [
                    "hubspot_id",
                    "task_type",
                    "task_status",
                    "task_subject",
                    "task_body_preview",
                    "support_tickets",
                ],
            },
        ),
        (
            _("Billy timestamps"),
            {
                "fields": [
                    "created_at",
                    "updated_at",
                ],
            },
        ),
        (
            _("Document Data"),
            {
                "fields": ("document_data",),
                "classes": ["collapse in"],
            },
        ),
    )

    def document_data(self, instance):
        # Convert the data to sorted, indented JSON
        return format_html("<pre>{}</pre>", pprint.pformat(instance.raw_data))


@admin.register(MassEmail, site=unfold_admin_site)
class MassEmailAdmin(ModelAdminBase, UnfoldModelAdmin):
    form = MassEmailForm

    list_display = (
        "name",
        "from_email",
        "template_id",
        "created_at",
    )

    ordering = ["-created_at"]

    search_fields = ["name", "template_id", "from_email"]

    actions = ["create_records_from_csv"]

    # TODO: There is some duplicate code in SendMassEmailAction class, ideally we would rewrite
    # the action bellow using SendMassEmailAction class
    @admin.action(
        description="Send from CSV",
        permissions=["upload_csv"],
    )
    def create_records_from_csv(self, request, queryset, *args, **kwargs):
        if queryset.count() != 1:
            self.message_user(
                request,
                "Can only send to a single mass email",
                messages.ERROR,
            )
            return

        mass_email: MassEmail = queryset.first()

        template = ("admin/send_mass_email_from_csv.html",)
        if "was_uploaded" in request.POST:
            form = UploadCSVMassEmailForm(request.POST, request.FILES)
            if form.is_valid():
                try:
                    count = create_email_service().send_customer_mass_email_from_csv(
                        mass_email,
                        codecs.iterdecode(form.cleaned_data["csv_file"], "utf-8-sig"),
                    )
                    self.message_user(
                        request,
                        f"Enqueued {count} new mass email records for sending.",
                        messages.SUCCESS,
                    )
                    return redirect(request.get_full_path())
                except RuntimeError as e:
                    form.add_error(None, str(e))
        else:
            form = UploadCSVMassEmailForm()

        opts = self.model._meta

        context = {
            **self.admin_site.each_context(request),
            "title": f"Send mass email {mass_email} from CSV",
            "subtitle": "",
            "selected_objects": queryset,
            "mass_email": mass_email,
            "form": form,
            "opts": opts,
            "app_label": opts.app_label,
        }

        return render(request, template, context=context)

    def has_upload_csv_permission(self, request, obj=None):
        return request.user.has_perm("core.add_massemail")


@admin.register(MassEmailRecord, site=unfold_admin_site)
class MassEmailRecordAdmin(
    ImmutableMixinWithDelete,
    SimpleHistoryAdmin,
    UnfoldModelAdmin,
):
    list_display = (
        "mass_email",
        "status",
        "customer_link",
        "updated_at",
    )
    search_fields = ["customer__name", "customer__email"]
    ordering = ["-updated_at"]

    list_filter = [
        AutocompleteFilterFactory("Mass Email", "mass_email"),
        AutocompleteFilterFactory("Customer", "customer"),
    ]

    actions_detail = [
        "view_email",
    ]

    @unfold_action(description="View Email")
    def view_email(self, request, object_id: int, permissions=[]):
        obj: MassEmailRecord = self.get_object(request, object_id)
        url = reverse("admin-preview-mass-email-record", kwargs={"id": obj.id})
        return redirect(url)

    @admin.display(description="Customer", ordering="customer")
    def customer_link(self, obj):
        url = reverse("admin:core_customer_change", args=(obj.customer.pk,))
        return format_html("<a href='{}'>{}</a>", url, obj.customer)


@admin.register(CollarTag, site=unfold_admin_site)
class CollarTag(ModelAdminBase, UnfoldModelAdmin):
    form = CollarTagForm

    list_display = ("name", "description", "block_shipping", "created_at", "created_by")

    search_fields = ("name", "description")

    ordering = ["name"]

    readonly_fields = ["created_by"]

    def save_model(self, request, obj, form, change):
        if getattr(obj, "created_by", None) is None:
            obj.created_by = request.user
        return super().save_model(request, obj, form, change)


class CollarUsageMonthlyProductInline(TabularInlineBase):
    model = CollarUsageMonthlyProduct


class CollarUsageMonthlyIsInvoicedFilter(SimpleListFilter):
    title = "Is Invoiced"
    parameter_name = "is_invocied"

    def lookups(self, request, model_admin):
        return [
            ("yes", _("Yes")),
            ("no", _("No")),
        ]

    def queryset(self, request, queryset):
        if self.value() == "yes":
            return queryset.filter(
                Exists(InvoicedUsage.objects.filter(usage=OuterRef("pk"))),
            )
        elif self.value() == "no":
            return queryset.filter(
                ~Exists(InvoicedUsage.objects.filter(usage=OuterRef("pk"))),
            )
        else:
            return queryset


class CollarUsageMonthlyUnbilledLateArrivingUsageFilter(SimpleListFilter):
    title = "Unbilled usage in already billed months"
    parameter_name = "late_arriving_unbilled"

    def lookups(self, request, model_admin):
        return [
            ("yes", _("Yes")),
        ]

    def queryset(self, request, queryset):
        if self.value() == "yes":
            invoiced = (
                InvoicedUsage.objects.filter(usage=OuterRef("pk"))
                .values("usage")
                .annotate(total_billed=Sum("billed_usage"))
                .values("total_billed")
            )

            return queryset.annotate(total_billed=Subquery(invoiced)).filter(
                flexible_usage__gt=F("total_billed"),
            )

        return queryset


@admin.register(CollarUsageMonthly, site=unfold_admin_site)
class CollarUsageMonthlyAdmin(SimpleHistoryAdmin, UnfoldModelAdmin):
    list_display = (
        "customer",
        "formatted_month",
        "total_usage",
        "pre_paid_usage",
        "flexible_usage",
    )

    ordering = ["-month"]

    readonly_fields = ["usage_caps", "created_at", "updated_at", "free_usage"]

    search_fields = ["month"]

    autocomplete_fields = ["customer"]

    inlines = [CollarUsageMonthlyProductInline, InvoicedUsageInline]

    list_filter = [
        AutocompleteFilterFactory("Customer", "customer"),
        "customer__nofence_company",
        ("month", RangeDateFilter),
        CollarUsageMonthlyIsInvoicedFilter,
        CollarUsageMonthlyUnbilledLateArrivingUsageFilter,
    ]

    fieldsets = (
        (
            None,
            {
                "fields": [
                    "month",
                    "customer",
                    "total_usage",
                    "pre_paid_usage",
                    "flexible_usage",
                    "created_at",
                    "updated_at",
                    "free_usage",
                ],
            },
        ),
        (
            _("Usage Cap Info"),
            {
                "fields": ("usage_caps",),
                "classes": ["collapse in"],
            },
        ),
    )

    actions = [
        "invoice_usage",
        "refresh_usage",
    ]

    def get_list_display(self, request):
        fields = super().get_list_display(request)
        if "late_arriving_unbilled" in request.GET:
            # These fields are only available when the CollarUsageMonthlyUnbilledLateArrivingUsageFilter is used
            return [*fields, "total_billed", "unbilled_usage"]
        return fields

    @admin.display
    def total_billed(self, obj: CollarUsageMonthly):
        return obj.total_billed

    @admin.display
    def unbilled_usage(self, obj: CollarUsageMonthly):
        return obj.flexible_usage - obj.total_billed

    @admin.display(description=_("Month"), ordering="month")
    def formatted_month(self, obj: CollarUsageMonthly):
        return date_format(obj.month, "M, Y")

    @admin.display(description=_("Free usage available"))
    def free_usage(self, obj: CollarUsageMonthly):
        return obj.get_total_available_free_usage_caps()

    @admin.display(description=_("Usage caps"))
    def usage_caps(self, obj: CollarUsageMonthly):
        caps = obj.get_total_available_free_usage_caps()

        html = []

        html.append(
            """
        <p>Free usage is calculated based on the following flexible usage data:</p>
        """,
        )

        html.append(
            format_html(
                """
        <table style="width: 100%;">
        <tr>
            <th>Month ({})</th><th>Flexible Usage</th>
        </tr>
        """,
                obj.month.year,
            ),
        )

        usage = self.get_monthly_flex_usage_from_start_of_year(obj)

        for i in range(1, obj.month.month):
            m = date(obj.month.year, i, 1)
            html.append(
                format_html(
                    """
                <tr>
                    <td>{}</td>
                    <td>{}</td>
                </tr>
                """,
                    date_format(m, "M"),
                    usage[m],
                ),
            )

        html.append(
            format_html(
                """
        <tr>
            <th colspan="2"></th>
        </tr>
        <tr>
            <th colspan="1">Caps Reached:</th>
            <td>{}</td>
        </tr>
        """,
                caps,
            ),
        )
        html.append("</table>")

        return mark_safe("".join(html))

    def get_monthly_flex_usage_from_start_of_year(self, usage: CollarUsageMonthly):
        year = usage.month.replace(month=1)
        month = usage.month
        usage_data = {
            u.month: u.flexible_usage
            for u in usage.customer.collarusagemonthly.filter(
                month__gte=year,
                month__lt=month,
            ).all()
        }

        usage = {}
        for i in range(1, month.month):
            m = date(month.year, i, 1)
            usage[m] = usage_data.get(m, 0)

        return usage

    @admin.action(description="Invoice usage", permissions=["create_invoice"])
    def invoice_usage(self, request, queryset):
        if queryset.count() >= 20:
            self.message_user(
                request,
                "This action is only intended for a small number of usages (< 20)",
                messages.WARNING,
            )
            return

        biller = create_usage_biller_flexible_monthy(
            # Skip refreshing the usage here, as we sometimes want to do some manual testing,
            # with manually created data. If we refresh here the manually created data may be
            # cleared.
            refresh_monthly_summary=False,
        )

        for usage in queryset:
            usage: CollarUsageMonthly
            biller.bill_customer_for_month(usage.customer, usage.month)

        self.message_user(
            request,
            f"Billed {len(queryset)} usages(s)",
            messages.SUCCESS,
        )

    def has_create_invoice_permission(self, request, obj=None):
        return request.user.has_perm("core.add_invoice")

    @admin.action(description="Refresh usage calculation", permissions=["refresh_usage"])
    def refresh_usage(self, request, queryset):
        if queryset.count() >= 20:
            self.message_user(
                request,
                "This action is only intended for a small number of usages (< 20)",
                messages.WARNING,
            )
            return

        summarizer = create_usage_summarizer()

        for usage in queryset:
            usage: CollarUsageMonthly
            summarizer.update_monthly_usage_summary(usage.customer, usage.month)

        self.message_user(
            request,
            f"Refreshed {len(queryset)} usages(s)",
            messages.SUCCESS,
        )

    def has_refresh_usage_permission(self, request, obj=None):
        return request.user.has_perm("core.change_collarusagemonthly")


class EmailRecordEventInline(ImmutableMixin, StackedInlineBase):
    model = EmailRecordEvent

    fields = ("event_type", "event_time", "formatted_payload")

    readonly_fields = ["formatted_payload"]

    ordering = ["-event_time"]

    classes = ["collapse"]

    @admin.display(description="Payload")
    def formatted_payload(self, obj):
        return format_html("<pre>{}</pre>", pprint.pformat(obj.payload))


class EmailRecordSalesOrderFilter(AutocompleteFilter):
    title = "Sales Order"
    field_name = "sales_order"
    parameter_name = "data__sales_order_id"

    # Small hack, we use an unrelated model, but we just need a model that has a foreign key
    # to the sales order for the auto complete to work
    rel_model = InvoiceDocument

    def queryset(self, request, queryset):
        if self.value():
            so: SalesOrder = SalesOrder.objects.filter(id=int(self.value())).first()
            if so is not None:
                conditions = [Q(data__sales_order_id=so.id)]
                inv = so.accounting_invoice_document
                if inv is not None:
                    conditions.extend(
                        EmailRecordInvoiceDocumentFilter.get_invoice_doc_conditions(
                            inv,
                        ),
                    )
                inv = so.accounting_payment_request_document
                if inv is not None:
                    conditions.extend(
                        EmailRecordInvoiceDocumentFilter.get_invoice_doc_conditions(
                            inv,
                        ),
                    )
                return queryset.filter(reduce(operator.or_, conditions))
        return queryset


class EmailRecordInvoiceDocumentFilter(AutocompleteFilter):
    title = "Invoice Document"
    field_name = "invoice"

    # Small hack, we use an unrelated model, but we just need a model that has a foreign key
    # to the invoice document for the auto complete to work
    rel_model = PaymentReminder

    def queryset(self, request, queryset):
        if self.value():
            doc = InvoiceDocument.objects.filter(id=int(self.value())).first()
            if doc is not None:
                conditions = EmailRecordInvoiceDocumentFilter.get_invoice_doc_conditions(
                    doc,
                )
                return queryset.filter(reduce(operator.or_, conditions))
        return queryset

    @classmethod
    def get_invoice_doc_conditions(cls, invoice_doc: InvoiceDocument) -> List[Q]:
        conditions = [Q(data__invoice_document_id=invoice_doc.id)]
        is_visma = SalesOrderAccountingSystem.is_visma_system(
            SalesOrderAccountingSystem.from_invoice_provider(invoice_doc.provider),
        )
        if is_visma:
            conditions.append(
                Q(data__template_context__reference_number=invoice_doc.reference_number),
            )
        return conditions


class EmailRecordAttachmentInline(ImmutableMixin, TabularInlineBase):
    model = EmailRecordAttachment


@admin.register(EmailRecord, site=unfold_admin_site)
class EmailRecordAdmin(SimpleHistoryAdmin, UnfoldModelAdmin):
    list_display = (
        "email_type",
        "to_email",
        "status",
        "customer_link",
        "created_at",
        "updated_at",
    )

    ordering = ["-created_at"]

    readonly_fields = ["created_at", "updated_at"]
    search_fields = ["to_email"]

    list_select_related = ["customer__nofence_company"]

    autocomplete_fields = ["customer"]

    list_filter = [
        AutocompleteFilterFactory("Customer", "customer"),
        "customer__nofence_company",
        "email_type",
        "status",
        EmailRecordSalesOrderFilter,
        EmailRecordInvoiceDocumentFilter,
    ]

    inlines = [EmailRecordEventInline, EmailRecordAttachmentInline]

    actions = [
        "send_email_record",
        "send_test_email",
    ]

    actions_list = ["view_samples"]
    actions_detail = ["view_email", "send_test_email"]

    @unfold_action(description=_("View Samples"), url_path="admin-preview-email-record-samples")
    def view_samples(self, request: HttpRequest, permissions=["core.view_emailrecord"]):
        return redirect(
            reverse("admin-preview-email-record-samples"),
        )

    @unfold_action(description=_("View Email"))
    def view_email(self, request: HttpRequest, object_id: int, permissions=[]):
        return redirect(
            reverse("admin-preview-email-record", args=(object_id,)),
        )

    @admin.display(description="Customer", ordering="customer")
    def customer_link(self, obj):
        if obj.customer:
            url = reverse("admin:core_customer_change", args=(obj.customer.pk,))
            return format_html("<a href='{}'>{}</a>", url, obj.customer)
        return "-"

    # TODO: There is some duplicate code in SendMassEmailAction class, ideally we would rewrite
    # the action bellow using SendMassEmailAction class
    @admin.action(
        description="Queue and Send Emails",
        permissions=["send_email_record"],
    )
    def send_email_record(self, request, queryset):
        service = create_email_service()

        for record in queryset:
            record: EmailRecord
            if record.status != EmailRecord.Status.QUEUED:
                record.status = EmailRecord.Status.QUEUED
                record.save()
            service.send_email_record(record)

        self.message_user(
            request,
            f"Queued {len(queryset)} records(s) for sending",
            messages.SUCCESS,
        )

    def has_send_email_record_permission(self, request, obj=None):
        return request.user.has_perm("core.change_emailrecord")

    @unfold_action(description="Test Email", permissions=["send_test_email"])
    def send_test_email(self, request, queryset=None, object_id=None):
        template = ("admin/test_email_records.html",)

        if object_id:
            queryset = EmailRecord.objects.filter(id=object_id)

        if "send_email" in request.POST:
            form = TestEmailRecordForm(request.POST)
            if form.is_valid():
                service = create_email_service()
                to_email = form.cleaned_data["to_email"]
                force_send = form.cleaned_data.get("force_send", False)
                for record in queryset:
                    service.send_email_record_admin_override(
                        record,
                        to_email,
                        force_send=force_send,
                    )
                self.message_user(
                    request,
                    f"Queued {len(queryset)} records(s) for sending to {to_email}",
                    messages.SUCCESS,
                )
                return redirect(request.get_full_path())
        else:
            form = TestEmailRecordForm(
                initial={
                    "to_email": request.user.email,
                    "force_send": True,
                },
            )

        opts = self.model._meta

        context = {
            **self.admin_site.each_context(request),
            "title": "Send test email",
            "subtitle": "",
            "selected_objects": queryset,
            "form": form,
            "opts": opts,
            "app_label": opts.app_label,
        }

        return render(request, template, context=context)

    def has_send_test_email_permission(self, request, obj=None):
        return request.user.has_perm("core.send_test_email")


class SkuFilter(SimpleListFilter):
    title = "Sku"
    parameter_name = "sku"

    def lookups(self, request, model_admin):
        return [
            (product.sku, product.sku)
            for product in Product.objects.filter(status=Product.ProductStatus.ACTIVE, is_inventory_product=True)
        ]

    def queryset(self, request, queryset):
        if self.value():
            # skus = getattr(products, self.value(), {}).keys()
            return queryset.filter(
                Q(
                    Exists(
                        CollarReplacement.objects.filter(
                            return_and_replacement=OuterRef("pk"),
                            product__sku=self.value(),
                        ),
                    )
                    | Exists(
                        ReturnAndReplacementExtraItem.objects.filter(
                            return_and_replacement=OuterRef("pk"),
                            product__sku=self.value(),
                        ),
                    ),
                ),
            )
        else:
            return queryset


class ReadOnlyURLWidget(forms.URLInput):
    def __init__(self, url="", attrs=None):
        self.url = url
        super().__init__(attrs)

    def render(self, name, value, attrs=None, renderer=None):
        if value:
            return format_html('<a href="{0}" target="_blank">{0}</a>', value)
        else:
            return format_html("<span>-</span>")


class CollarReplacementInlineForm(ModelFormBase):
    class Meta:
        model = CollarReplacement
        fields = "__all__"

    def __init__(self, *args, **kwargs):
        super().__init__(*args, **kwargs)
        if self.instance and self.instance.jira_issue:
            self.fields["jira_issue"].widget = ReadOnlyURLWidget(url=self.instance.jira_issue)


class CollarReplacementInline(StackedInlineBase):
    model = CollarReplacement
    form = CollarReplacementInlineForm
    autocomplete_fields = [
        "collar",
        "replacement_collar",
        "product",
        "replacement_product",
    ]
    extra = 0
    min_num = 0

    def get_fieldsets(self, request, obj=None):
        fieldsets = (
            (
                None,
                {
                    "fields": [
                        "collar",
                        "reason_for_replacement",
                        "product",
                        "replacement_product",
                        "chain_size",
                        "replacement_collar",
                        "jira_issue",
                    ],
                },
            ),
        )

        if obj is not None:
            fieldsets[0][1]["description"] = """
                <i style="background: var(--message-warning-bg)">
                Note: Changes to existing collar replacements from the admin will not
                be automatically synced to the related sales order and shipment</i>
            """
        return fieldsets

    def get_min_num(self, request, obj=None, **kwargs):
        return max(0, len(request.GET.getlist("collar", [])))


class ReturnAndReplacementExtraItemInline(TabularInlineBase):
    model = ReturnAndReplacementExtraItem

    autocomplete_fields = ["product"]

    verbose_name = "Extra Item"


class ReplacementDeliveryAddressInline(TabularInlineBase):
    model = ReplacementDeliveryAddress
    min_num = 1
    max_num = 1
    can_delete = False


class RRSalesOrderInline(ImmutableMixin, TabularInlineBase):
    model = SalesOrder
    fields = ["status", "order_type", "shipment_warehouse", "inco_terms", "currency"]
    show_change_link = True


@admin.register(ReturnAndReplacement, site=unfold_admin_site)
class ReturnAndReplacementAdmin(SimpleHistoryAdmin, UnfoldModelAdmin):
    custom_add_form = ReturnAndReplacementForm

    list_display = ["created_at", "delivered_at", "deactivate_at", "status", "customer_link", "sales_order_link"]

    list_select_related = ["customer__user", "sales_order"]

    add_form_template = "admin/rr_change_form.html"

    ordering = ["-pk"]

    autocomplete_fields = ["customer"]

    search_fields = [
        "id",
        "sales_order__generated_order_number",
    ]

    list_filter = [
        AutocompleteFilterFactory("Customer", "customer"),
        ("created_at", RangeDateFilter),
        ("delivered_at", RangeDateFilter),
        "status",
        "nofence_company",
        "return_old_collars",
        "customer__country",
        SkuFilter,
    ]

    actions = [
        "start_return_processing_action",
        "download_csv_for_replaced_orders",
        "cancel_return",
    ]

    inlines = [
        ReplacementDeliveryAddressInline,
        CollarReplacementInline,
        ReturnAndReplacementExtraItemInline,
        RRSalesOrderInline,
    ]

    def save_related(self, request, form, formsets, change):
        super().save_related(request, form, formsets, change)
        is_adding_new = not change
        rr: ReturnAndReplacement = form.instance
        if is_adding_new:
            data = form.cleaned_data
            if data.get("start_processing", False):
                self.start_return_processing(request, rr)
            else:
                rr.status = ReturnAndReplacement.Status.DUE_FOR_REPLACEMENT
                rr.save()

        # Tag the collars immediately. No problem if a collar is tagged twice.
        success = rr.set_due_for_replacement_tag()
        if not success:
            self.message_user(
                request,
                "Collars could not be tagged as due for replacement. Contact software team.",
                messages.WARNING,
            )

    def get_form(self, request, obj=None, change=False, **kwargs):
        defaults = {}
        if obj is None:
            defaults["form"] = self.custom_add_form
        defaults.update(kwargs)
        return super().get_form(request, obj, change, **defaults)

    def start_return_processing(self, request, rr: ReturnAndReplacement):
        rr.create_sales_order_and_shipment()

        if rr.return_old_collars:
            rr.status = ReturnAndReplacement.Status.PENDING_DELIVERY
        else:
            rr.status = ReturnAndReplacement.Status.NO_RETURN
        rr.save()

        # Send emails as the last step in case something crashes above
        rr.send_email()

    @admin.action(
        description="Start RR Processing",
        permissions=["start_return_processing"],
    )
    def start_return_processing_action(self, request, queryset):
        if queryset.exclude(
            status=ReturnAndReplacement.Status.DUE_FOR_REPLACEMENT,
        ).exists():
            self.message_user(
                request,
                "Only Return and Replacements with status 'Due for replacement' can be started",
                messages.ERROR,
            )
            return

        count = 0
        for rr in queryset:
            rr: ReturnAndReplacement
            with transaction.atomic():
                self.start_return_processing(request, rr)
                count += 1

        self.message_user(
            request,
            f"Started processing of {count} RR orders",
            messages.SUCCESS,
        )

    @admin.action(
        description="Download CSV for replaced orders",
    )
    def download_csv_for_replaced_orders(self, request, queryset):
        response = HttpResponse(content_type="text/csv")
        response["Content-Disposition"] = "attachment; filename=rr_items.csv"
        writer = csv.writer(response)

        # get collars
        collar_replacements = (
            queryset.order_by()
            .values(
                "status",
                "nofence_company__code",
                "created_at",
                "delivered_at",
                "customer__country",
                "return_old_collars",
                sku=F("collars__replacement_product__sku"),
                replaced_sku=F("collars__product__sku"),
                chain_size=F("collars__chain_size"),
                reason_for_replacement=F("collars__reason_for_replacement"),
            )
            .annotate(
                total=Count("id"),
            )
        )
        # get extra items
        extra_items = (
            queryset.order_by()
            .values(
                "status",
                "nofence_company__code",
                "created_at",
                "delivered_at",
                "customer__country",
                "return_old_collars",
                sku=F("extra_items__product__sku"),
                reason_for_replacement=F("extra_items__description"),
            )
            .annotate(
                total=Sum("extra_items__quantity"),
            )
        )

        # combine collar replacements and extra items
        items = []
        combined_dict = defaultdict(lambda: {"ordered": 0, "total": 0})
        for item in itertools.chain(collar_replacements, extra_items):
            if item["sku"]:  # some skus are empty
                key = (
                    item["sku"],
                    item["status"],
                    item["nofence_company__code"],
                    item["created_at"],
                    item["delivered_at"],
                    item.get("replaced_sku", ""),
                    item["reason_for_replacement"],
                    item.get("chain_size", ""),
                    item["customer__country"],
                    item["return_old_collars"],
                )
                combined_dict[key]["total"] += item["total"]

        # Convert the defaultdict to a list of dictionaries
        combined_items = [
            {
                "sku": key[0],
                "total": value["total"],
                "status": key[1],
                "nofence_company": key[2],
                "created_at": key[3],
                "delivered_at": key[4],
                "replaced_sku": key[5],
                "reason_for_replacement": key[6],
                "chain_size": key[7],
                "customer_country": key[8],
                "return_old_collars": key[9],
            }
            for key, value in combined_dict.items()
        ]
        for item in combined_items:
            inventory = Inventory.objects.filter(
                product__sku=item["sku"],
                location__warehouse__nofence_company__code=item["nofence_company"],
                location__location_type=WarehouseLocation.LocationType.PRIMARY,
            )
            if inventory.exists():
                available = inventory.first().available_quantity
            else:
                available = 0
            items.append(
                {
                    "sku": item["sku"],
                    "ordered": item["total"],
                    "status": item["status"],
                    "nofence_company": item["nofence_company"],
                    "available": available,
                    "created_at": item["created_at"],
                    "delivered_at": item["delivered_at"],
                    "replaced_sku": item["replaced_sku"],
                    "reason_for_replacement": item["reason_for_replacement"],
                    "chain_size": item["chain_size"],
                    "customer_country": item["customer_country"],
                    "return_old_collars": item["return_old_collars"],
                },
            )

        # write csv
        writer.writerow(
            [
                "sku",
                "quantity_ordered",
                "quantity_available",
                "status",
                "inventory_location",
                "created_at",
                "delivered_at",
                "replaced_sku",
                "reason_for_replacement",
                "chain_size",
                "customer_country",
                "return_old_collars",
            ],
        )
        for item in items:
            writer.writerow(
                [
                    item["sku"],
                    item["ordered"],
                    item["available"],
                    item["status"],
                    item["nofence_company"],
                    item["created_at"],
                    item["delivered_at"],
                    item["replaced_sku"],
                    item["reason_for_replacement"],
                    item["chain_size"],
                    item["customer_country"],
                    item["return_old_collars"],
                ],
            )
        return response

    @admin.action(
        description="Cancel Return and Replacement",
    )
    def cancel_return(self, request, queryset):
        if len(queryset) > 1:
            self.message_user(request, "Please select only 1 item", messages.WARNING)
            return

        rr = queryset.first()

        try:
            rr.cancel()
        except Exception as e:
            self.message_user(request, f"Could not cancel RR: {e}", messages.WARNING)
            return

        self.message_user(request, "Cancelled 1 RR", messages.SUCCESS)

    def has_start_return_processing_permission(self, request, obj=None):
        return request.user.has_perm("core.change_returnandreplacement")

    def changeform_view(self, request, object_id=None, form_url="", extra_context=None):
        if extra_context is None:
            extra_context = {}

        extra_context["is_adding_new"] = object_id is None

        return super().changeform_view(request, object_id, form_url, extra_context)

    def get_fieldsets(self, request, obj=None):
        fieldsets = super().get_fieldsets(request, obj=obj)
        if obj is None and "status":
            # We automatically set the status when initially creating the RR.
            fieldsets[0][1]["fields"].remove("status")
            # Removed to make the initial create for cleaner
            fieldsets[0][1]["fields"].remove("delivered_at")

        return fieldsets

    def get_inline_formsets(self, request, formsets, inline_instances, obj=None):
        formsets = super().get_inline_formsets(request, formsets, inline_instances, obj)

        # NOTE: Django 4.0+ have a get_formset_kwargs method that can be used to set initial
        # values on the inline forms, but we are not on this version yet.

        if "collar" in request.GET:
            collars = [Collar.objects.get(id=cid) for cid in request.GET.getlist("collar", [])]
            for formset in formsets:
                if isinstance(formset.opts, CollarReplacementInline):
                    for c, form in zip(collars, formset.forms):
                        form.initial = {
                            "collar": c,
                        }

        return formsets

    @admin.display(description="Customer")
    def customer_link(self, obj):
        url = reverse("admin:core_customer_change", args=(obj.customer.pk,))
        return format_html("<a href='{}'>{}</a>", url, obj.customer)

    @admin.display(description="Sales Order")
    def sales_order_link(self, obj):
        if obj.get_sales_order() is not None:
            url = reverse("admin:core_salesorder_change", args=(obj.sales_order.pk,))
            return format_html("<a href='{}'>{}</a>", url, obj.sales_order)


@admin.register(TicketBAI, site=unfold_admin_site)
class TicketBAIAdmin(SimpleHistoryAdmin, UnfoldModelAdmin):
    list_display = ("invoice_document", "customer_name", "ticket_bai", "created_at")
    list_filter = (("created_at", RangeDateFilter),)
    search_fields = [
        "invoice_document__customer__xero_id",
        "invoice_document__customer__user__email",
        "invoice_document__customer__name",
        "invoice_document__doc_ref",
        "invoice_document__doc_id",
    ]
    fields = ["invoice_document", "code", "ticket_bai"]
    ordering = ["-created_at"]
    date_hierarchy = "created_at"
    autocomplete_fields = ["invoice_document"]
    readonly_fields = ("ticket_bai",)
    list_select_related = [
        "invoice_document__customer__nofence_company",
        "invoice_document__nofence_company",
    ]

    def ticket_bai(self, obj):
        return format_html("<a href='{}'>{}</a>", obj.code, "link")

    @admin.display(
        ordering="invoice_document__customer",
        description="Customer",
    )
    def customer_name(self, obj):
        if obj.invoice_document.customer is not None:
            return f"{obj.invoice_document.customer}"

    def has_change_permission(self, request, obj=None):
        return request.user.is_superuser or settings.DEBUG

    def has_delete_permission(self, request, obj=None):
        return request.user.is_superuser or settings.DEBUG


@admin.register(TermsAndConditions, site=unfold_admin_site)
class TermsAndConditionsAdmin(SimpleHistoryAdmin, TranslationUnfoldModelAdmin):
    list_display = ("name", "link", "type", "nofence_company", "created_at")
    list_filter = (
        ("created_at", RangeDateFilter),
        "nofence_company",
        "type",
    )
    search_fields = [
        "name",
        "link",
    ]
    fields = ["name", "link", "type", "nofence_company"]
    ordering = ["-created_at"]
    date_hierarchy = "created_at"


@admin.register(InvoiceDocumentFile, site=unfold_admin_site)
class InvoiceDocumentFileAdmin(ModelAdminBase, UnfoldModelAdmin):
    list_display = [
        "filename",
        "type",
        "customer",
        "invoice_document_ref",
        "created_at",
        "download_file",
    ]
    search_fields = ["customer__name"]
    list_filter = [
        AutocompleteFilterFactory("Customer", "invoice_document__customer"),
        ("created_at", RangeDateFilter),
        "type",
    ]
    readonly_fields = [
        "download_filename",
        "type",
        "customer",
        "invoice_document",
        "created_at",
    ]
    ordering = ["-created_at"]
    date_hierarchy = "created_at"
    exclude = ["file"]

    @admin.display(description="File")
    def filename(self, obj):
        return obj.file.name

    def customer(self, obj):
        return obj.invoice_document.customer

    @admin.display(description="Invoice document")
    def invoice_document_ref(self, obj):
        return obj.invoice_document.doc_ref

    @admin.display(description="Download file")
    def download_file(self, obj):
        url = obj.file.url
        return format_html('<a target="_blank" href="{url}">PDF</a>', url=url)

    @admin.display(description="File")
    def download_filename(self, obj):
        url = obj.file.url
        name = obj.file.name
        return format_html(
            '<a target="_blank" href="{url}">{name}</a>',
            url=url,
            name=name,
        )


@admin.register(ReportExport, site=unfold_admin_site)
class ReportExportAdmin(ImmutableMixinWithDelete, ModelAdminBase, UnfoldModelAdmin):
    list_display = ("report_name", "created_at", "download_file")

    list_filter = ("report_type",)
    search_fields = ["report_name"]
    readonly_fields = ("created_at", "exported_file", "formatted_metadata")

    fieldsets = (
        (
            None,
            {
                "fields": (
                    "report_type",
                    "report_name",
                    "exported_file",
                    "created_at",
                ),
            },
        ),
        (
            "Metadata",
            {
                "fields": ("formatted_metadata",),
                "classes": ["collapse in"],
            },
        ),
    )

    @admin.display(description="CSV File")
    def download_file(self, obj: ReportExport):
        url = obj.exported_file.url
        name = obj.exported_file.name
        return format_html(
            '<a target="_blank" href="{url}">{name}</a>',
            url=url,
            name=name,
        )

    def formatted_metadata(self, obj: ReportExport):
        html = []
        meta = obj.metadata.copy()

        if "query" in meta:
            query = meta.pop("query")
            html.append(format_html("<h4>Query</h4><pre>{}</pre>", query))

        html.append(
            format_html("<pre>{}</pre>", pprint.pformat(meta, indent=2, width=100)),
        )

        return mark_safe("".join(html))


@admin.register(PurchaseOrderNumber, site=unfold_admin_site)
class PurchaseOrderNumberAdmin(ModelAdminBase, UnfoldModelAdmin):
    list_display = [
        "purchase_order_number",
        "customer",
        "start_date",
        "end_date",
        "created_at",
    ]
    list_filter = [
        CustomerFilter,
    ]
    search_fields = ["customer__name"]
    autocomplete_fields = ["customer"]
    ordering = ["-created_at"]
    date_hierarchy = "created_at"


class SyncStatusNofenceCompanyListFilter(SimpleListFilter):
    title = "Nofence Company"
    parameter_name = "nofence_company"

    def lookups(self, request, model_admin):
        return [(nc.id, nc.name) for nc in NofenceCompany.objects.all()]

    def queryset(self, request, queryset):
        if self.value():
            return queryset.filter(
                Q(customer__nofence_company__id=self.value())
                | Q(sales_order__nofence_company__id=self.value())
                | Q(invoice__nofence_company__id=self.value()),
            )


class InputFilter(SimpleListFilter):
    template = "admin/input_filter.html"

    def lookups(self, request, model_admin):
        return ((),)

    def choices(self, changelist):
        # Grab only the "all" option.
        all_choice = next(super().choices(changelist))
        all_choice["query_string_tpl"] = changelist.get_query_string(
            {self.parameter_name: "INPUT-VALUE"},
            [self.parameter_name],
        )

        yield all_choice

    def queryset(self, request, queryset):
        if self.value():
            return queryset.filter(
                Q(customer__nofence_company__id=self.value()) | Q(sales_order__nofence_company__id=self.value()),
            )


class MessageRegexFilter(InputFilter):
    parameter_name = "format_message"
    title = "Message Regex Filter"

    def queryset(self, request, queryset):
        if self.value() is not None:
            regex = self.value()
            return queryset.filter(Q(message__iregex=regex))


class SyncStatusSummaryFilter(SimpleListFilter):
    parameter_name = "show_summary"
    title = "Show summary message"

    def lookups(self, request, model_admin):
        return [("show_summary", "Show Summary")]

    def queryset(self, request, queryset):
        return queryset


@admin.register(SyncStatus, site=unfold_admin_site)
class SyncStatusAdmin(ImmutableMixinWithDelete, InlineActionsMixin, ModelAdminBase, UnfoldModelAdmin):
    autocomplete_fields = ["customer", "sales_order"]
    ordering = ["-updated_at"]

    list_select_related = ["customer__user", "sales_order"]

    search_fields = ["object_id", "message"]
    djangoql_completion_enabled_by_default = False

    readonly_fields = [
        "format_traceback",
        "get_from",
        "get_to",
        "link_to_tasklog",
        "format_status",
    ]

    exclude = ["traceback"]

    fieldsets = (
        (
            None,
            {
                "fields": [
                    "object_id",
                    "task_type",
                    "format_status",
                    "message",
                    "exception_class",
                    "format_traceback",
                    "customer",
                    "sales_order",
                    "link_to_tasklog",
                    "last_updated",
                ],
            },
        ),
        (
            "Sync targets",
            {
                "fields": [
                    "get_from",
                    "get_to",
                ],
            },
        ),
        (
            "Details",
            {
                "fields": [
                    "metadata",
                ],
                "classes": ["collapse in"],
            },
        ),
    )

    list_filter = [
        "status",
        ("task_type", custom_titled_filter("Sync")),
        CustomerFilter,
        SalesOrderFilter,
        AutocompleteFilterFactory("Invoice", "invoice"),
        SyncStatusNofenceCompanyListFilter,
        MessageRegexFilter,
        "customer__country",
        "customer__customer_status",
        "ownership_dep",
        AutocompleteFilterFactory("Assigned to", "assigned_to"),
        SyncStatusSummaryFilter,
    ]

    actions = [
        "update_ownership",
        "retry_task",
    ]

    actions_detail = [
        "retry_task",
    ]

    def get_list_display(self, request):
        if "show_summary" in request.GET:
            list_display = [
                "format_status_for_summary",
                "get_summary",
                "format_message_for_summary",
                "customer_status",
                "synced_at",
                "ownership_dep",
                "assigned_to",
            ]
        else:
            list_display = [
                "task_type",
                "format_status",
                "get_from",
                "get_to",
                "format_message",
                "customer_status",
                "synced_at",
                "customer_link",
                "ownership_dep",
                "assigned_to",
            ]
        return list_display

    @admin.action(description="Update ownership", permissions=["update_ownership"])
    def update_ownership(self, request, queryset: QuerySet[SyncStatus]):
        count = 0
        for status in queryset:
            status.update_ownership()
            count += 1

        self.message_user(
            request,
            format_html(
                f"Updated {count} status(es)",
                messages.SUCCESS,
            ),
        )

    def has_update_ownership_permission(self, request, obj=None):
        return request.user.has_perm("core.view_user")

    @admin.display(description="From")
    def get_from(self, obj: SyncStatus):
        return obj.get_from_link()

    @admin.display(description="To")
    def get_to(self, obj: SyncStatus):
        return obj.get_to_link()

    @admin.display(description="Status")
    def format_status(self, obj: SyncStatus):
        colors = {
            "SUCCESS": "#39cc18",
            "FAILED": "#cc2718",
        }
        color = colors.get(obj.status, "#ccc")
        return format_html('<span style="color:{};">{}</b>', color, obj.status)

    @admin.display(description="Status", ordering="status")
    def format_status_for_summary(self, obj: SyncStatus):
        if obj.status == SyncStatus.Status.FAILED:
            return format_html('<span style="color:#cc2718;">Failed</span>')
        else:
            return format_html('<span style="color:#39cc18;">Successfully</span>')

    @admin.display(description="Summary")
    def get_summary(self, obj: SyncStatus):
        return obj.get_summary()

    @admin.display(description="Error", ordering="message")
    def format_message_for_summary(self, obj: SyncStatus):
        msg = textwrap.shorten(obj.message, width=320)
        return format_html(
            '<span style="color: #ce1111; font-size: 0.9rem;">{}</span>',
            mark_safe("<br>".join(textwrap.wrap(msg, width=80))),
        )

    @admin.display(description="Traceback")
    def format_traceback(self, obj: SyncStatus):
        return format_html(
            '<pre style="white-space: pre-wrap;">{}</pre>',
            obj.traceback,
        )

    @admin.display(description="Message")
    def format_message(self, obj: SyncStatus):
        return textwrap.shorten(obj.message, width=400)

    @admin.display(description="Last Updated")
    def last_updated(self, obj: SyncStatus):
        return obj.last_updated_at or obj.updated_at

    @admin.display(description="Last synced")
    def synced_at(self, obj: SyncStatus):
        return obj.last_updated_at or obj.updated_at

    @admin.display(description="Tasklog")
    def link_to_tasklog(self, obj: SyncStatus):
        return obj.get_tasklog_link()

    @admin.display(description="Customer status", ordering="customer__customer_status")
    def customer_status(self, obj):
        if obj.customer:
            return obj.customer.get_customer_status_display()
        return "-"

    def customer_link(self, obj):
        if not obj.customer:
            return "-"
        url = reverse("admin:core_customer_change", args=(obj.customer.pk,))
        return format_html("<a href='{}'>{}</a>", url, obj.customer)

    customer_link.admin_order_field = "customer"
    customer_link.short_description = "customer"

    @unfold_action(description="Retry task", attrs={"use_post_request": True}, permissions=["retry_task"])
    def retry_task(self, request, queryset=None, object_id=None):
        count, skipped = 0, 0

        if object_id:
            queryset = self.get_queryset(request).filter(id=object_id)

        from celery.canvas import Signature

        for status in queryset.all():
            status: SyncStatus
            if status.metadata and "celery_task_signature" in status.metadata:
                task = Signature.from_dict(status.metadata["celery_task_signature"])
                task_id = task.delay()
                logger.info(f"Retried {task=} for {status=} in {task_id}")
                count += 1
            else:
                skipped += 1

        if skipped > 0:
            self.message_user(
                request,
                f"Skipped {skipped} tasks, because of missing task metadata.",
                messages.WARNING,
            )
        if count > 0:
            self.message_user(
                request,
                f"Retried {count} tasks",
                messages.SUCCESS,
            )

        if object_id:
            return redirect(self.get_redirect_url_for_inline_action(request, obj_pk=object_id))

    def has_retry_task_permission(self, request, obj=None):
        return request.user.has_perm("core.change_syncstatus")


class FakeDoorLinkForm(forms.ModelForm):
    class Meta:
        model = FakeDoorLink
        fields = "__all__"

    def clean(self):
        cleaned_data = super().clean()

        location = cleaned_data.get("location")
        subtitle_en = cleaned_data.get("subtitle_en")
        subtitle_nb = cleaned_data.get("subtitle_nb")
        subtitle_es = cleaned_data.get("subtitle_es")
        tag_title = cleaned_data.get("tag_title")
        tag_color = cleaned_data.get("tag_color")

        if location == FakeDoorLink.Location.BANNER and not (subtitle_en or subtitle_nb or subtitle_es):
            self.add_error(None, "Subtitle must be specified when location is set to BANNER.")

        if tag_title and not tag_color:
            self.add_error(None, "Tag color must be specified when tag title is specified.")

        return cleaned_data


@admin.register(FakeDoorLink, site=unfold_admin_site)
class FakeDoorLinkAdmin(TranslationUnfoldModelAdmin):
    form = FakeDoorLinkForm

    formfield_override_by_name = {
        "countries": {"widget": UnfoldSelect2MultipleAdminWidget},
    }

    list_display = ["title", "tracking_id", "active", "tag", "created_at", "updated_at"]
    fieldsets = (
        ("Title", {"fields": ("title",)}),
        ("Subtitle", {"fields": ("subtitle",)}),
        ("Link URL", {"fields": ("link_url",)}),
        (
            "Options",
            {
                "fields": (
                    "tracking_id",
                    "image",
                    "display_image",
                    "location",
                    "tag_title",
                    "tag_color",
                    "show_notification",
                    "active",
                    "countries",
                ),
            },
        ),
    )
    search_fields = ["title", "tracking_id"]
    ordering = ["-created_at"]
    list_filter = ["location", "tracking_id", "active"]

    def tag(self, obj):
        if obj.tag_title and obj.tag_color:
            background_color = "#A41818"
            if obj.tag_color == "ORANGE":
                background_color = "#FF9C09"
            elif obj.tag_color == "GREEN":
                background_color = "#64A70B"
            return format_html(
                '<div style="background-color: {color}; padding: 4px; border-radius: 4px; width: fit-content;"> \
                    {title} \
                </div>',
                color=background_color,
                title=obj.tag_title,
            )
        return ""


class DueForDebtCollectionFilter(SimpleListFilter):
    parameter_name = "due_for_debt_collection"
    title = "Due for debt collection"

    def lookups(self, request, model_admin):
        return [
            ("yes", "Yes"),
            ("no", "No"),
        ]

    def queryset(self, request, queryset):
        if self.value() == "yes":
            return queryset.filter(debt_collection_due_date__lte=date.today() - timedelta(days=3))
        elif self.value() == "no":
            return queryset.filter(debt_collection_due_date__gt=date.today() - timedelta(days=3))


class DebtCollectionCreateCaseFailedFilter(SimpleListFilter):
    parameter_name = "has_create_case_error"
    title = "Create case failed"

    def lookups(self, request, model_admin):
        return [
            ("yes", "Yes"),
            ("no", "No"),
        ]

    def queryset(self, request, queryset):
        if self.value() == "yes":
            return queryset.filter(create_case_error__isnull=False)
        elif self.value() == "no":
            return queryset.filter(create_case_error__isnull=True)


@admin.register(DebtCollection, site=unfold_admin_site)
class DebtCollectionAdmin(SimpleHistoryAdmin, UnfoldModelAdmin):
    list_select_related = [
        "invoice_document__customer__nofence_company",
        "invoice_document__nofence_company",
        "email_record",
        "riverty_case",
    ]

    fields = [
        "status",
        "debt_collection_due_date",
        "invoice_document",
        "email_record",
        "create_case_error",
        "create_case_msg",
    ]
    readonly_fields = ["invoice_document", "email_record"]
    search_fields = [
        "invoice_document__doc_ref",
        "invoice_document__doc_id",
        "invoice_document__customer__name",
        "invoice_document__customer__visma_id",
    ]
    ordering = ["-created_at"]
    list_filter = [
        "status",
        AutocompleteFilterFactory("Customer", "invoice_document__customer"),
        "invoice_document__customer__customer_type",
        "invoice_document__customer__nofence_company",
        DueForDebtCollectionFilter,
        DebtCollectionCreateCaseFailedFilter,
        "create_case_error",
        ("debt_collection_due_date", RangeDateFilter),
    ]

    actions = [
        "create_rivery_case",
        "clear_case_error",
    ]

    def get_list_display(self, request):
        if request.GET.get("has_create_case_error", "") == "yes":
            return [
                "status",
                "customer",
                "nofence_company",
                "customer_type",
                "invoice_document_link",
                "total",
                "debt_due_date_colored",
                "formatted_create_case_error",
            ]

        return [
            "status",
            "customer",
            "nofence_company",
            "customer_type",
            "invoice_document_link",
            "total",
            "debt_due_date_colored",
            "days_overdue",
            "email_notification",
            "created_at",
        ]

    @admin.display(description="Create case error", ordering=["create_case_error"])
    def formatted_create_case_error(self, obj: DebtCollection):
        return format_html(
            """<p><span style="color: #000000;">{error}</span></p>
            <p><span style="display: inline-block; padding: 0.2rem 0;">{msg}</span></p>""",
            error=obj.get_create_case_error_display(),
            msg=obj.create_case_msg,
        )

    @admin.display(description="Customer")
    def customer(self, obj):
        url = reverse("admin:core_customer_change", args=(obj.invoice_document.customer.pk,))
        return format_html('<a href="{url}">{title}</a>', url=url, title=f"{obj.invoice_document.customer}")

    @admin.display(description="Nofence Company", ordering=["invoice_document__nofence_compnay"])
    def nofence_company(self, obj):
        url = reverse("admin:core_nofencecompany_change", args=(obj.invoice_document.nofence_company.pk,))
        return format_html('<a href="{url}">{title}</a>', url=url, title=f"{obj.invoice_document.nofence_company}")

    @admin.display(description="Customer Type")
    def customer_type(self, obj):
        return obj.invoice_document.customer.get_customer_type_display()

    @admin.display(description="Invoice document")
    def invoice_document_link(self, obj: DebtCollection):
        url = reverse(
            "admin:core_invoicedocument_change",
            args=(obj.invoice_document.pk,),
        )
        inv: InvoiceDocument = obj.invoice_document

        title = f"{inv.reference_number} - {inv.get_status_display()}"
        if obj.has_riverty_case():
            title += " (Riverty)"

        return format_html(
            '<a href="{url}">{title}</a>',
            url=url,
            title=title,
        )

    @admin.display(description="Total")
    def total(self, obj):
        return obj.invoice_document.total

    @admin.display(description="Email record")
    def email_notification(self, obj):
        url = reverse("admin:core_emailrecord_change", args=(obj.email_record.pk,))
        return format_html(
            '<a href="{url}">{title}</a>',
            url=url,
            title=f"{obj.email_record.status}",
        )

    @admin.display(description="Debt due date")
    def debt_due_date_colored(self, obj: DebtCollection):
        due_date = obj.debt_collection_due_date
        if obj.is_active():
            if due_date <= date.today():
                return format_html(
                    '<span style="color: #ff0000;">{}</span>',
                    date_format(due_date),
                )
            elif due_date - timedelta(days=4) <= date.today():
                return format_html(
                    '<span style="color: #e58500">{}</span>',
                    date_format(due_date),
                )
        return due_date

    @admin.display(description="Days overdue")
    def days_overdue(self, obj: DebtCollection):
        if obj.is_active():
            return obj.invoice_document.days_overdue
        return ""

    @admin.action(description="Create Riverty Case", permissions=["create_riverty_case"])
    def create_rivery_case(self, request, queryset: QuerySet[DebtCollection]):
        service = create_riverty_service()
        count = 0
        skipped = 0
        for debt_collection in queryset:
            if debt_collection.status == DebtCollection.Status.WARNING_SENT_TO_CUSTOMER:
                invoice: InvoiceDocument = debt_collection.invoice_document
                customer: Customer = debt_collection.invoice_document.customer

                if invoice.nofence_company != NOFENCE_NO:
                    self.message_user(
                        request,
                        (f"Failed to create case for {invoice.reference_number}: Invoice not under Nofence AS."),
                        messages.ERROR,
                    )
                elif not customer.is_business_customer():
                    self.message_user(
                        request,
                        (f"Failed to create case for {invoice.reference_number}: Not a business customer."),
                        messages.ERROR,
                    )
                elif not customer.business_reg_no:
                    self.message_user(
                        request,
                        (f"Failed to create case for {invoice.reference_number}: Customer missing business reg no."),
                        messages.ERROR,
                    )
                else:
                    try:
                        service.create_riverty_case(debt_collection)
                        count += 1
                    except RivertyServiceError as e:
                        self.message_user(
                            request,
                            (f"Failed to create case for {debt_collection.invoice_document.reference_number}: {e}"),
                            messages.ERROR,
                        )
                        if e.error_code:
                            debt_collection.set_create_case_error(e.error_code, str(e))
            else:
                skipped += 1

        if count:
            url = reverse("admin:core_rivertycase_changelist")
            self.message_user(
                request,
                format_html(
                    f"Created {count} riverty case(s) ready to be sent to rivery. <a href='{{url}}'>Go to cases</a>",
                    count=count,
                    url=url,
                ),
                messages.SUCCESS,
            )
        if skipped:
            valid_status = DebtCollection.Status.WARNING_SENT_TO_CUSTOMER.label
            self.message_user(
                request,
                f"Skipped {skipped} deb collections with wrong status. Only {valid_status} valid.",
                messages.WARNING,
            )

    def has_create_riverty_case_permission(self, request, obj=None):
        return request.user.has_perm("core.add_rivertycase")

    @admin.action(description="Clear Create Case Error", permissions=["clear_case_error"])
    def clear_case_error(self, request, queryset: QuerySet[DebtCollection]):
        count = 0
        for debt_collection in queryset:
            debt_collection._change_reason = "Clear error from admin"
            debt_collection.clear_create_case_error()
            count += 1

        self.message_user(
            request,
            f"Cleared error on {count} cases.",
            messages.SUCCESS,
        )

    def has_clear_case_error_permission(self, request, obj=None):
        return request.user.has_perm("core.change_rivertycase")


class DirectDebitEventInline(ImmutableMixin, StackedInlineBase):
    model = DirectDebitEvent

    fields = (
        "source",
        "event_created_at",
        "pretty_data",
    )
    readonly_fields = ("pretty_data",)

    ordering = ("-event_created_at",)

    classes = ["collapse"]

    @admin.display(description="Data")
    def pretty_data(self, obj: DirectDebitEvent):
        if not obj.data:
            return "-"

        if not isinstance(obj.data, dict):
            return obj.data

        content = json.dumps(obj.data, indent=2, sort_keys=True)
        return format_html("<pre>{content}</pre>", content=content)


class DirectDebitPaymentInline(ImmutableMixin, TabularInlineBase):
    model = DirectDebitPayment

    fields = (
        "status",
        "invoice_document",
        "get_amount",
        "payment_status",
        "payment_id",
    )

    readonly_fields = ["get_amount"]

    ordering = ("-id",)

    classes = ["collapse"]

    @admin.display(description="Amount")
    def get_amount(self, obj: DirectDebitPayment):
        if obj.currency and obj.amount:
            return obj.get_amount_display
        return "-"


class DirectDebitPayoutInline(ImmutableMixin, TabularInlineBase):
    model = DirectDebitPayout

    fields = (
        "status",
        "reference",
        "get_amount",
    )

    readonly_fields = ["get_amount"]

    ordering = ("-id",)

    @admin.display(description="Amount")
    def get_amount(self, obj: DirectDebitPayment):
        if obj.currency and obj.amount:
            return obj.get_amount_display
        return "-"


@admin.register(DirectDebitMandate, site=unfold_admin_site)
class DirectDebitMandateAdmin(SimpleHistoryAdmin, UnfoldModelAdmin):
    list_display = ["customer", "status", "created_at"]

    exclude = ["data"]
    history_unified_diff = ["data"]

    readonly_fields = [
        "mandate_id",
        "mandate_status",
        "bank_account_id",
        "billing_request_id",
        "billing_request_status",
        "billing_request_flow_id",
        "billing_request_flow_url",
        "go_cardless_customer_id",
        "next_possible_charge_date",
        "created_at",
        "updated_at",
    ]

    search_fields = ["mandate_id"]

    autocomplete_fields = [
        "customer",
    ]

    list_filter = [
        "status",
        "nofence_company",
        AutocompleteFilterFactory("Customer", "customer"),
        "mandate_status",
        "billing_request_status",
    ]

    inlines = [
        DirectDebitPaymentInline,
        DirectDebitEventInline,
    ]


@admin.register(DirectDebitPayment, site=unfold_admin_site)
class DirectDebitPaymentAdmin(SimpleHistoryAdmin, UnfoldModelAdmin):
    list_display = ["status", "get_amount", "charge_date", "payment_status", "invoice_link", "customer_link"]

    list_select_related = [
        "invoice_document__nofence_company",
        "invoice_document__customer__user",
        "customer__user",
    ]

    list_filter = [
        "status",
        AutocompleteFilterFactory("Customer", "customer"),
        AutocompleteFilterFactory("Invoice Document", "invoice_document"),
        "payment_status",
    ]

    exclude = ["data"]
    history_unified_diff = ["data"]

    search_fields = ["payment_id"]

    readonly_fields = [
        "mandate",
        "customer",
        "invoice_document",
        "payment_id",
        "payment_status",
        "amount",
        "currency",
        "charge_date",
        "created_at",
        "updated_at",
    ]

    inlines = [
        DirectDebitPayoutInline,
        DirectDebitEventInline,
    ]

    autocomplete_fields = [
        "customer",
        "invoice_document",
        "mandate",
    ]

    actions = [
        "retry_payment",
        "create_payout_csv",
    ]

    @admin.display(description="Amount")
    def get_amount(self, obj: DirectDebitPayment):
        if obj.currency and obj.amount:
            return obj.get_amount_display
        return "-"

    @admin.display(description="Customer")
    def customer_link(self, obj):
        url = reverse("admin:core_customer_change", args=(obj.customer.pk,))
        return format_html("<a href='{}'>{}</a>", url, obj.customer)

    @admin.display(description="Invoice")
    def invoice_link(self, obj):
        url = reverse("admin:core_invoicedocument_change", args=(obj.invoice_document.pk,))
        return format_html("<a href='{}'>{}</a>", url, obj.invoice_document)

    @admin.action(description="Retry payment")
    def retry_payment(self, request, queryset):
        for payment in queryset:
            retry_direct_debit_payment.delay(payment.pk)
        self.message_user(request, f"Retrying {len(queryset)} payments in gocardless..", messages.SUCCESS)

    @admin.action(description="Create Payout CSV")
    def create_payout_csv(self, request, queryset):
        service = create_direct_debit_payout_notifier()

        payouts = DirectDebitPayout.objects.filter(
            payment__in=queryset.filter(payment_status="paid_out"),
        )
        csv, _ = service.generate_csv(payouts)
        response = StreamingHttpResponse(csv, content_type="text/csv")
        response["Content-Disposition"] = "attachment; filename=payouts.csv"

        return response


class HubSpotPipelineStageModelInline(TabularInlineBase):
    model = HubSpotPipelineStageModel

    fields = [
        "hubspot_id",
        "label",
        "metadata",
    ]

    ordering = ["display_order", "id"]

    max_num = 0
    can_delete = False

    readonly_fields = [
        "hubspot_id",
        "label",
        "metadata",
    ]

    def get_queryset(self, request: HttpRequest):
        return super().get_queryset(request).select_related("pipeline")


@admin.register(HubSpotPipelineModel, site=unfold_admin_site)
class HubSpotPipelineModelAdmin(SimpleHistoryAdmin, UnfoldModelAdmin):
    list_display = [
        "label",
        "hubspot_id",
        "object_type",
    ]

    search_fields = ["label"]

    inlines = [
        HubSpotPipelineStageModelInline,
    ]

    exclude = ["display_order"]

    ordering = ["display_order"]

    readonly_fields = [
        "hubspot_id",
        "object_type",
        "label",
        "archived",
        "pipeline_created_at",
        "pipeline_updated_at",
    ]

    list_filter = [
        "object_type",
        "is_web_shop_pipeline",
    ]

    fieldsets = (
        (
            None,
            {
                "fields": (
                    "hubspot_id",
                    "object_type",
                    "label",
                    "pre_paid",
                    "is_web_shop_pipeline",
                    "archived",
                    "pipeline_created_at",
                    "pipeline_updated_at",
                ),
            },
        ),
    )

    def get_queryset(self, request: HttpRequest):
        qs = super().get_queryset(request)
        if request.GET.get("model_name", None) == "salesorder":
            qs = qs.filter(object_type="deal")
        return qs.order_by("display_order")


@admin.register(HubSpotPipelineStageModel, site=unfold_admin_site)
class HubSpotPipelineStageModelAdmin(SimpleHistoryAdmin, UnfoldModelAdmin):
    list_display = [
        "label",
        "hubspot_id",
    ]

    search_fields = ["label", "pipeline__label"]

    def get_queryset(self, request: HttpRequest):
        qs = super().get_queryset(request).select_related("pipeline")
        if request.GET.get("model_name", None) == "salesorder":
            qs = qs.filter(pipeline__object_type="deal")
        return qs.order_by("pipeline__display_order", "display_order")

    def has_change_permission(self, *args, **kwargs):
        return False

    def has_delete_permission(self, *args, **kwargs):
        return False

    def has_add_permission(self, *args, **kwargs):
        return False


@admin.register(NofenceLearningResource, site=unfold_admin_site)
class LearnAboutNofenceAdmin(TranslationUnfoldModelAdmin):
    list_display = [
        "title",
        "description",
        "video_url",
        "order",
    ]
    search_fields = ["title"]
    fieldsets = (
        (
            None,
            {
                "fields": (
                    "title",
                    "description",
                    "video_url",
                    "order",
                ),
            },
        ),
    )


class SupplierAddressInline(TabularInlineBase):
    model = SupplierAddress


class SupplierBillingAddressInline(TabularInlineBase):
    model = SupplierBillingAddress


class SupplierProductsInline(TabularInlineBase):
    model = SupplierProduct
    autocomplete_fields = ("product",)


@admin.register(Supplier, site=unfold_admin_site)
class SupplierAdmin(SimpleHistoryAdmin, UnfoldModelAdmin):
    list_display = ["supplier_number", "name", "type", "created_at", "updated_at", "segmentation"]

    readonly_fields = [
        "supplier_number",
        "created_at",
        "updated_at",
    ]
    search_fields = ["supplier_number"]
    fieldsets = (
        (
            None,
            {
                "fields": (
                    "supplier_number",
                    "code",
                    "name",
                    "type",
                    "business_registration_number",
                    "currency",
                    "payment_terms",
                    "delivery_terms",
                    "dhl_account_number",
                    "created_at",
                    "updated_at",
                    "contract",
                    "segmentation",
                ),
            },
        ),
        (
            "Contact person",
            {
                "fields": (
                    "contact_person_name",
                    "contact_person_email",
                    "contact_person_phone_number",
                ),
            },
        ),
        (
            "Billing",
            {
                "fields": ("billing_email",),
            },
        ),
    )

    inlines = [
        SupplierAddressInline,
        SupplierBillingAddressInline,
        SupplierProductsInline,
    ]

    def get_readonly_fields(self, request, obj=None):
        if obj is None:
            return [
                "supplier_number",
                "created_at",
                "updated_at",
            ]
        return super().get_readonly_fields(request, obj=obj)


class PurchaseOrderDeliveryAddressInline(TabularInlineBase):
    model = PurchaseOrderDeliveryAddress


class PurchaseOrderLineInline(TabularInlineBase):
    model = PurchaseOrderLine
    autocomplete_fields = ["product"]
    min_num = 1


class PurchaseOrderFileInline(TabularInlineBase):
    model = PurchaseOrderFile
    fields = ("download_file", "type", "created_at")
    readonly_fields = [
        "download_file",
        "type",
        "created_at",
    ]

    @admin.display(description="File")
    def download_file(self, obj):
        url = obj.file.url
        name = obj.file.name
        return format_html(
            '<a target="_blank" href="{url}">{name}</a>',
            url=url,
            name=name,
        )


class PurchaseOrderReceivedQuantitiesInline(ImmutableMixin, TabularInlineBase):
    """Shows a custom table with information about received quantities.

    An inline is used to get better control over the template and look. The context
    used in the template is passed to the template in the changeform_view in the
    purchase order admin.
    """

    model = PurchaseReceipt
    template = "admin/inlines/purchase-order-received-quantities.html"
    verbose_name = "Received Quantity"
    verbose_name_plural = "Received Quantities"
    fields = []


class PurchaseInvoiceInline(ImmutableMixin, TabularInlineBase):
    model = PurchaseInvoice
    fields = ["invoice_reference", "file"]
    show_change_link = True


@admin.register(PurchaseOrder, site=unfold_admin_site)
class PurchaseOrderAdmin(SimpleHistoryAdmin, UnfoldModelAdmin):
    list_display = [
        "order_number",
        "supplier",
        "warehouse",
        "status_colored",
        "quantity_status",
        "total",
        "issue_date",
        "requested_by_date",
        "pdf",
        "summary",
    ]

    ordering = ["-created_at"]

    list_filter = [
        "supplier",
        "warehouse",
        "status",
    ]

    search_fields = ["order_number", "our_reference"]

    readonly_fields = [
        # "order_number", TODO: Make read only again once transition period from visma is over.
        "status",
        "created_at",
        "updated_at",
    ]

    fields = [
        "order_number",
        "status",
        "supplier",
        "warehouse",
        "our_reference",
        "supplier_reference",
        "requested_by",
        "payment_terms",
        "delivery_terms",
        "issue_date",
        "requested_by_date",
        "confirmed_on_date",
        "description",
        "created_at",
        "updated_at",
    ]

    actions = [
        "create_or_update_draft_pdf",
        "confirm_purchase_order",
        "send_purchase_order_to_supplier",
        "park_purchase_order",
        "unpark_purchase_order",
    ]

    actions_detail = [
        "create_or_update_draft_pdf",
        "confirm_purchase_order",
        "send_purchase_order_to_supplier",
        "park_purchase_order",
        "unpark_purchase_order",
    ]

    def get_queryset(self, request):
        return (
            super()
            .get_queryset(request)
            .prefetch_related(
                "lines__product",
                "purchase_receipts__lines",
            )
        )

    def get_inlines(self, request, obj=None):
        inlines = [
            PurchaseOrderDeliveryAddressInline,
            PurchaseOrderLineInline,
            PurchaseOrderFileInline,
        ]
        if obj is not None:
            inlines.append(PurchaseOrderReceivedQuantitiesInline)
            inlines.append(PurchaseInvoiceInline)

        return inlines

    @admin.display(description="Status")
    def status_colored(self, obj: PurchaseOrder):
        if obj.status == PurchaseOrder.Status.OVERDUE:
            return format_html(
                '<span style="color: #ff0000;">{}</span>',
                obj.status,
            )
        if obj.status == PurchaseOrder.Status.CLOSED:
            return format_html(
                '<span style="color: #64A70B;">{}</span>',
                obj.status,
            )
        return obj.status

    @admin.display(description="Received/Ordered")
    def quantity_status(self, obj: PurchaseOrder):
        lines = obj.lines.all()
        quantity_ordered = 0
        for line in lines:
            quantity_ordered += line.quantity

        purchase_receipts = obj.purchase_receipts.all()
        quantity_received = 0
        for receipt in purchase_receipts:
            for line in receipt.lines.all():
                quantity_received += line.received_quantity

        if quantity_received > quantity_ordered:
            return format_html(
                '<span style="color: #e58500;">{} / {}</span>',
                quantity_received,
                quantity_ordered,
            )
        return format_html(
            "<span>{} / {}</span>",
            quantity_received,
            quantity_ordered,
        )

    @admin.display(description="PDF")
    def pdf(self, obj):
        if obj.status == PurchaseOrder.Status.DRAFT:
            name = "Draft"
            file = PurchaseOrderFile.objects.filter(purchase_order=obj, type=PurchaseOrderFile.Type.DRAFT).first()
        elif obj.status != PurchaseOrder.Status.DRAFT:
            name = "PDF"
            file = PurchaseOrderFile.objects.filter(purchase_order=obj, type=PurchaseOrderFile.Type.ISSUED).first()

        if not file:
            return "-"
        return format_html(
            '<a target="_blank" href="{url}">{name}</a>',
            url=file.file.url,
            name=name,
        )

    def get_readonly_fields(self, request, obj=None):
        if obj and obj.status != PurchaseOrder.Status.DRAFT:
            return [
                "issue_date",
                "requested_by_date",
                # "order_number", TODO: Make read only again once transition period from visma is over.
                "status",
                "created_at",
                "updated_at",
            ]
        return super().get_readonly_fields(request, obj=obj)

    def get_actions_detail(self, request, object_id=None):
        actions = []
        obj = self.get_object(request, object_id)

        if obj.status == PurchaseOrder.Status.ISSUED:
            actions = ["confirm_purchase_order", "send_purchase_order_to_supplier"]
        if obj.status == PurchaseOrder.Status.DRAFT:
            actions = ["create_or_update_draft_pdf", "send_purchase_order_to_supplier", "park_purchase_order"]
        if obj.status == PurchaseOrder.Status.PARKED:
            actions = ["unpark_purchase_order"]

        return self._filter_unfold_actions_by_permissions(
            request,
            [self.get_unfold_action(action) for action in actions],
            object_id,
        )

    @unfold_action(description="Create or update draft PDF", attrs={"use_post_request": True})
    def create_or_update_draft_pdf(self, request, queryset=None, object_id=None):
        count = 0

        if object_id:
            queryset = self.get_queryset(request).filter(id=object_id)

        for po in queryset.all():
            if po.status == PurchaseOrder.Status.DRAFT:
                po.create_or_update_draft_pdf()
                count += 1

        self.message_user(
            request,
            f"Created or updated {count} draft PDFs",
            messages.SUCCESS,
        )
        # redirect to the purchase order list page
        return redirect(reverse("admin:core_purchaseorder_changelist"))

    @unfold_action(description="Park", attrs={"use_post_request": True})
    def park_purchase_order(self, request, queryset=None, object_id=None):
        if object_id:
            queryset = self.get_queryset(request).filter(id=object_id)

        for po in queryset.all():
            if po.status == PurchaseOrder.Status.DRAFT:
                po.status = PurchaseOrder.Status.PARKED
                po.save()

        self.message_user(request, "Parked purchase order", messages.SUCCESS)
        return redirect(reverse("admin:core_purchaseorder_changelist"))

    @unfold_action(description="Unpark", attrs={"use_post_request": True})
    def unpark_purchase_order(self, request, queryset=None, object_id=None):
        if object_id:
            queryset = self.get_queryset(request).filter(id=object_id)

        for po in queryset.all():
            if po.status == PurchaseOrder.Status.PARKED:
                po.status = PurchaseOrder.Status.DRAFT
                po.save()
        self.message_user(request, "Unparked purchase order", messages.SUCCESS)
        return redirect(reverse("admin:core_purchaseorder_changelist"))

    @unfold_action(description="Send or resend PO to supplier", attrs={"use_post_request": True})
    def send_purchase_order_to_supplier(self, request, queryset=None, object_id=None):
        count = 0
        if object_id:
            queryset = self.get_queryset(request).filter(id=object_id)

        for po in queryset.all():
            if po.issue_date <= date.today():
                # Send email and pdf on purchase order to supplier
                email_record = create_email_service().send_purchase_order_to_supplier(po)
                if email_record:
                    count += 1
                    if po.status == PurchaseOrder.Status.DRAFT:
                        po.status = PurchaseOrder.Status.ISSUED
                        po.save()
                    logger.info(f"Sent out email for PO {po} to supplier from admin")
                else:
                    logger.error(f"Failed to send out email to supplier for purchase order {po} from admin")

        if count > 0:
            self.message_user(
                request,
                f"Sent {count} purchase orders to supplier",
                messages.SUCCESS,
            )
        else:
            self.message_user(
                request,
                "Could not send email. Check that issue date is today or back in time and that the PO looks correct.",
                messages.ERROR,
            )
        return redirect(reverse("admin:core_purchaseorder_changelist"))

    @unfold_action(description="Confirm Purchase Order", attrs={"use_post_request": True})
    def confirm_purchase_order(self, request, queryset=None, object_id=None):
        if object_id:
            queryset = self.get_queryset(request).filter(id=object_id)

        purchase_orders = queryset.all()
        if len(purchase_orders) > 1:
            self.message_user(
                request,
                "Only one purchase order can be confirmed at a time",
                messages.ERROR,
            )
            return redirect(request.get_full_path())

        purchase_order: PurchaseOrder = purchase_orders[0]

        if purchase_order.status != PurchaseOrder.Status.ISSUED:
            self.message_user(
                request,
                "Purchase order is not ISSUED",
                messages.ERROR,
            )
            return redirect(request.get_full_path())

        purchase_order.status = PurchaseOrder.Status.CONFIRMED
        purchase_order.save()
        self.message_user(request, "Confirmed purchase order", messages.SUCCESS)
        return redirect(reverse("admin:core_purchaseorder_changelist"))

    def changeform_view(self, request, object_id=None, form_url="", extra_context=None):
        if object_id is not None:
            # Add some extra context used by the received quantities inline
            obj = self.get_object(request, object_id)
            extra_context = extra_context or {}
            extra_context.update(self.get_received_quantities_context(request, obj))
        return super().changeform_view(request, object_id, form_url, extra_context)

    def get_received_quantities_context(self, request, obj: PurchaseOrder):
        def group_by_product(it):
            return it.product

        ordered_by_product = {}
        order_lines = obj.lines.order_by("product").select_related("product")
        for k, g in itertools.groupby(order_lines.all(), key=group_by_product):
            ordered_by_product[k] = list(g)

        # At the moment there is nothing stopping someone from registering receipt for
        # products that was not ordered. So we also find all products from receipts so
        # that any extra products will show up in the table. But normally the received
        # products should be the same as the ordered products.
        received_by_product = {}
        receipt_lines = (
            PurchaseReceiptLine.objects.filter(
                purchase_receipt__status=PurchaseReceipt.Status.CONFIRMED,
                purchase_receipt__purchase_order=obj,
            )
            .order_by("product")
            .select_related("purchase_receipt", "product")
        )
        for k, g in itertools.groupby(receipt_lines.all(), key=group_by_product):
            received_by_product[k] = list(g)

        quantities = []
        all_products = ordered_by_product.keys() | received_by_product.keys()
        for product in all_products:
            quantities.append(
                {
                    "product": product,
                    "total_ordered": sum(line.quantity for line in ordered_by_product.get(product, [])),
                    "total_received": sum(line.received_quantity for line in received_by_product.get(product, [])),
                    "receipt_lines": received_by_product.get(product, []),
                },
            )
        return {"received_quantities": quantities}

    def summary(self, obj):
        summaries = []
        for line in obj.lines.all():
            summaries.append(str(line.product))

        return ", ".join(summaries)


class GoodsReceiptLineInline(TabularInlineBase):
    model = GoodsReceiptLine

    fields = [
        "sku",
        "product",
        "expected_quantity",
        "received_quantity",
    ]

    readonly_fields = [
        "get_deviation",
    ]

    ordering = ["id"]

    autocomplete_fields = ["product"]

    def has_change_permission(self, request: HttpRequest, obj: GoodsReceipt = None) -> bool:
        if obj is not None and obj.is_confirmed():
            return False
        return super().has_change_permission(request, obj)

    def has_delete_permission(self, request: HttpRequest, obj: GoodsReceipt = None) -> bool:
        if obj is not None and obj.is_confirmed():
            return False
        return super().has_delete_permission(request, obj)

    @admin.display(description="Deviation")
    def get_deviation(self, obj: GoodsReceiptLine):
        if obj.id is not None:
            return obj.get_deviation()

    def get_max_num(self, request: HttpRequest, obj: GoodsReceipt, **kwargs):
        if obj is not None and obj.is_confirmed():
            return 0
        return super().get_max_num(request, obj, **kwargs)

    def get_fields(self, request: HttpRequest, obj: GoodsReceipt):
        if obj is not None and obj.is_confirmed():
            return [
                "sku",
                "product",
                "expected_quantity",
                "received_quantity",
                "get_deviation",
            ]

        return [
            "sku",
            "product",
            "expected_quantity",
            "received_quantity",
        ]


@admin.register(GoodsReceipt, site=unfold_admin_site)
class GoodsReceiptAdmin(SimpleHistoryAdmin, UnfoldModelAdmin):
    list_display = [
        "status",
        "warehouse",
        "get_object_link",
        "created_at",
    ]

    exclude = ["type"]

    list_filter = [
        "type",
        "status",
        "warehouse",
        "warehouse__nofence_company",
        AutocompleteFilterFactory("Shipment", "shipment"),
    ]

    list_select_related = ["shipment", "warehouse"]

    search_fields = ["shipment__shipment_no"]

    autocomplete_fields = ["shipment"]

    ordering = ["-id"]

    inlines = [
        GoodsReceiptLineInline,
    ]

    actions = [
        "confirm_shipment_receipt",
    ]

    actions_detail = [
        "confirm_shipment_receipt",
    ]

    @admin.display(description="Shipment")
    def get_object_link(self, obj: GoodsReceipt):
        if obj.type == GoodsReceipt.Type.INTERNAL_SHIPMENT and obj.shipment is not None:
            url = reverse("admin:core_shipment_change", args=(obj.shipment.pk,))
            return format_html("<a href='{}'>{}</a>", url, obj.shipment)
        return "-"

    def get_actions_detail(self, request, object_id=None):
        actions = []
        obj = self.get_object(request, object_id)
        if not obj.is_confirmed():
            actions = ["confirm_shipment_receipt"]
        return self._filter_unfold_actions_by_permissions(
            request,
            [self.get_unfold_action(action) for action in actions],
            object_id,
        )

    @unfold_action(
        description="Confirm Arrival",
        permissions=["confirm_shipment_receipt"],
    )
    @transaction.atomic
    def confirm_shipment_receipt(self, request, queryset=None, object_id=None):
        if object_id:
            queryset = self.get_queryset(request).filter(id=object_id)

        receipts = queryset.all()
        if len(receipts) > 1:
            self.message_user(
                request,
                "Only one receipt can be confirmed at a time",
                messages.ERROR,
            )
            return redirect(request.get_full_path())

        receipt: GoodsReceipt = receipts[0]

        if receipt.is_confirmed():
            self.message_user(
                request,
                f"Receipt {receipt} already confirmed",
                messages.ERROR,
            )
            return redirect(request.get_full_path())

        template = "admin/confirm_shipment_receipt.html"

        if "cancel" in request.POST:
            return redirect(request.get_full_path())
        elif "confirm" in request.POST:
            form = ConfirmShipmentReceiptForm(request.POST)
            if not receipt.is_confirmed():
                try:
                    create_goods_receipt_service().confirm_receipt(receipt)
                    self.message_user(
                        request,
                        f"Shipment Receipt {receipt} Confirmed",
                    )
                    return redirect(request.get_full_path())
                except GoodsReceiptException as e:
                    form.add_error(None, str(e))
            else:
                self.message_user(
                    request,
                    f"Shipment Receipt {receipt} already confirmed",
                    messages.WARNING,
                )
                return redirect(request.get_full_path())
        else:
            form = ConfirmShipmentReceiptForm()

        opts = self.model._meta

        context = {
            **self.admin_site.each_context(request),
            "title": "Confirm Receipt",
            "subtitle": "",
            "form": form,
            "opts": opts,
            "app_label": opts.app_label,
            "receipt": receipt,
        }

        return render(request, template, context=context)

    def has_confirm_shipment_receipt_permission(self, request, obj=None):
        return request.user.has_perm("core.change_goodsreceipt")


class PurchaseReceiptLineInline(TabularInlineBase):
    model = PurchaseReceiptLine

    fields = ["product", "expected_quantity", "received_quantity"]

    autocomplete_fields = ["product"]


class PurchaseReceiptFileInline(TabularInlineBase):
    model = PurchaseReceiptFile

    fields = ["file"]


class PurchaseReceiptInvoiceLineInline(ImmutableMixin, TabularInlineBase):
    model = PurchaseInvoiceLine
    fields = ["product", "quantity", "unit_price", "purchase_invoice"]
    autocomplete_fields = ["product"]
    show_change_link = True


@admin.register(PurchaseReceipt, site=unfold_admin_site)
class PurchaseReceiptAdmin(InlineActionsMixin, SimpleHistoryAdmin, UnfoldModelAdmin):
    list_display = [
        "receipt_reference",
        "status",
        "warehouse",
        "purchase_order",
        "created_at",
        "summary",
    ]

    autocomplete_fields = [
        "purchase_order",
    ]

    search_fields = ["receipt_reference", "purchase_order__order_number"]

    list_filter = [
        "status",
        "warehouse",
        AutocompleteFilterFactory("Purchase Order", "purchase_order"),
        "lines__product",
    ]

    readonly_fields = [
        "status",
    ]

    inlines = [
        PurchaseReceiptLineInline,
        PurchaseReceiptFileInline,
        PurchaseReceiptInvoiceLineInline,
    ]

    actions = [
        "confirm_purchase_receipt",
        "refresh_inission_csv",
    ]

    actions_detail = [
        "confirm_purchase_receipt",
        "refresh_inission_csv",
    ]

    actions_list = ["cost_export", "add_inission_receipt"]

    @unfold_action(description="Cost Export", permissions=["view_purchasereceipt"])
    def cost_export(self, request, queryset=None):
        return redirect(reverse("admin-purchase-receipt-cost-export"))

    @unfold_action(description="Add Inission Receipt", permissions=["add_purchasereceipt"])
    def add_inission_receipt(self, request, queryset=None):
        return redirect(reverse("admin:core_purchasereceipt_add") + "?" + urlencode({"supplier_code": INISSION}))

    def summary(self, obj=None):
        if obj is None:
            return ""

        summaries = []
        correct_quantity = True
        for line in obj.lines.all():
            summaries.append(f"{line.product}: {line.received_quantity} / {line.expected_quantity} ")
            if line.expected_quantity != line.received_quantity:
                correct_quantity = False

        return ("✅" if correct_quantity else "❌") + " " + ", ".join(summaries)

    def has_view_purchasereceipt_permission(self, request, obj=None):
        return request.user.has_perm("core.view_purchasereceipt")

    def has_add_purchasereceipt_permission(self, request, obj=None):
        return request.user.has_perm("core.add_purchasereceipt")

    def get_actions_detail(self, request, obj_id=None):
        actions = []
        obj: PurchaseReceipt = self.get_object(request, obj_id)
        if not obj.is_confirmed():
            actions.append("confirm_purchase_receipt")
        if obj.purchase_order.supplier.code == INISSION and obj.is_confirmed():
            actions.append("refresh_inission_csv")
        return self._filter_unfold_actions_by_permissions(
            request,
            [self.get_unfold_action(action) for action in actions],
            obj_id,
        )

    @unfold_action(
        description="Confirm Receipt",
        permissions=["confirm_purchase_receipt"],
    )
    @transaction.atomic
    def confirm_purchase_receipt(self, request, queryset=None, object_id=None):
        if object_id:
            queryset = self.get_queryset(request).filter(id=object_id)

        receipts = queryset.all()
        if len(receipts) > 1:
            self.message_user(
                request,
                "Only one receipt can be confirmed at a time",
                messages.ERROR,
            )
            return redirect(request.get_full_path())

        receipt: PurchaseReceipt = receipts[0]

        logger.info(f"Confirming receipt {receipt}")

        if receipt.is_confirmed():
            self.message_user(
                request,
                f"Receipt {receipt} already confirmed",
                messages.ERROR,
            )
            return redirect(request.get_full_path())

        template = "admin/confirm_purchase_receipt.html"

        if "cancel" in request.POST:
            return redirect(request.get_full_path())
        elif "confirm" in request.POST:
            form = ConfirmPurchaseReceiptForm(request.POST)
            if not receipt.is_confirmed():
                try:
                    create_purchase_receipt_service().confirm_receipt(receipt)
                    self.message_user(
                        request,
                        f"Purchase Receipt {receipt} Confirmed",
                    )
                    return redirect(request.get_full_path())
                except PurchaseReceiptException as e:
                    logger.exception(f"Failed to confirm receipt {receipt}: {e}")
                    form.add_error(None, str(e))
            else:
                self.message_user(
                    request,
                    f"Purchase Receipt {receipt} already confirmed",
                    messages.WARNING,
                )
                return redirect(request.get_full_path())
        else:
            form = ConfirmPurchaseReceiptForm()

        opts = self.model._meta

        context = {
            **self.admin_site.each_context(request),
            "title": "Confirm Receipt",
            "subtitle": "",
            "form": form,
            "opts": opts,
            "app_label": opts.app_label,
            "receipt": receipt,
        }

        return render(request, template, context=context)

    def has_confirm_purchase_receipt_permission(self, request, obj=None):
        return request.user.has_perm("core.change_purchasereceipt")

    @unfold_action(
        description="Refresh Inission CSV",
        permissions=["refresh_inission_csv"],
    )
    def refresh_inission_csv(self, request, queryset=None, object_id=None):
        if object_id:
            queryset = self.get_queryset(request).filter(id=object_id)

        receipts = queryset.all()
        if len(receipts) > 1:
            self.message_user(
                request,
                "Only one receipt can be refreshed at a time",
                messages.ERROR,
            )
            return redirect(request.get_full_path())

        receipt: PurchaseReceipt = receipts[0]

        if receipt.purchase_order.supplier.code != INISSION:
            self.message_user(
                request,
                "Can only refresh CSV from Inission suppliers",
                messages.ERROR,
            )
            return redirect(self.get_redirect_url_for_inline_action(request, receipt.pk))

        if not receipt.is_confirmed():
            self.message_user(
                request,
                "Can only refresh CSV for confirmed receipts. For DRAFT receipt, just delete the receipt "
                "and upload again.",
                messages.ERROR,
            )
            return redirect(self.get_redirect_url_for_inline_action(request, receipt.pk))

        template = "admin/actions/refresh_purchase_receipt_inission.html"

        if "cancel" in request.POST:
            return redirect(request.get_full_path())
        elif "refresh" in request.POST:
            form = PurchaseReceiptInissionRefreshForm(
                request.POST,
                request.FILES,
                instance=receipt,
            )
            if form.is_valid():
                form.save()
                self.message_user(
                    request,
                    f"Purchase Receipt {receipt} Updated",
                )
                return redirect(self.get_redirect_url_for_inline_action(request, receipt.pk))
        else:
            form = PurchaseReceiptInissionRefreshForm(instance=receipt)

        opts = self.model._meta

        context = {
            **self.admin_site.each_context(request),
            "title": "Refresh Inission CSV",
            "subtitle": "",
            "form": form,
            "opts": opts,
            "app_label": opts.app_label,
            "selected_objects": [receipt],
            "is_file_form": True,
            **self.get_inline_action_context(request),
        }

        return render(request, template, context=context)

    def has_refresh_inission_csv_permission(self, request, obj=None):
        return request.user.has_perm("core.change_purchasereceipt")

    def get_form(self, request, obj: PurchaseReceipt = None, **kwargs):
        supplier_code = self._get_selected_supplier_code(request)
        if supplier_code == INISSION:
            return PurchaseReceiptInissionAddForm
        return super().get_form(request, obj, **kwargs)

    def get_inlines(self, request, obj=None):
        if obj is None:
            supplier_code = self._get_selected_supplier_code(request)
            if supplier_code is not None:
                # Hide inlines when creating a new user using custom form
                return []
        return super().get_inlines(request, obj=obj)

    def get_fieldsets(self, request, obj=None):
        if obj is None:
            supplier_code = self._get_selected_supplier_code(request)
            if supplier_code == INISSION:
                return (
                    (
                        f"Purchase Receipt from {supplier_code}",
                        {
                            "fields": ["packing_slip_csv"],
                        },
                    ),
                )

        return super().get_fieldsets(request, obj)

    def _get_selected_supplier_code(self, request) -> Optional[Supplier]:
        return request.GET.get("supplier_code", None)

    def get_queryset(self, request):
        queryset = super().get_queryset(request)
        queryset = queryset.prefetch_related("lines", "lines__product")
        return queryset


@admin.register(PurchaseReceiptCollarLink, site=unfold_admin_site)
class PurchaseReceiptCollarLinkAdmin(SimpleHistoryAdmin, UnfoldModelAdmin):
    list_display = [
        "collar",
        "purchase_receipt",
    ]
    search_fields = ["collar__serial_no"]
    list_filter = [
        AutocompleteFilterFactory("Collar", "collar"),
        AutocompleteFilterFactory("Purchase Receipt", "purchase_receipt"),
        AutocompleteFilterFactory("Purchase Order", "purchase_receipt__purchase_order"),
    ]

    autocomplete_fields = ["collar", "purchase_receipt"]


@admin.register(PurchaseReceiptLine, site=unfold_admin_site)
class PurchaseReceiptLineAdmin(ImmutableMixin, SimpleHistoryAdmin, UnfoldModelAdmin):
    search_fields = ["product__sku", "product__name", "purchase_receipt__receipt_reference"]

    readonly_fields = ["get_purchase_invoice_link"]

    def has_module_permission(self, request):
        # Hide in admin. Only used for autocomplete lookups.
        return False

    def has_delete_permission(self, *args, **kwargs):
        return super(SimpleHistoryAdmin, self).has_delete_permission(*args, **kwargs)

    @admin.display(description="Purhase Invoice")
    def get_purchase_invoice_link(self, obj):
        if getattr(obj, "purchase_invoice_line", None):
            inv = obj.purchase_invoice_line.purchase_invoice
            url = reverse("admin:core_purchaseinvoice_change", args=[inv.pk])
            return format_html('<a href="{}">{}</a>', url, inv)
        return "-"


class PurchaseInvoiceLineInline(TabularInlineBase):
    model = PurchaseInvoiceLine
    autocomplete_fields = ["product", "purchase_receipt", "purchase_receipt_line"]


@admin.register(PurchaseInvoice, site=unfold_admin_site)
class PurchaseInvoiceAdmin(InlineActionsMixin, SimpleHistoryAdmin, UnfoldModelAdmin):
    list_display = ["invoice_reference", "purchase_order", "file"]

    autocomplete_fields = ["purchase_order"]

    search_fields = ["invoice_reference"]

    list_filter = [
        AutocompleteFilterFactory("Purchase Order", "purchase_order"),
        AutocompleteFilterFactory("Purchase Receipt", "lines__purchase_receipt"),
    ]

    actions = ["import_receipt_lines"]

    actions_detail = ["import_receipt_lines"]

    def get_queryset(self, request: HttpRequest):
        return super().get_queryset(request).distinct()

    def get_inlines(self, request, obj=None):
        inlines = []
        if obj is not None:
            inlines.append(PurchaseInvoiceLineInline)
        return inlines

    @unfold_action(
        description="Import receipt lines",
        permissions=["import_receipts"],
        attrs={"use_post_request": True},
    )
    @transaction.atomic
    def import_receipt_lines(self, request, queryset=None, object_id=None):
        if object_id:
            queryset = self.get_queryset(request).filter(id=object_id)

        if queryset.count() != 1:
            self.message_user(
                request,
                "Please select only one purchase invoice",
                messages.ERROR,
            )
            return redirect(request.get_full_path())

        inv: PurchaseInvoice = queryset[0]

        po: PurchaseOrder = inv.purchase_order

        counts = 0

        for receipt in po.purchase_receipts.filter(status=PurchaseReceipt.Status.CONFIRMED).all():
            receipt: PurchaseReceipt

            for line in receipt.lines.all():
                line: PurchaseReceiptLine

                _, created = PurchaseInvoiceLine.objects.get_or_create(
                    purchase_receipt_line=line,
                    defaults=dict(
                        purchase_invoice=inv,
                        purchase_receipt=receipt,
                        product=line.product,
                        quantity=line.received_quantity,
                        unit_price=0,
                        unit_price_currency=inv.purchase_order.supplier.currency,
                    ),
                )
                if created:
                    counts += 1

        self.message_user(
            request,
            f"Imported {counts} new lines not already linked to invoices",
            messages.SUCCESS,
        )
        return redirect(self.get_redirect_url_for_inline_action(request, inv.id))

    def has_import_receipts_permission(self, request, obj=None):
        return request.user.has_perm("core.change_purchaseinvoice")


@admin.register(FreightMatrix, site=unfold_admin_site)
class FreightMatrixAdmin(DynamicListEditableMixin, SimpleHistoryAdmin, UnfoldModelAdmin):
    list_display = [
        "country",
        "region",
        "collar_type",
        "quantity",
        "product",
        "get_price",
    ]

    autocomplete_fields = ["product"]

    search_fields = ["country", "product__sku"]

    readonly_fields = ["get_price"]

    ordering = ["country", "region", "collar_type", "quantity"]
    dynamic_list_editable = ["product"]

    list_filter = [
        "country",
        "collar_type",
        AutocompleteFilterFactory("Product", "product"),
    ]

    def get_queryset(self, request):
        q = super().get_queryset(request)
        return q.annotate(price_with_currency=get_freight_price_subquery())

    @admin.display(description="Price", ordering="price_with_currency")
    def get_price(self, obj: FreightMatrix = None):
        if obj and obj.price_with_currency:
            return Money(*obj.price_with_currency.split("|"))

    def get_form(self, request, obj=None, **kwargs):
        help_texts = kwargs.setdefault("help_texts", {})
        help_texts.update({"get_price": "Price is defined on the product. The shown price is refreshed after save."})
        return super().get_form(request, obj, **kwargs)


@admin.register(FreightWeightThreshold, site=unfold_admin_site)
class FreightWeightThresholdAdmin(DynamicListEditableMixin, SimpleHistoryAdmin, UnfoldModelAdmin):
    list_display = [
        "country",
        "region",
        "from_weight",
        "product",
        "get_price",
        "internal_cost",
    ]

    autocomplete_fields = ["product"]

    search_fields = ["country", "product__sku"]

    ordering = ["country", "region", "from_weight"]

    readonly_fields = ["get_price"]
    dynamic_list_editable = ["from_weight", "product", "internal_cost"]

    list_filter = [
        "country",
        AutocompleteFilterFactory("Product", "product"),
    ]

    def get_queryset(self, request):
        q = super().get_queryset(request)
        return q.annotate(price_with_currency=get_freight_price_subquery())

    @admin.display(description="Price", ordering="price_with_currency")
    def get_price(self, obj: FreightMatrix = None):
        if obj and obj.price_with_currency:
            return Money(*obj.price_with_currency.split("|"))

    def get_form(self, request, obj=None, **kwargs):
        help_texts = kwargs.setdefault("help_texts", {})
        help_texts.update({"get_price": "Price is defined on the product. The shown price is refreshed after save."})
        return super().get_form(request, obj, **kwargs)


def get_freight_price_subquery() -> Subquery:
    """
    Subquery used to annotate the query set on the freight models to be able to show the price without
    doing a query for each row.

    This should work for both the freight matrix and the freight weight thresholds
    """
    return Subquery(
        ProductPrice.objects.filter(
            product=OuterRef("product"),
            country=OuterRef("country"),
        )
        .annotate(
            # We need to cancatenate the values and split into Money later, as we can only have
            # one return value
            price_with_currency=Concat(
                "price",
                Value("|"),
                "price_currency",
                output_field=CharField(),
            ),
        )
        .values("price_with_currency"),
    )


class BasketLineInline(TabularInlineBase):
    model = BasketLine
    fields = ("product", "quantity", "total", "vat")
    autocomplete_fields = ["product"]
    show_change_link = True
    readonly_fields = ("total", "vat")


class BasketVoucherLineInline(TabularInlineBase):
    model = BasketVoucherLine
    fields = ("customer_voucher", "name")
    show_change_link = True
    readonly_fields = ("name",)
    autocomplete_fields = ["customer_voucher"]

    def get_queryset(self, request: HttpRequest):
        return (
            super()
            .get_queryset(request)
            .select_related(
                "basket__user",
                "customer_voucher__voucher__product__display_name",
            )
        )


class BasketReferralLineInline(TabularInlineBase):
    model = ReferralLine
    verbose_name = "Applied code"


class ReferralLineInline(TabularInlineBase):
    model = ReferralLine
    fields = ("basket",)
    verbose_name = "Applied code"
    raw_id_fields = ("basket",)

    def get_queryset(self, request: HttpRequest) -> QuerySet[UserAdmin]:
        return super().get_queryset(request).select_related("basket")


@admin.register(Discount, site=unfold_admin_site)
class CampaignDiscountAdmin(SimpleHistoryAdmin, UnfoldModelAdmin):
    # This admin class is only registered so that we can have a autocomplete field.
    def has_module_permission(self, request):
        return False


@admin.register(CampaignReferral, site=unfold_admin_site)
class CampaignReferralAdmin(SimpleHistoryAdmin, UnfoldModelAdmin):
    exclude = ["auto_generate_code"]
    search_fields = ["name"]
    list_display = [
        "referral_code",
        "is_referrer_discount",
        "name",
        "description",
        "expiry_date",
        "status",
        "apply_discount_to",
        "discount_percentage",
        "dashboard_link",
    ]
    inlines = [ReferralLineInline]
    list_filter = [
        "expiry_date",
        "market",
        "is_referrer_discount",
        "expiry_date",
    ]

    def get_urls(self):
        urls = super().get_urls()
        custom_urls = [
            path(
                "dashboard/<int:campaign_id>/",
                self.admin_site.admin_view(self.campaign_dashboard_view),
                name="campaign_dashboard",
            ),
        ]
        return custom_urls + urls

    def dashboard_link(self, obj):
        url = reverse("admin:campaign_dashboard", args=[obj.id])
        return format_html('<a href="{}">Dashboard</a>', url)

    dashboard_link.short_description = "Dashboard"

    def campaign_dashboard_view(self, request, campaign_id):
        campaign = CampaignReferral.objects.get(id=campaign_id)
        context: dict = self.admin_site.each_context(request)

        from core.default_services import create_referral_campaign_service

        campaign_data = create_referral_campaign_service().get_dashboard_data_for_campaign(campaign)
        logger.info(self.create_dashboard_table_data(campaign_data))
        context.update(self.create_dashboard_table_data(campaign_data))
        return render(request, "admin/unfold/campaign_dashboard.html", context)

    def create_dashboard_table_data(self, campaign_data: dict) -> dict:
        return {
            "campaign": campaign_data["campaign"],
            "daily_purchase_data": campaign_data["daily_purchase_data"],
            "daily_order_numbers_data": campaign_data["daily_order_numbers_data"],
            "total_order_numbers": campaign_data["dashboard_data"]["confirmed"]["summary"]["total_collars"],
            "total_purchase_amount": campaign_data["dashboard_data"]["confirmed"]["summary"]["total_amount"],
            "table_data": {
                "confirmed": {
                    "headers": [
                        "Basket ID",
                        "Email",
                        "SG Collars",
                        "C Collars",
                        "Total Collars",
                        "Total Amount",
                        "Total Discount",
                    ],
                    "rows": [
                        [
                            basket["id"],
                            basket["email"],
                            basket["sg_collars"],
                            basket["c_collars"],
                            basket["total_collars"],
                            basket["total_amount"],
                            basket["total_discount"],
                        ]
                        for basket in campaign_data["dashboard_data"]["confirmed"]["baskets"]
                    ]
                    + [
                        [
                            "summary",
                            campaign_data["dashboard_data"]["confirmed"]["summary"]["email"],
                            campaign_data["dashboard_data"]["confirmed"]["summary"]["sg_collars"],
                            campaign_data["dashboard_data"]["confirmed"]["summary"]["c_collars"],
                            campaign_data["dashboard_data"]["confirmed"]["summary"]["total_collars"],
                            campaign_data["dashboard_data"]["confirmed"]["summary"]["total_amount"],
                            campaign_data["dashboard_data"]["confirmed"]["summary"]["total_discount"],
                        ],
                    ],
                },
                "pending": {
                    "headers": [
                        "Basket ID",
                        "Email",
                        "SG Collars",
                        "C Collars",
                        "Total Collars",
                        "Total Amount",
                        "Total Discount",
                    ],
                    "rows": [
                        [
                            basket["id"],
                            basket["email"],
                            basket["sg_collars"],
                            basket["c_collars"],
                            basket["total_collars"],
                            basket["total_amount"],
                            basket["total_discount"],
                        ]
                        for basket in campaign_data["dashboard_data"]["pending"]["baskets"]
                    ]
                    + [
                        [
                            "summary",
                            campaign_data["dashboard_data"]["pending"]["summary"]["email"],
                            campaign_data["dashboard_data"]["pending"]["summary"]["sg_collars"],
                            campaign_data["dashboard_data"]["pending"]["summary"]["c_collars"],
                            campaign_data["dashboard_data"]["pending"]["summary"]["total_collars"],
                            campaign_data["dashboard_data"]["pending"]["summary"]["total_amount"],
                            campaign_data["dashboard_data"]["pending"]["summary"]["total_discount"],
                        ],
                    ],
                },
                "total": {
                    "headers": ["SG Collars", "C Collars", "Total Collars", "Total Amount", "Total Discount"],
                    "rows": [
                        [
                            campaign_data["total_data"]["sg_collars"],
                            campaign_data["total_data"]["c_collars"],
                            campaign_data["total_data"]["total_collars"],
                            campaign_data["total_data"]["total_amount"],
                            campaign_data["total_data"]["total_discount"],
                        ],
                    ],
                },
            },
        }


class CustomerReferralStaffFilter(admin.SimpleListFilter):
    title = "Staff"
    parameter_name = "staff"

    def lookups(self, request, model_admin):
        return [("YES", "Yes"), ("NO", "No")]

    def queryset(self, request, queryset):
        if self.value() == "YES":
            return queryset.filter(referred_by__user__is_staff=True)
        elif self.value() == "NO":
            return queryset.filter(referred_by__user__is_staff=False)


class RefereeStatusFilter(SimpleListFilter):
    title = "Referee Status"
    parameter_name = "referee_status"

    def lookups(self, request, model_admin):
        return Customer.Status.choices + [("none", "-")]

    def queryset(self, request, queryset):
        if self.value() == "none":
            return queryset.filter(referee_status__isnull=True)
        elif self.value():
            return queryset.filter(referee_status=self.value())


@admin.register(CustomerReferral, site=unfold_admin_site)
class CustomerReferralAdmin(SimpleHistoryAdmin, UnfoldModelAdmin):
    autocomplete_fields = ["referred_by"]
    search_fields = ["name", "referral_code", "email", "description"]
    list_display = [
        "referral_code",
        "email",
        "referee_status",
        "created_at",
        "referred_by_link",
        "apply_status",
        "sales_order_link1",
        "sales_order_link2",
        "referrer_apply_status",
        "referrer_discount_expiry_date",
    ]

    list_filter = [
        CustomerReferralStaffFilter,
        "apply_status",
        "referrer_apply_status",
        "referred_by__nofence_company",
        "expiry_date",
        "referrer_discount_expiry_date",
        RefereeStatusFilter,
    ]
    list_select_related = ["referred_by", "sales_order", "reward_sales_order"]

    autocomplete_fields = ["sales_order", "reward_sales_order", "referred_by"]

    def referred_by_link(self, obj):
        if obj.referred_by:
            url = reverse("admin:core_customer_change", args=(obj.referred_by.id,))
            return format_html("<a href='{}'>{}</a>", url, obj.referred_by)
        return "-"

    referred_by_link.short_description = "Referred by"

    @admin.display(description="Order")
    def sales_order_link1(self, obj: CustomerReferral):
        sales_order = obj.sales_order
        if sales_order:
            url = reverse("admin:core_salesorder_change", args=(sales_order.id,))
            return format_html(
                "<a href='{}'>{} - {}</a>",
                url,
                sales_order.order_number,
                sales_order.get_status_display(),
            )
        return "-"

    @admin.display(description="Reward Order")
    def sales_order_link2(self, obj: CustomerReferral):
        sales_order = obj.reward_sales_order

        if sales_order:
            url = reverse("admin:core_salesorder_change", args=(sales_order.id,))
            return format_html(
                "<a href='{}'>{} - {}</a>",
                url,
                sales_order.order_number,
                sales_order.get_status_display(),
            )
        return "-"

    def get_queryset(self, request: HttpRequest) -> QuerySet:
        return super().get_queryset(request).order_by("-created_at")


@admin.register(Basket, site=unfold_admin_site)
class BasketAdmin(SimpleHistoryAdmin, UnfoldModelAdmin):
    list_display = ["created_at", "status", "user_link", "vat", "total"]
    autocomplete_fields = ["user", "sales_order"]
    list_filter = [
        ("created_at", RangeDateFilter),
        UserFilter,
        "status",
        "sales_order__payment_status",
        "sales_order__status",
        "user__customer__country",
        "user__customer__nofence_company",
    ]

    list_select_related = ["user__customer__nofence_company"]

    search_fields = [
        "user__email",
        "user__name",
        "user__customer__name",
    ]

    inlines = [BasketLineInline, BasketVoucherLineInline, BasketReferralLineInline]

    actions = [
        csv_export.streaming_csv_export_action(
            "Export Data (CSV)",
            fields=[
                "created_at",
                "status",
                "user",
                "vat",
                "total",
            ],
            extra_fields=[
                ("sales_order__payment_status", "payment status"),
                ("sales_order__status", "sales order status"),
                (),
            ],
        ),
    ]

    def get_queryset(self, request: HttpRequest) -> QuerySet:
        qs = super().get_queryset(request)
        return qs.prefetch_related(
            "basket_lines__product",
        )

    def user_link(self, obj):
        if obj.user:
            url = reverse("admin:core_user_change", args=(obj.user.pk,))
            return format_html("<a href='{}'>{}</a>", url, obj.user)

    user_link.admin_order_field = "user"
    user_link.short_description = "user"


@admin.register(CollarBOMBlock, site=unfold_admin_site)
class CollarBOMBlockAdmin(SimpleHistoryAdmin, UnfoldModelAdmin):
    search_fields = ["ems_provider"]
    list_display = ["created_at", "ems_provider", "product", "pcb_revision", "mec_revision", "product_record"]


unfold_admin_site.register(ShopCategory, ShopCategoryAdmin)


@admin.register(Voucher, site=unfold_admin_site)
class VoucherAdmin(SimpleHistoryAdmin, TranslationUnfoldModelAdmin):
    list_display = ["name", "description", "product", "shipping_included", "created_at"]

    search_fields = ["name", "description", "product__name", "product__sku"]

    autocomplete_fields = ["product"]

    ordering = ["-created_at"]


@admin.register(CustomerVoucher, site=unfold_admin_site)
class CustomerVoucherAdmin(SimpleHistoryAdmin, UnfoldModelAdmin):
    search_fields = ["customer__name", "voucher__name"]

    # This admin class is only registered so that we can have a autocomplete field.
    def has_module_permission(self, request):
        return False


class QuickQuoteAnimalInline(TabularInlineBase):
    model = QuickQuoteAnimal
    fields = ["animal"]


class QuickQuoteFarmingPurposeInline(TabularInlineBase):
    model = QuickQuoteFarmingPurpose
    fields = ["farming_purpose"]


class QuickQuotePastureInline(TabularInlineBase):
    model = QuickQuotePasture
    fields = ["pasture_type"]


class QuickQuoteHasReferralFilter(admin.SimpleListFilter):
    title = "Has Referral Code"
    parameter_name = "has_referral_code"

    def lookups(self, request, model_admin):
        return [("YES", "Yes"), ("NO", "No")]

    def queryset(self, request, queryset):
        if self.value() == "YES":
            return queryset.filter(referral_code__isnull=False)
        elif self.value() == "NO":
            return queryset.filter(referral_code__isnull=True)


@admin.register(QuickQuote, site=unfold_admin_site)
class QuickQuoteAdmin(ModelAdminBase, SimpleHistoryAdmin, UnfoldModelAdmin):
    list_display = [
        "status",
        "country",
        "first_name",
        "last_name",
        "company_name",
        "email",
        "sms_consent",
        "animal",
        "farming_purpose",
        "mobile_coverage",
        "business_type",
        "qualification_status",
        "hubspot_link",
        "customer_link",
        "auto_qualified",
        "created_at",
        "completed_at",
    ]

    search_fields = [
        "first_name",
        "last_name",
        "company_name",
        "email",
        "hubspot_company_id",
        "hubspot_deal_id",
    ]

    list_filter = [
        ("created_at", RangeDateFilter),
        "status",
        "auto_qualified",
        "country",
        "farming_purpose__farming_purpose",
        "mobile_coverage",
        "business_type",
        "qualification_status",
        "source",
        QuickQuoteHasReferralFilter,
        "animal__animal",
        AutocompleteFilterFactory("Customer", "customer"),
    ]

    autocomplete_fields = ["customer"]

    ordering = ["-created_at"]
    actions = [
        "create_billy_customer_without_hubspot_sync",
        "create_billy_customer_with_hubspot_sync",
    ]

    inlines = [
        QuickQuoteAnimalInline,
        QuickQuoteFarmingPurposeInline,
        QuickQuotePastureInline,
    ]

    @admin.display(description="Customer", ordering="customer")
    def customer_link(self, obj: QuickQuote):
        if obj.customer:
            url = reverse("admin:core_customer_change", args=(obj.customer.pk,))
            return format_html("<a href='{}'>{}</a>", url, obj.customer)
        return "-"

    @admin.display(description="Hubspot Company", ordering="hubspot_company_id")
    def hubspot_link(self, obj: QuickQuote):
        if obj.hubspot_company_id:
            return format_html(
                "<a href='{url}'>{hubspot_id}</a>",
                hubspot_id=obj.hubspot_company_id,
                url=f"https://app.hubspot.com/contacts/{settings.HUBSPOT_PORTAL_ID}/company/{obj.hubspot_company_id}",
            )
        return "-"

    @admin.action(description="Create or update billy customer from quick quote (No hubspot sync)")
    def create_billy_customer_without_hubspot_sync(self, request, queryset):
        """
        This can be useful to create a billy customer for a hubspot company that already exists. Maybe something
        caused the company to fail to be synced back to billy in the old setup.

        Then the hubspot id can be manually copied into the created billy customer, and then the action with
        sync to hubspot can be triggered to update the company with the latest values.

        If the company was already created in hubspot, but no customer existed in billy, a duplicate company
        will be created if the action with sync is used.
        """

        service = create_quick_quote_service()

        count = 0
        for quick_quote in queryset.filter(status=QuickQuote.Status.COMPLETED):
            try:
                with transaction.atomic():
                    service.get_or_create_billy_customer(quick_quote, notify=False)
                    count += 1
            except Exception as e:
                self.message_user(
                    request,
                    f"Failed to create billy customer for {quick_quote}: {e}",
                    messages.ERROR,
                )

        if count > 0:
            self.message_user(
                request,
                f"Created/Updated billy customer(s) from {count} quick quote(s). "
                "The customer(s)/quickquote(s) was not synced to hubspot.",
                messages.SUCCESS,
            )

    @admin.action(description="Create or update billy customer from quick quote (With hubspot sync)")
    def create_billy_customer_with_hubspot_sync(self, request, queryset):
        service = create_quick_quote_service()

        count = 0
        for quick_quote in queryset.filter(status=QuickQuote.Status.COMPLETED):
            try:
                with transaction.atomic():
                    service.get_or_create_billy_customer(quick_quote, notify=True)
                    count += 1
            except Exception as e:
                self.message_user(
                    request,
                    f"Failed to create billy customer from {quick_quote}: {e}",
                    messages.ERROR,
                )

        if count > 0:
            self.message_user(
                request,
                f"Created/Updated billy customer(s) from {count} quick quote(s), with sync to hubspot",
                messages.SUCCESS,
            )


@admin.register(CarouselItem, site=unfold_admin_site)
class CarouselItemAdmin(SimpleHistoryAdmin, TranslationUnfoldModelAdmin):
    formfield_override_by_name = {
        "countries": {"widget": UnfoldSelect2MultipleAdminWidget},
    }

    list_display = ["title", "ordering", "created_at", "background_color"]
    search_fields = ["title"]
    list_filter = ["markets"]
    ordering = ["ordering", "pk"]

    readonly_fields = [
        "get_preview_en",
        "get_preview_nb",
        "get_preview_es",
    ]

    fieldsets = [
        (
            None,
            {
                "fields": [
                    "title",
                    "description",
                    "get_preview_en",
                    "html_en",
                    "get_preview_nb",
                    "html_nb",
                    "get_preview_es",
                    "html_es",
                    "button_text",
                    "button_link",
                    "image",
                    "background_color",
                    "text_color",
                    "button_background_color",
                    "button_text_color",
                    "markets",
                    "countries",
                    "ordering",
                    "referral_type",
                    "special_modal_type",
                ],
            },
        ),
    ]

    @admin.display(description="Preview")
    def get_preview_en(self, obj: CarouselItem):
        return self._render_preview(obj.html_en or "-", "en")

    @admin.display(description="Preview")
    def get_preview_nb(self, obj: CarouselItem):
        return self._render_preview(obj.html_nb or "-", "nb")

    @admin.display(description="Preview")
    def get_preview_es(self, obj: CarouselItem):
        return self._render_preview(obj.html_es or "-", "es")

    def _render_preview(self, html: str, lang: str):
        return mark_safe(f"""
        <div id="preview-html-{lang}">
            <template shadowrootmode="open">
                <div id="preview-content">{html}</div>
                <div style="font-style: itallic; font-size: 0.8rem; color: #aaa; margin-top: 1rem;">
                Note: The preview may not be 100% accurate. Depending on where it is used, the
                final result may be affected by the global styling of the page where it is rendered.
                </div>
            </template>
        </div>
        <script>
            document.addEventListener('DOMContentLoaded', function () {{
                let input = document.querySelector("#id_html_{lang}")
                let previewHost = document.querySelector("#preview-html-{lang}")
                input.addEventListener("input", function(e){{
                    let preview = previewHost.shadowRoot.querySelector("#preview-content")
                    preview.innerHTML = input.value
                }})
            }});
        </script>
        """)


@admin.register(TaxDefinition, site=unfold_admin_site)
class TaxDefinitionAdmin(
    DynamicListEditableMixin,
    ModelAdminBase,
    SimpleHistoryAdmin,
):
    list_display = ["nofence_company", "country", "tax_category", "tax_rate", "tax_code", "tax_message", "updated_at"]
    ordering = ["nofence_company", "country"]

    autocomplete_fields = ["tax_category", "tax_code"]

    list_select_related = ["nofence_company", "tax_category", "tax_code__nofence_company"]

    search_fields = ["name"]

    dynamic_list_editable = ["tax_rate", "tax_code", "tax_message"]


@admin.register(TaxCategory, site=unfold_admin_site)
class TaxCategoryAdmin(ModelAdminBase, SimpleHistoryAdmin, UnfoldModelAdmin):
    list_display = ["name", "default", "updated_at", "stripe_product_tax_code"]
    search_fields = ["name"]

    autocomplete_fields = ["stripe_product_tax_code"]


@admin.register(ProductPrice, site=unfold_admin_site)
class ProductPriceAdmin(DynamicListEditableMixin, ModelAdminBase, SimpleHistoryAdmin):
    list_display = ["product", "country", "price"]
    list_filter = [
        "country",
        AutocompleteFilterFactory("Product", "product"),
        "product__category",
        "country",
    ]
    ordering = ["price", "product__name", "country"]
    autocomplete_fields = ["product"]
    search_fields = ["country", "product__sku"]
    dynamic_list_editable = ["price"]


@admin.register(TaxCode, site=unfold_admin_site)
class TaxCodeAdmin(ModelAdminBase, SimpleHistoryAdmin, UnfoldModelAdmin):
    list_display = ["tax_code", "name", "nofence_company", "provider"]
    list_filter = [
        "nofence_company",
        "provider",
    ]
    search_fields = ["name", "nofence_company__name"]
    readonly_fields = ["formatted_metadata"]
    ordering = ["nofence_company", "name"]

    def formatted_metadata(self, obj: TaxCode):
        html = []
        html.append(
            format_html("<pre>{}</pre>", pprint.pformat(obj.metadata, indent=2, width=100)),
        )
        return mark_safe("".join(html))

    def get_search_results(self, request, queryset, search_term):
        # Try to make the autocomplete a bit more relevant by filtering only on the selected
        # nofence company. This only work from inline edits from the changlist and update of
        # existing tax definition. It should be possible to extended to the add form if we want,
        # but this will require some javascript.
        referer = request.headers.get("referer", None)
        if referer and "autocomplete" in request.path:
            referer = urlparse(referer)
            company_id = None
            m = re.search(r"/taxdefinition/(\d+)/change", referer.path)
            if m:
                tax_def = TaxDefinition.objects.get(id=m.group(1))
                company_id = tax_def.nofence_company.id
            elif referer.query:
                query = parse_qs(referer.query)
                company_id = query.get("nofence_company__id__exact", None)
                if company_id:
                    company_id = company_id[0]

            if company_id:
                queryset = queryset.filter(nofence_company_id=company_id)

        queryset = queryset.select_related("nofence_company")
        return super().get_search_results(request, queryset, search_term)


class AnswerChoiceNestedInline(NestedStackedInline, StackedInlineBase):
    model = AnswerChoice
    extra = 1
    fk_name = "question"
    ordering = ["value"]
    readonly_fields = ["percentage_choosen", "number_chosen", "image"]

    def formfield_for_foreignkey(self, db_field, request=None, **kwargs):
        field = super(AnswerChoiceNestedInline, self).formfield_for_foreignkey(db_field, request, **kwargs)

        if db_field.name == "next_question":
            if request.resolver_match.kwargs.get("object_id") is not None:
                field.queryset = field.queryset.filter(
                    questionnaire__exact=request.resolver_match.kwargs.get("object_id"),
                )
            else:
                field.queryset = field.queryset.none()

        return field


class QuestionInline(NestedStackedInline, StackedInlineBase):
    model = Question
    extra = 0
    show_change_link = True
    ordering = ["order"]
    readonly_fields = ["number_of_responders", "average_weighted_value"]
    inlines = [AnswerChoiceNestedInline]

    def formfield_for_foreignkey(self, db_field, request=None, **kwargs):
        field = super(QuestionInline, self).formfield_for_foreignkey(db_field, request, **kwargs)

        if db_field.name == "next_question":
            if request.resolver_match.kwargs.get("object_id") is not None:
                field.queryset = field.queryset.filter(
                    questionnaire__exact=request.resolver_match.kwargs.get("object_id"),
                )
            else:
                field.queryset = field.queryset.none()

        return field


@admin.register(Questionnaire, site=unfold_admin_site)
class QuestionnaireAdmin(NestedModelAdminBase, SimpleHistoryAdmin, TranslationUnfoldModelAdmin):
    list_display = ["title", "type", "active_time_start", "active_time_end", "created_at"]
    autocomplete_fields = ["users", "user_has_viewed_questionnaire"]
    search_fields = ["title"]
    inlines = [QuestionInline]
    readonly_fields = ["view_count", "open_ratio", "answered_count", "answered_ratio"]

    actions = ["export_as_csv"]

    @admin.action(description="Export as CSV")
    def export_as_csv(self, request, queryset):
        # Define response and CSV writer
        response = HttpResponse(content_type="text/csv")
        writer = csv.writer(response)

        # Header: Add customer fields and then dynamic question fields
        customer_field_names = ["customer_country", "customer_id"]
        static_fields = ["user_id"]  # Static user fields to include

        # Write header row
        header = customer_field_names + static_fields

        if queryset.count() > 1:
            # show error to user
            messages.error(request, "Please select only one questionnaire to export")
            return redirect(request, "admin:core_questionnaire_changelist")

        # Add dynamic question field names
        if queryset.exists():
            # Assume all questionnaires have the same questions
            questionnaire: Questionnaire = queryset.first()
            questions = questionnaire.questions.all()
            # Truncated question text for column names
            question_field_names = []
            for q in questions:
                question_field_names += [f"{q.pk}_{q.question[:200]}", f"{q.pk}_value"]
            header += question_field_names

            response["Content-Disposition"] = f'attachment; filename="{questionnaire.pk}_{questionnaire.title}.csv"'
        else:
            messages.error(request, "No questionnaires selected")
            return redirect(request, "admin:core_questionnaire_changelist")

        writer.writerow(header)

        def get_questionnaire_answers(questionnaire, user_id) -> List[str]:
            answers = []
            for question in questionnaire.questions.order_by("order").all():
                if question.question_type == Question.QuestionType.TEXT:
                    text_answer = question.questionnairetextanswer_set.filter(identifier=user_id).first()
                    if text_answer:
                        answers.append(text_answer.answer)
                        answers.append("N/A")
                    else:
                        answers.append("None")
                        answers.append("N/A")
                elif question.question_type == Question.QuestionType.CHOICE:
                    choice_answer = question.questionnairechoiceanswer_set.filter(identifier=user_id).first()
                    if choice_answer:
                        selected_choices = choice_answer.choice.answer
                        selected_choice_value = choice_answer.choice.value
                        answers.append(selected_choices)
                        answers.append(selected_choice_value)
                    else:
                        answers.append("None")
                        answers.append("None")
                else:
                    answers.append("Unknown")
                    answers.append("Unknown")

            return answers

        # Write data rows
        for questionnaire in queryset:
            if questionnaire.active_for_all_users:
                for user_id, responder in (
                    QuestionnaireChoiceAnswer.objects.filter(question__questionnaire=questionnaire)
                    .values_list("identifier", "responder")
                    .distinct("identifier")
                ):
                    if not questionnaire.user_has_answered(user_id):
                        continue
                    customer_country = "N/A"
                    customer_id = responder.customer.id if hasattr(responder, "customer") else "N/A"
                    user_info = [customer_country, customer_id, user_id]

                    answers = get_questionnaire_answers(questionnaire, user_id)

                    writer.writerow(user_info + answers)
                continue
            for user in questionnaire.users.all():
                if not questionnaire.user_has_answered(user.pk):
                    continue
                # Get customer info
                customer_country = user.customer.country if hasattr(user, "customer") else "N/A"
                customer_id = user.customer.id if hasattr(user, "customer") else "N/A"

                # Basic user info
                user_info = [customer_country, customer_id, user.id]

                # Gather answers for each question
                answers = get_questionnaire_answers(questionnaire, user.id)

                # Write row to CSV
                writer.writerow(user_info + answers)

        return response


class QuestionChoiceInline(TabularInlineBase):
    model = AnswerChoice
    fk_name = "question"


class QuestionnaireTextAnswerInline(TabularInlineBase):
    model = QuestionnaireTextAnswer
    raw_id_fields = ["responder"]


class QuestionnaireChoiceAnswerInline(TabularInlineBase):
    model = QuestionnaireChoiceAnswer
    raw_id_fields = ["responder"]


@admin.register(Question, site=unfold_admin_site)
class QuestionAdmin(ModelAdminBase, SimpleHistoryAdmin, TranslationUnfoldModelAdmin):
    list_display = ["question", "question_type", "created_at"]

    inlines = [QuestionChoiceInline, QuestionnaireTextAnswerInline, QuestionnaireChoiceAnswerInline]


@admin.register(QuestionnaireDismissed, site=unfold_admin_site)
class QuestionnaireDismissedAdmin(ModelAdminBase):
    list_display = ["user", "questionnaire", "dismiss_count"]


@admin.register(CustomerAccountingSystem, site=unfold_admin_site)
class CustomerAccountingSystemAdmin(ModelAdminBase, SimpleHistoryAdmin, UnfoldModelAdmin):
    list_display = ["customer", "nofence_company", "accounting_system", "external_id"]

    list_filter = ["nofence_company", "accounting_system"]

    search_fields = ["external_id", "customer__name"]

    autocomplete_fields = ["customer"]


@admin.register(TaxCalculation, site=unfold_admin_site)
class TaxCalculationAdmin(
    ImmutableMixinWithDelete,
    ModelAdminBase,
    SimpleHistoryAdmin,
    UnfoldModelAdmin,
):
    list_display = [
        "amount_total",
        "tax_amount_exclusive",
        "source",
        "user_link",
        "customer_link",
        "object_link",
        "created_at",
    ]
    search_fields = ["customer__name"]
    autocomplete_fields = [
        "user",
        "customer",
        "basket",
    ]

    list_select_related = ["user", "customer", "basket__user", "invoice", "sales_order"]

    readonly_fields = [
        "pretty_request",
        "pretty_result",
    ]

    exclude = ["request", "result"]

    list_filter = [
        AutocompleteFilterFactory("User", "user"),
        AutocompleteFilterFactory("Customer", "customer"),
        AutocompleteFilterFactory("Basket", "basket"),
        AutocompleteFilterFactory("Sales Order", "sales_order"),
        "source",
    ]

    @admin.display(description="request")
    def pretty_request(self, obj: TaxCalculation):
        content = pprint.pformat(obj.request, compact=True)
        return format_html("<pre>{content}</pre>", content=content)

    @admin.display(description="result")
    def pretty_result(self, obj: TaxCalculation):
        content = pprint.pformat(obj.result, compact=True)
        return format_html("<pre>{content}</pre>", content=content)

    @admin.display(description="Sales Order / Basket / Invoice")
    def object_link(self, obj: TaxCalculation):
        if obj.sales_order:
            url = reverse("admin:core_salesorder_change", args=(obj.sales_order.pk,))
            title = f"{obj.sales_order.order_number}"
        elif obj.basket:
            url = reverse("admin:core_basket_change", args=(obj.basket.pk,))
            title = f"{obj.basket}"
        elif obj.invoice:
            title = obj.invoice.invoice_provider_ref or obj.invoice.invoice_provider_id or obj.invoice.id
            url = reverse("admin:core_invoice_change", args=(obj.invoice.pk,))
            title = f"Invoice: {title}"
        else:
            url = None
            title = "-"
        if url:
            return format_html("<a href='{}'>{}</a>", url, title)
        return title

    @admin.display(description="Customer", ordering="customer")
    def customer_link(self, obj: TaxCalculation):
        if obj.customer:
            url = reverse("admin:core_customer_change", args=(obj.customer.pk,))
            return format_html("<a href='{}'>{}</a>", url, obj.customer)
        return "-"

    @admin.display(description="User", ordering="user")
    def user_link(self, obj: TaxCalculation):
        if obj.user:
            url = reverse("admin:core_user_change", args=(obj.user.pk,))
            return format_html("<a href='{}'>{}</a>", url, obj.user)
        return "-"


class CardPaymentEventInline(StackedInlineBase):
    model = CardPaymentEvent
    fields = ["source", "event_type", "event_created_at", "pretty_data"]
    readonly_fields = [
        "pretty_data",
    ]

    # The stripe events seems to not have milliseconds, so if we just order
    # by the event created at as is, some event can appear out of order.
    # By ordering by the each second and then by id when they are the same,
    # it should make the ordering correct in most cases.
    ordering = (Trunc("event_created_at", "second").desc(), "-id")

    classes = ["collapse"]

    @admin.display(description="data")
    def pretty_data(self, obj: CardPaymentEvent):
        content = pprint.pformat(obj.data, compact=True)
        return format_html("<pre>{content}</pre>", content=content)


class CardPaymentIsFailedFilter(SimpleListFilter):
    title = "Is Failed"
    parameter_name = "is_failed"

    def lookups(self, request, model_admin):
        return ("YES", "Yes"), ("NO", "No")

    def queryset(self, request, queryset):
        if self.value() == "YES":
            return queryset.exclude(
                payment_intent_data__last_payment_error__isnull=False,
                payment_intent_data__last_payment_error=None,
            )
        if self.value() == "NO":
            return queryset.filter(
                payment_intent_data__last_payment_error=None,
            )


class VismaPaymentSyncInline(ImmutableMixin, TabularInlineBase):
    model = VismaPaymentSync
    fields = [
        "customer_payment_id",
        "customer_payment_released",
        "fee_journal_transaction_id",
        "fee_journal_transaction_released",
        "updated_at",
    ]

    readonly_fields = ["updated_at"]


class XeroPaymentSyncInline(ImmutableMixin, TabularInlineBase):
    model = XeroPaymentSync
    fields = [
        "payment_id",
        "fee_bank_transaction_id",
        "fee_amount",
        "rounding_amount",
        "updated_at",
    ]

    readonly_fields = ["updated_at"]


@admin.register(CardPayment, site=unfold_admin_site)
class CardPaymentAdmin(ImmutableMixin, ModelAdminBase, SimpleHistoryAdmin, UnfoldModelAdmin):
    list_display = [
        "amount",
        "status",
        "sales_order_link",
        "customer_link",
        "nofence_company",
        "updated_at",
    ]

    autocomplete_fields = [
        "customer",
        "sales_order",
        "tax_calculation",
        "invoice_document",
    ]
    search_fields = [
        "customer__name",
        "sales_order__generated_order_number",
        "invoice_document__doc_id",
        "invoice_document__doc_ref",
    ]

    inlines = [
        CardPaymentEventInline,
    ]

    list_select_related = [
        "sales_order",
        "invoice_document",
        "nofence_company",
        "customer__user",
    ]

    list_filter = [
        "status",
        "nofence_company",
        "object_type",
        CardPaymentIsFailedFilter,
        AutocompleteFilterFactory("Customer", "customer"),
        AutocompleteFilterFactory("Sales Order", "sales_order"),
        AutocompleteFilterFactory("Invoice Document", "invoice_document"),
    ]

    history_unified_diff = ["payment_intent_data"]

    ordering = ["-id"]

    def get_inlines(self, request, obj: CardPayment):
        inlines = [
            CardPaymentEventInline,
        ]

        if obj.nofence_company == NOFENCE_NO:
            inlines.append(VismaPaymentSyncInline)
        else:
            inlines.append(XeroPaymentSyncInline)

        return inlines

    def get_list_display(self, request):
        list_display = super().get_list_display(request)
        if request.GET.get("is_failed", "") == "YES":
            return [*list_display, "error_message"]
        return list_display

    @admin.display(description="Sales Order/Invoice")
    def sales_order_link(self, obj: CardPayment):
        if obj.is_for_sales_order():
            so: SalesOrder = obj.sales_order
            url = reverse("admin:core_salesorder_change", args=(obj.sales_order.pk,))
            title = f"{so.order_number} - {so.get_status_display()}"
        elif obj.is_for_invoice():
            inv: InvoiceDocument = obj.invoice_document
            url = reverse("admin:core_invoicedocument_change", args=(obj.invoice_document.pk,))
            title = f"{inv.reference_number} - {inv.get_status_display()}"
            if inv.is_using_visma():
                title = f"Inv: {title}"

        return format_html("<a href='{}'>{}</a>", url, title)

    @admin.display(description="Customer", ordering="customer")
    def customer_link(self, obj: CardPayment):
        url = reverse("admin:core_customer_change", args=(obj.customer.pk,))
        return format_html("<a href='{}'>{}</a>", url, obj.customer)

    @admin.display(description="Last Error")
    def error_message(self, obj: CardPayment):
        msg = []
        if obj.payment_intent_data:
            error = obj.payment_intent_data.get("last_payment_error")
            if error:
                msg.append(error["message"])

            charge = obj.payment_intent_data.get("latest_charge", {})
            if charge:
                outcome = charge.get("outcome", {})
                if outcome:
                    msg.append(outcome.get("seller_message", ""))
        else:
            msg.append("Payment intent creation failed. Check event log for error messages.")

        if msg:
            return format_html('<span style="color:red;">{}</span>', " ".join(msg))
        return "-"


class IsDefaultPaymentMethodFilter(SimpleListFilter):
    title = "Is Default"
    parameter_name = "is_default"

    def lookups(self, request, model_admin):
        return ("YES", "Yes"), ("NO", "No")

    def queryset(self, request, queryset):
        if self.value() == "YES":
            return queryset.filter(
                customer__stripe_default_payment_method=F("payment_method_id"),
            )
        if self.value() == "NO":
            return queryset.exclude(
                customer__stripe_default_payment_method=F("payment_method_id"),
            )


class IsExpiredPaymentMethodFilter(SimpleListFilter):
    title = "Is Expired"
    parameter_name = "is_expired"

    def lookups(self, request, model_admin):
        return ("YES", "Yes"), ("NO", "No")

    def queryset(self, request, queryset):
        if self.value() == "YES":
            return queryset.filter(
                expire_month__lt=date.today().replace(day=1),
            )
        if self.value() == "NO":
            return queryset.filter(
                Q(expire_month__isnull=True) | Q(expire_month__gte=date.today().replace(day=1)),
            )


@admin.register(StripePaymentMethod, site=unfold_admin_site)
class StripePaymentMethodAdmin(ImmutableMixinWithDelete, ModelAdminBase, SimpleHistoryAdmin):
    list_display = [
        "get_method_name",
        "get_payment_method_type",
        "get_expires",
        "get_is_default",
        "customer_link",
        "payment_method_created_at",
        "updated_at",
    ]

    search_fields = ["payment_method_id"]

    list_select_related = ["customer"]

    autocomplete_fields = [
        "customer",
        "nofence_company",
    ]

    ordering = ["-payment_method_created_at"]

    list_filter = [
        "payment_method_type",
        IsDefaultPaymentMethodFilter,
        IsExpiredPaymentMethodFilter,
        ("expire_month", RangeDateFilter),
        AutocompleteFilterFactory("Customer", "customer"),
    ]

    @admin.display(description="Payment Method")
    def get_method_name(self, obj: StripePaymentMethod):
        display = obj.get_display_data()
        return f"{display.name} {display.extra}"

    @admin.display(description="Is Default", boolean=True)
    def get_is_default(self, obj: StripePaymentMethod):
        return obj.is_customers_default_method()

    @admin.display(description="Expires", ordering="expire_month")
    def get_expires(self, obj: StripePaymentMethod):
        if obj.expire_month:
            return f"{obj.expire_month.month}/{obj.expire_month.year}"

    @admin.display(description="Type", ordering="payment_method_type")
    def get_payment_method_type(self, obj: StripePaymentMethod):
        display = obj.get_display_data()
        return format_html(
            '<div><img style="display: inline-block" src="{}"> {}</div>',
            display.icon,
            obj.payment_method_type,
        )

    @admin.display(description="Customer", ordering="customer")
    def customer_link(self, obj):
        url = reverse("admin:core_customer_change", args=(obj.customer.pk,))
        return format_html("<a href='{}'>{}</a>", url, obj.customer)


@admin.register(StripeProductTaxCode, site=unfold_admin_site)
class StripeProductTaxCodeAdmin(ModelAdminBase, SimpleHistoryAdmin, UnfoldModelAdmin):
    list_display = [
        "tax_code_id",
        "type",
        "description",
        "name",
    ]
    search_fields = ["name", "tax_code_id"]


class DataExportTermsAndConditionsInline(TabularInlineBase):
    model = DataExport.terms_and_conditions.through
    extra = 0
    verbose_name = _("Terms and Conditions")
    verbose_name_plural = _("Terms and Conditions")


@admin.register(DataExport, site=unfold_admin_site)
class DataExportAdmin(ImmutableMixinWithDelete, ModelAdminBase, SimpleHistoryAdmin, UnfoldModelAdmin):
    list_display = [
        "created_at",
        "export_reason",
        "customer_link",
    ]
    ordering = ["-created_at"]

    list_filter = [
        ("created_at", RangeDateFilter),
        "export_reason",
        AutocompleteFilterFactory("Customer", "customer"),
    ]

    inlines = [DataExportTermsAndConditionsInline]

    exclude = ["terms_and_conditions"]

    search_fields = ["external_id"]

    @admin.display(description="Customer")
    def customer_link(self, obj):
        url = reverse("admin:core_customer_change", args=(obj.customer.pk,))
        return format_html("<a href='{}'>{}</a>", url, obj.customer)


@admin.register(HTMLContent, site=unfold_admin_site)
class HTMLContentAdmin(SimpleHistoryAdmin, TranslationUnfoldModelAdmin):
    formfield_override_by_name = {
        "countries": {"widget": UnfoldSelect2MultipleAdminWidget},
    }

    list_display = [
        "name",
        "content",
    ]

    readonly_fields = [
        "get_preview_en",
        "get_preview_nb",
        "get_preview_es",
    ]

    fieldsets = [
        (
            None,
            {
                "fields": [
                    "type",
                    "name",
                    "get_preview_en",
                    "content_en",
                    "get_preview_nb",
                    "content_nb",
                    "get_preview_es",
                    "content_es",
                    "countries",
                ],
            },
        ),
    ]

    @admin.display(description="Preview")
    def get_preview_en(self, obj: HTMLContent):
        return self._render_preview(obj.content_en or "-", "en")

    @admin.display(description="Preview")
    def get_preview_nb(self, obj: HTMLContent):
        return self._render_preview(obj.content_nb or "-", "nb")

    @admin.display(description="Preview")
    def get_preview_es(self, obj: HTMLContent):
        return self._render_preview(obj.content_es or "-", "es")

    def _render_preview(self, html: str, lang: str):
        return mark_safe(f"""
        <div id="preview-content-{lang}">
            <template shadowrootmode="open">
                <div id="preview-content">{html}</div>
                <div style="font-style: itallic; font-size: 0.8rem; color: #aaa; margin-top: 1rem;">
                Note: The preview may not be 100% accurate. Depending on where it is used, the
                final result may be affected by the global styling of the page where it is rendered.
                </div>
            </template>
        </div>
        <script>
            document.addEventListener('DOMContentLoaded', function () {{
                let input = document.querySelector("#id_content_{lang}")
                let previewHost = document.querySelector("#preview-content-{lang}")
                input.addEventListener("input", function(e){{
                    let preview = previewHost.shadowRoot.querySelector("#preview-content")
                    preview.innerHTML = input.value
                }})
            }});
        </script>
        """)


@admin.register(InvoiceDocumentPayment, site=unfold_admin_site)
class InvoiceDocumentPaymentAdmin(ImmutableMixin, ModelAdminBase, SimpleHistoryAdmin, UnfoldModelAdmin):
    list_display = [
        "payment_id",
        "provider",
        "type",
        "amount",
        "payment_date",
        "invoice_link",
    ]

    list_select_related = [
        "invoice_document__customer__user",
        "invoice_document__nofence_company",
    ]

    autocomplete_fields = ["invoice_document"]

    ordering = ["-id"]

    list_filter = [
        "nofence_company",
        "provider",
        "status",
        "type",
        AutocompleteFilterFactory("Invoice Document", "invoice_document"),
    ]

    search_fields = ["payment_id"]

    history_unified_diff = ["body"]

    @admin.display(description="Invoice Document")
    def invoice_link(self, obj: InvoiceDocumentPayment):
        if obj.invoice_document:
            url = reverse("admin:core_invoicedocument_change", args=(obj.invoice_document.pk,))
            return format_html("<a href='{}'>{}</a>", url, obj.invoice_document)
        return "-"


class SentToRivertyFilter(SimpleListFilter):
    title = "Sent to riverty"
    parameter_name = "sent_to_riverty"

    def lookups(self, request, model_admin):
        return ("YES", "Yes"), ("NO", "No")

    def queryset(self, request, queryset):
        if self.value() == "YES":
            return queryset.filter(sent_to_riverty_at__isnull=False)
        if self.value() == "NO":
            return queryset.filter(sent_to_riverty_at__isnull=True)


class RivertyCasePaymentInline(ImmutableMixin, TabularInlineBase):
    model = RivertyCasePayment

    fields = [
        "payment_reference",
        "amount",
        "payment_date",
        "is_new_payment",
        "sent_to_riverty_at",
        "updated_at",
    ]

    readonly_fields = ["updated_at"]

    ordering = ("-id",)

    show_change_link = True


@admin.register(RivertyCase, site=unfold_admin_site)
class RiveryCaseAdmin(ModelAdminBase, SimpleHistoryAdmin):
    list_display = [
        "get_case_description",
        "customer_link",
        "original_balance",
        "sent_to_riverty_at",
        "updated_at",
    ]

    list_select_related = [
        "invoice_document",
        "customer__user",
    ]

    autocomplete_fields = ["debt_collection", "invoice_document", "customer"]

    ordering = ["-id"]

    list_filter = [
        AutocompleteFilterFactory("Customer", "customer"),
        AutocompleteFilterFactory("Invoice Document", "invoice_document"),
        AutocompleteFilterFactory("Debt collection", "debt_collection"),
        SentToRivertyFilter,
        "invoice_document__status",
    ]

    readonly_fields = ["file_upload"]

    exclude = ["file_payload"]

    search_fields = [
        "invoice_document__doc_id",
    ]

    inlines = [
        RivertyCasePaymentInline,
    ]

    actions = [
        "refresh_case",
        "upload_to_riverty",
        "download_case_file",
    ]

    actions_row = [
        "download_case_file",
    ]

    actions_detail = [
        "download_case_file",
    ]

    @admin.display(description="Customer")
    def customer_link(self, obj: RivertyCase):
        customer = obj.customer
        url = reverse("admin:core_customer_change", args=(customer.pk,))
        return format_html("<a href='{}'>{}</a>", url, customer)

    @admin.display(description="Invoice")
    def get_case_description(self, obj: RivertyCase):
        invoice = obj.invoice_document
        return f"{invoice.reference_number} - {invoice.get_status_display()}"

    @unfold_action(description="Download case file payload")
    def download_case_file(self, request, queryset=None, object_id=None):
        if object_id:
            cases = RivertyCase.objects.filter(id=object_id)
        else:
            cases = queryset

        service = create_riverty_service()

        cases = service.order_cases_for_collection_file(cases)

        payload = service.create_collection_file_payload(cases)
        payload = service.get_encoded_file_content(payload)

        filename = service.get_collection_filename(RivertyFileUpload(id=0))

        return HttpResponse(
            payload,
            content_type="text/plain",
            headers={"Content-Disposition": f'attachment; charset=windows-1252; filename="{filename}"'},
        )

    @unfold_action(description="Refresh case")
    def refresh_case(self, request, queryset: QuerySet[RivertyCase]):
        service = create_riverty_service()
        count = 0
        for case in queryset:
            try:
                service.update_riverty_case(case)
                count += 1
            except RivertyServiceError as e:
                self.message_user(
                    request,
                    f"Failed to refresh {case.invoice_document.reference_number}: {e}",
                    messages.ERROR,
                )

        if count:
            self.message_user(
                request,
                f"Refreshed {count} riverty case(s)",
                messages.SUCCESS,
            )

    @unfold_action(description="Upload to rivery")
    def upload_to_riverty(self, request, queryset: QuerySet[RivertyCase]):
        service = create_riverty_service()
        skipped = 0

        cases = []
        for case in queryset:
            # Make sure it is up to date
            try:
                case = service.update_riverty_case(case)
                if case.debt_collection.is_active():
                    cases.append(case)
                else:
                    skipped += 1
            except RivertyServiceError as e:
                self.message_user(
                    request,
                    f"Failed to upload {case.invoice_document.reference_number}: {e}",
                    messages.ERROR,
                )

        try:
            uploads = service.upload_to_riverty(cases)
        except RivertyServiceError as e:
            self.message_user(
                request,
                f"Failed to upload {len(cases)} case(s): {e}",
                messages.ERROR,
            )
            return

        cases_upload = next((u for u in uploads if u.is_collection_file()), None)
        num_cases = cases_upload.get_num_cases() if cases_upload else 0
        self.message_user(
            request,
            f"Uploaded {num_cases} new riverty case(s)",
            messages.SUCCESS,
        )
        payments_upload = next((u for u in uploads if u.is_payment_file()), None)
        num_payments = payments_upload.get_num_payments() if payments_upload else 0
        self.message_user(
            request,
            f"Uploaded {num_payments} new riverty payment(s)",
            messages.SUCCESS,
        )

        if skipped:
            self.message_user(
                request,
                (f"Skipped upload of {skipped} riverty case(s). Debt collection status not valid for upload."),
                messages.WARNING,
            )


@admin.register(RivertyCasePayment, site=unfold_admin_site)
class RiveryCasePaymentAdmin(ModelAdminBase, SimpleHistoryAdmin):
    list_display = [
        "payment_reference",
        "case_link",
        "amount",
        "payment_date",
        "is_new_payment",
        "sent_to_riverty_at",
        "updated_at",
    ]

    list_select_related = [
        "riverty_case__invoice_document",
    ]

    readonly_fields = ["file_upload"]

    exclude = ["file_payload"]

    ordering = ["-id"]

    autocomplete_fields = ["riverty_case", "invoice_document_payment"]

    list_filter = [
        AutocompleteFilterFactory("Riverty Case", "riverty_case"),
        "is_new_payment",
        SentToRivertyFilter,
    ]

    actions = [
        "download_payment_file",
    ]
    actions_row = [
        "download_payment_file",
    ]
    actions_detail = [
        "download_payment_file",
    ]

    search_fields = ["payment_reference"]

    @admin.display(description="Riverty Case")
    def case_link(self, obj: RivertyCasePayment):
        invoice = obj.riverty_case.invoice_document
        url = reverse("admin:core_rivertycase_change", args=(obj.riverty_case.pk,))
        return format_html(
            "<a href='{}'>{}</a>",
            url,
            f"{invoice.reference_number} - {invoice.get_status_display()}",
        )

    @unfold_action(description="Download payment file payload")
    def download_payment_file(self, request, queryset: QuerySet[RivertyCasePayment] = None, object_id=None):
        if object_id:
            payments = RivertyCasePayment.objects.filter(id=object_id)
        else:
            payments = queryset

        payments = payments.order_by("invoice_document_payment__id")

        service = create_riverty_service()

        payload = service.create_payment_file_payload(payments)
        payload = service.get_encoded_file_content(payload)
        filename = service.get_payment_filename(RivertyFileUpload(id=0))

        return HttpResponse(
            payload,
            content_type="text/plain",
            headers={"Content-Disposition": f'attachment; charset=windows-1252; filename="{filename}"'},
        )


class RivertyCaseInline(ImmutableMixin, TabularInlineBase):
    model = RivertyCase

    fields = [
        "case_link",
        "customer",
        "original_balance",
    ]

    readonly_fields = ["case_link"]

    ordering = ("-id",)

    show_change_link = True

    def get_queryset(self, request: HttpRequest) -> QuerySet:
        return super().get_queryset(request).select_related("invoice_document", "customer")

    @admin.display(description="Riverty Case")
    def case_link(self, obj: RivertyCase):
        invoice = obj.invoice_document
        url = reverse("admin:core_rivertycase_change", args=(obj.pk,))
        return format_html(
            "<a class='text-primary-600 dark:text-primary-500' href='{}'>{}</a>",
            url,
            f"{invoice.reference_number} - {invoice.get_status_display()}",
        )


@admin.register(RivertyFileUpload, site=unfold_admin_site)
class RiveryFileUploadAdmin(ImmutableMixinWithDelete, ModelAdminBase, SimpleHistoryAdmin):
    list_display = [
        "filename",
        "file_type",
        "sent_to_riverty_at",
    ]

    ordering = ["-id"]

    list_filter = [
        SentToRivertyFilter,
    ]

    inlines = [
        RivertyCaseInline,
        RivertyCasePaymentInline,
    ]

    exclude = ["file_payload"]
    readonly_fields = ["file_content_pre"]

    actions = [
        "mark_debt_collections_as_sent",
    ]

    actions_row = [
        "download_file",
    ]
    actions_detail = [
        "download_file",
    ]

    @admin.display(description="File payload")
    def file_content_pre(self, obj=None):
        if obj:
            return format_html("<pre>{}</pre>", obj.file_payload)
        return "-"

    @unfold_action(description="Download file payload")
    def download_file(self, request, object_id):
        upload: RivertyFileUpload = RivertyFileUpload.objects.get(id=object_id)
        service = create_riverty_service()
        payload = service.get_encoded_file_content(upload.file_payload)
        filename = upload.filename

        return HttpResponse(
            payload,
            content_type="text/plain",
            headers={"Content-Disposition": f'attachment; charset=windows-1252; filename="{filename}"'},
        )

    @unfold_action(description="Mark debt collections as sent")
    def mark_debt_collections_as_sent(self, request, queryset):
        for upload in queryset:
            upload: RivertyFileUpload
            upload.update_debt_collection_status_of_cases()
        self.message_user(
            request,
            "Uploaded debt collection statuses",
            messages.SUCCESS,
        )

    def get_inlines(self, request, obj: RivertyFileUpload = None):
        if obj is not None:
            if obj.is_collection_file():
                return [RivertyCaseInline]
            elif obj.is_payment_file():
                return [RivertyCasePaymentInline]
        return []


@admin.register(AppAlert, site=unfold_admin_site)
class AppAlertAdmin(ModelAdminBase, SimpleHistoryAdmin, UnfoldModelAdmin):
    formfield_override_by_name = {
        "countries": {"widget": UnfoldSelect2MultipleAdminWidget},
    }

    list_display = [
        "type",
        "active",
    ]


@admin.register(TaskResult, site=unfold_admin_site)
class TaskResultAdmin(DisabledCheckboxDjangoQLSearchMixin, BaseTaskResultAdmin, UnfoldModelAdmin):
    search_fields = BaseTaskResultAdmin.search_fields


@admin.register(GroupResult, site=unfold_admin_site)
class GroupResultAdmin(DisabledCheckboxDjangoQLSearchMixin, BaseGroupResultAdmin, UnfoldModelAdmin):
    search_fields = BaseGroupResultAdmin.search_fields


@admin.register(VATNumberConfig, site=unfold_admin_site)
class VATNumberConfigAdmin(ModelAdminBase, SimpleHistoryAdmin):
    list_display = ["vat_number", "option"]

    search_fields = ["vat_number"]

    list_filter = ["option"]


@admin.register(RecallItemRequest, site=unfold_admin_site)
class RecallItemRequestAdmin(ModelAdminBase, SimpleHistoryAdmin):
    list_display = [
        "customer",
        "vouchers_issued",
        "status",
        "voucher",
        "notes",
        "quantity_requested",
        "quantity_on_nofence_books",
        "quantity_aproved",
        "action_buttons",
    ]

    readonly_fields = ("customer",)
    list_filter = (
        CustomerFilter,
        "status",
    )
    list_select_related = ["customer", "voucher"]

    list_editable = ["notes", "quantity_aproved"]

    search_fields = ["customer__user__email", "customer__user__name"]

    actions = ["reject_request", "issue_vouchers", "send_voucher_issued_email"]

    def action_buttons(self, obj):
        if obj.vouchers_issued:
            return format_html(
                '<span style="backgounrd:gray" class="font-medium rounded-md px-3 py-1 text-black">'
                "Vouchers already issued</span>",
            )

        if obj.quantity_aproved > 0:
            issue_voucher_html = (
                '<a href="{}" class="bg-primary-600 font-medium rounded-md px-3 py-1 text-white"">Issue Voucher</a>'
            ).format(f"issue-voucher/{obj.pk}/")
        else:
            issue_voucher_html = (
                '<span style="background:darkgray; pointer-events:none" '
                'class="font-medium rounded-md px-3 py-1 text-white">Issue Voucher</span>'
            )

        return format_html(
            '<div style="display: flex; gap: 8px;">'
            '<button type="submit" name="_save" '
            'class="bg-primary-600 font-medium rounded-md px-3 py-1 text-white">Save </button>'
            "{}"
            '<a href="{}" class="inline-block bg-red-600 hover:bg-red-700 '
            'text-white font-medium rounded-md px-3 py-1">Reject Request</a>'
            "</div>",
            format_html(issue_voucher_html),  # Ensuring it's correctly interpreted
            f"reject-request/{obj.pk}/",
        )

    def get_urls(self):
        urls = super().get_urls()
        custom_urls = [
            path("issue-voucher/<int:pk>/", self.admin_site.admin_view(self.issue_voucher_view), name="issue_voucher"),
            path(
                "reject-request/<int:pk>/",
                self.admin_site.admin_view(self.reject_request_view),
                name="reject_request",
            ),
        ]
        return custom_urls + urls

    @transaction.atomic
    def issue_voucher_view(self, request, pk):
        obj = get_object_or_404(RecallItemRequest, pk=pk)
        obj.issue_vouchers_and_send_email(RecallItemRequest.Status.COMPLETED)
        return redirect(request.META.get("HTTP_REFERER", ".."))

    @transaction.atomic
    def reject_request_view(self, request, pk):
        obj = get_object_or_404(RecallItemRequest, pk=pk)

        obj.quantity_aproved = 0
        obj.status = RecallItemRequest.Status.REJECTED
        obj.notes += "\nRequest rejected."
        obj.save()

        self.message_user(request, _("Request successfully rejected."), messages.SUCCESS)

        return redirect(request.META.get("HTTP_REFERER", ".."))

    def formfield_for_dbfield(self, db_field, request, **kwargs):
        if db_field.name == "notes":
            kwargs["widget"] = forms.Textarea(attrs={"rows": 4, "cols": 50})
        elif db_field.name == "quantity_aproved":
            kwargs["widget"] = forms.NumberInput(attrs={"min": 0, "step": 1})
        return super().formfield_for_dbfield(db_field, request, **kwargs)

    def reject_request(self, request, queryset):
        count = queryset.update(quantity_aproved=0, status=RecallItemRequest.Status.REJECTED)
        self.message_user(
            request,
            _("%d request(s) were successfully rejected.") % count,
            messages.SUCCESS,
        )

    reject_request.short_description = "Reject selected requests"

    @transaction.atomic
    def issue_vouchers(self, request, queryset):
        count = 0
        for item in queryset:
            item: RecallItemRequest
            if item.issue_vouchers_and_send_email(RecallItemRequest.Status.COMPLETED):
                count += 1
        self.message_user(
            request,
            _("%d request(s) successfully issued with vouchers.") % count,
            messages.SUCCESS,
        )

    issue_vouchers.short_description = "Issue vouchers for selected requests"


def make_csv_export_action(model):
    action = csv_export.streaming_csv_export_action(
        f"Export {model.__name__} items as CSV",
        fields=["all"],
        filename=f"export_{model.__name__}.csv",
    )
    action.__name__ = f"csv_export_{model.__name__.lower()}"
    action.short_description = f"Export selected {model.__name__} rows to CSV"
    return action


for model, model_admin in unfold_admin_site._registry.items():
    model_admin.actions = list(model_admin.actions) + [
        make_csv_export_action(model),
    ]


class FinancingPartnerCompanyAddressAdmin(TabularInlineBase):
    model = FinancingPartnerCompanyAddress
    extra = 0
    max_num = 0
    can_delete = False


class FinancingPartnerInvoiceAddressAdmin(TabularInlineBase):
    model = FinancingPartnerInvoiceAddress
    extra = 0
    max_num = 0
    can_delete = False


class FinancingPartnerDeliveryAddressAdmin(TabularInlineBase):
    model = FinancingPartnerDeliveryAddress
    extra = 0
    max_num = 0
    can_delete = False


@admin.register(FinancingPartner, site=unfold_admin_site)
class FinancingPartnerAdmin(SimpleHistoryAdmin, UnfoldModelAdmin):
    list_display = [
        "name",
        "active",
        "nofence_company",
        "discount_code",
    ]

    inlines = [
        FinancingPartnerCompanyAddressAdmin,
        FinancingPartnerInvoiceAddressAdmin,
        FinancingPartnerDeliveryAddressAdmin,
    ]
