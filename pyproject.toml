[project]
name = "billy"
version = "0.1.0"
description = "The Nofence Backend"
readme = "README.md"
requires-python = ">=3.12"
dependencies = [
    "amqp~=5.2",
    "asgiref~=3.8",
    "authlib~=1.3",
    "billiard~=4.2",
    "boto3~=1.34",
    "botocore~=1.34",
    "celery-progress~=0.5",
    "celery~=5.3",
    "certifi~=2024.8",
    "chardet~=5.2",
    "click-didyoumean~=0.3",
    "click-plugins~=1.1",
    "click-repl~=0.3",
    "click~=8.1",
    "clickhouse-connect==0.8.14",
    "dataclasses-json~=0.5",
    "django-add-default-value~=0.10",
    "django-admin-autocomplete-filter~=0.7",
    "django-admin-inline-paginator~=0.4",
    "django-admin-rangefilter~=0.13",
    "django-celery-beat~=2.8",
    "django-celery-results~=2.6",
    "django-compressor~=4.5",
    "django-cors-headers~=4.7",
    "django-countries~=7.6",
    "django-debug-toolbar~=5.2",
    "django-deprecate-fields~=0.2",
    "django-environ~=0.12",
    "django-extensions~=4.1",
    "django-guid~=3.4",
    "django-htmx~=1.23",
    "django-impersonate~=1.9",
    "django-localflavor~=4.0",
    "django-model-utils~=5.0",
    "django-modeltranslation~=0.18",
    "django-money~=3.5",
    "django-nested-inline~=0.4",
    "django-push-notifications~=3.2",
    "django-qr-code~=4.2",
    "django-rest-framework-mongoengine~=3.4",
    "django-sass~=1.1",
    "django-sendgrid-v5~=1.3",
    "phonenumbers~=8.13",
    "django-sequences~=3.0",
    "django-simple-history~=3.10",
    "django-sql-explorer~=5.0",
    "django-storages~=1.14",
    "django-timezone-field~=7.1",
    "django-tinymce~=4.1",
    "django-treebeard~=4.7",
    "django-unfold==0.49.1",
    "django-widget-tweaks~=1.5",
    "django~=5.1",
    "djangoql~=0.18",
    "djangorestframework-camel-case~=1.4",
    "djangorestframework~=3.16",
    "firebase-admin~=6.5",
    "geoip2~=4.8",
    "gocardless-pro~=1.49",
    "google-api-core~=2.21",
    "google-cloud-firestore~=2.19",
    "hubspot-api-client~=6.1.0",
    "idna~=3.10",
    "isort~=5.13",
    "jinja2~=3.1",
    "jira~=3.8",
    "jmespath~=1.0",
    "kombu~=5.3",
    "libsass~=0.23",
    "marshmallow-dataclass==8.5.3",
    "marshmallow==3.15.0",
    "mjml-python~=1.3",
    "mongoengine~=0.28",
    "natsort~=8.4",
    "numpy==1.26.4",
    "pdpyras~=5.2",
    "phone-iso3166~=0.4",
    "polib~=1.2",
    "prompt-toolkit~=3.0",
    "prometheus-client~=0.20",
    "protobuf==5.27.5",
    "psycopg2-binary~=2.9",
    "pycurl~=7.45",
    "pygments~=2.17",
    "pymongo~=4.6",
    "python-barcode~=0.15",
    "python-crontab~=3.0",
    "python-dateutil~=2.9",
    "python-json-logger~=2.0",
    "python-stdnum~=1.20",
    "pytz~=2024.1",
    "requests-oauthlib~=2.0",
    "requests~=2.32",
    "s3transfer~=0.10",
    "sendgrid~=6.11",
    "shortuuid~=1.0",
    "six~=1.16",
    "slack-sdk~=3.27",
    "sqlparse~=0.5",
    "stackprinter~=0.2",
    "starkbank-ecdsa~=2.2",
    "stripe~=10.0",
    "timezonefinder~=6.5",
    "typing-inspect==0.9.0",
    "urllib3~=2.2",
    "vine~=5.1",
    "wagtail~=7.0",
    "wcwidth~=0.2",
    "weasyprint==52",
    "whitenoise~=6.9",
    "xero-python~=2.0",
    "pandas~=2.2",
    "scipy~=1.14",
    "paramiko>=3.5.0",
    "ddtrace~=3.10",
    "shapely>=2.0.6",
    "pyproj>=3.7.0",
    "polars>=1.20.0",
    "pyarrow>=12.0.0",
    "django-more-admin-filters~=1.13",
    "django-advanced-filters>=2.0.0",
    "orjson~=3.10",
    "django-select2>=8.4.0",
    "setuptools==78.0.2",
    "playwright>=1.51.0",
    "django-admin-sortable2>=2.2.8",
    "pgvector>=0.4.1",
    "langchain-community>=0.3.21",
    "llama-index>=0.12.42",
    "llama-index-core>=0.12.42",
    "llama-index-embeddings-openai>=0.3.1",
    "llama-index-llms-openai>=0.4.7",
    "openai>=1.93.0",
    "pyjwt>=2.8.0",
    "apns2",
    "firebase-admin>=6.8.0",
]

[tool.black]
line-length = 119

[tool.ruff]
line-length = 119

extend-exclude = ["core/migrations", "clickhouse/migrations"]

[tool.uv]
dev-dependencies = [
    "black~=24.10",
    "behave~=1.2",
    "darker~=2.1",
    "deepl==1.17.0",
    "factory-boy~=3.3",
    "flake8~=7.0",
    "freezegun~=1.4",
    "mongomock~=4.1",
    "pre-commit~=3.5",
    "pytest-bdd~=8.1.0",
    "pytest-cov~=5.0",
    "pytest-django~=4.8",
    "pytest-dotenv==0.5.2",
    "pytest-factoryboy~=2.7",
    "pytest-mock~=3.14",
    "pytest-xdist~=3.5",
    "pytest~=8.2",
    "redis~=5.0",
    "requests-mock~=1.12",
    "sttable~=0.0",
    "time-machine~=2.14",
    "ruff==0.11.7",
]

[tool.uv.sources]
apns2 = { git = "https://github.com/Pr0Ger/PyAPNs2", rev = "aac4bd3494670b8090774cb051798cfac5e0ed6a" }
