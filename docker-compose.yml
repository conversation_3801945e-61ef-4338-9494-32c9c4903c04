version: '3.7'

services:
  postgres:
    image: pgvector/pgvector:pg14
    ports:
      - "5432:5432"
    volumes:
       - postgres-data:/var/lib/postgresql/data/
    environment:
      - POSTGRES_USER=billy
      - POSTGRES_PASSWORD=billy
      - POSTGRES_DB=billy
    profiles:
      - databases
      - all

  mongodb:
    image: mongo:7.0.16
    ports:
      - "27017:27017"
    volumes:
      - mongodb-data:/data/db
    profiles:
      - databases
      - all

  redis:
    image: redis:latest
    ports:
      - "6379:6379"
    profiles:
      - celery
      - all

  billy:
    image: 290121948369.dkr.ecr.eu-central-1.amazonaws.com/nofence:billy-latest
    ports:
      - "8000:8000"
    command: gunicorn --bind :8000 --workers=1 --threads=3 billy.wsgi:application -c gunicorn-config.py
    env_file: ".env-docker"
    logging:
      driver: "local"
      options:
          max-size: "10m"
          max-file: "10"
    labels:
      - vector_logs=collect
    depends_on:
      - postgres
    links:
      - postgres
    profiles:
      - all

  celery_worker_default:
    image: 290121948369.dkr.ecr.eu-central-1.amazonaws.com/nofence:billy-latest
    container_name: celery_worker_default
    command: celery -A billy worker -Q celery -l INFO --max-memory-per-child=1000000 --concurrency=2 --prefetch-multiplier=1 -n default-worker.%h
    env_file: ".env-docker"
    depends_on:
      - redis
      - postgres
    profiles:
      - celery
      - all

  celery_worker_notifications:
    image: 290121948369.dkr.ecr.eu-central-1.amazonaws.com/nofence:billy-latest
    container_name: celery_worker_notifications
    command: celery -A billy worker -Q notifications -l INFO --max-memory-per-child=1000000 --concurrency=2 --prefetch-multiplier=1 -n notifications-worker.%h
    env_file: ".env-docker"
    depends_on:
      - redis
      - postgres
    profiles:
      - celery
      - all

  celery_beat:
    image: 290121948369.dkr.ecr.eu-central-1.amazonaws.com/nofence:billy-latest
    container_name: celery_beat
    command: celery -A billy beat -l INFO --scheduler django_celery_beat.schedulers:DatabaseScheduler
    env_file: ".env-docker"
    depends_on:
      - redis
      - postgres
    profiles:
      - celery
      - all

  clickhouse:
    image: ghcr.io/nofenceas/clickhouse:25.3
    platform: linux/amd64
    container_name: clickhouse
    ports:
      - "9000:9000"
      - "8123:8123"

  vector:
    image: 290121948369.dkr.ecr.eu-central-1.amazonaws.com/nofence:vector-agent-latest
    container_name: vector
    depends_on:
      - postgres
    profiles:
      - all
    volumes:
      - /var/run/docker.sock:/var/run/docker.sock:ro
      - /var/lib/docker/containers:/var/lib/docker/containers:ro
    ports:
      - "8686:8686"
    restart: always
    healthcheck:
      test: [ "CMD-SHELL", "curl -sSf http://localhost:8686/health" ]
      interval: 5s
      timeout: 2s
      retries: 3
      start_period: 2s

volumes:
  postgres-data:
  mongodb-data:
