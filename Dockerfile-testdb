FROM pgvector/pgvector:pg13 AS dumper

# Create a template database using the schema.sql that is expected to reside in the Docker context. See the
# build-postgres-testdb github workflow for the details, but the gist of it is that we run all migrations against a
# clean DB and then pg_dump the result.
ARG POSTGRES_USER
ARG POSTGRES_PASSWORD
ARG POSTGRES_DB
ENV POSTGRES_USER=$POSTGRES_USER
ENV POSTGRES_PASSWORD=$POSTGRES_PASSWORD
ENV POSTGRES_DB=$POSTGRES_DB
ENV PGDATA=/data
COPY schema.sql /docker-entrypoint-initdb.d/

# This little trick ensures that the postgres process that gets started by the docker-entrypoint.sh script isn't
# daemonized so that we wait for it to start instead...
RUN ["sed", "-i", "s/exec \"$@\"/echo \"skipping...\"/", "/usr/local/bin/docker-entrypoint.sh"]

RUN ["/usr/local/bin/docker-entrypoint.sh", "postgres"]

# Now we can create the final image which is just a stock postgres image with the "billy" database pre-created.
FROM pgvector/pgvector:pg13
COPY --from=dumper /data $PGDATA
